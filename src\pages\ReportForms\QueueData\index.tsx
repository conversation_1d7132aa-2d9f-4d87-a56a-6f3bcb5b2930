import { getDoctorConsultationData } from '@/services/api/queue';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProTable,
} from '@ant-design/pro-components';
import { Button, message} from 'antd';
import '@umijs/max';
import React, { useRef } from 'react';
import { parseParam } from '@/utils';
import defaultSettings from '../../../../config/defaultSettings';

/**
 * 导出
 * @param fields
 */

const handleExportToExcel = async (fields: any) => {
  console.log(fields);
  const hide = message.loading('正在导出');
  try {
    const paramsStr = parseParam(fields);
    console.log(paramsStr);
    const aLink = document.createElement('a');
    document.body.appendChild(aLink);
    aLink.style.display = 'none';
    aLink.href = `/${defaultSettings.queueApiName}/statement/exportDoctorConsultationDataExcel?${paramsStr}`;
    aLink.setAttribute('download', '挂号数据');
    aLink.click();
    document.body.removeChild(aLink);
    hide();
    return true;
  } catch (error) {
    hide();
    message.error('导出失败请重试！');
    return false;
  }
};

const TableList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const columns: ProColumns<QUEUEDATA.QueueDataListItem>[] = [
    {
      title: '统计日期',
      dataIndex: 'reportDateStr',
      width: 80,
      valueType: 'date',
      search: {
        transform: (value) => {
          return {
            startDate: value,
            endDate: value,
          };
        },
      },
    },
    {
      title: '科室名称',
      width: 80,
      dataIndex: 'deptName',
    },
    {
      title: '医生工号',
      width: 80,
      dataIndex: 'doctorCode',
      render: (_) => <a>{_}</a>,
    },
    {
      title: '医生姓名',
      dataIndex: 'doctorName',
      width: 120,
    },
    {
      title: '候诊中',
      width: 60,
      dataIndex: 'hzz',
      hideInSearch: true
    },
    {
      title: '已就诊',
      width: 60,
      dataIndex: 'yjz',
      hideInSearch: true
    },
    {
      title: '初诊',
      width: 60,
      dataIndex: 'czrs',
      hideInSearch: true
    },
    {
      title: '总就诊',
      width: 60,
      dataIndex: 'zjj',
      hideInSearch: true
    },
    {
      title: '过号',
      width: 60,
      dataIndex: 'ygh',
      hideInSearch: true
    },
    {
      title: '回诊',
      width: 60,
      dataIndex: 'hz',
      hideInSearch: true
    },
    {
      title: '平均就诊时长（分）',
      width: 100,
      dataIndex: 'avgVisitsTime',
      hideInSearch: true
    },
    {
      title: '平均等待时长（分）',
      width: 100,
      dataIndex: 'avgWaitTime',
      hideInSearch: true
    },
    {
      title: '平均在院时间（分）',
      width: 100,
      dataIndex: 'avgInHospTime',
      hideInSearch: true
    }
  ];
  return (
    <PageContainer>
      <ProTable<QUEUEDATA.QueueDataListItem, QUEUEDATA.PageParams>
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="id"
        // search={{
        //   labelWidth: 120,
        // }}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          optionRender: (searchConfig, formProps, dom) => [
            ...dom.reverse(),
            <Button key="out" onClick={() => {
              const values = searchConfig?.form?.getFieldsValue();
              handleExportToExcel(values);
            }}>
              导出
            </Button>,
          ],
        }}
        toolBarRender={() => []}
        request={getDoctorConsultationData}
        columns={columns}
      />
    </PageContainer>
  );
};
export default TableList;
