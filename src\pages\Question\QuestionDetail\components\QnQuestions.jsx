/*
    问卷标题
*/

import React, { useContext, useMemo } from "react";
import { Input, Radio, Checkbox, Button } from "antd";
import { MinusCircleOutlined } from '@ant-design/icons';
import { QuestionFuncContext } from './CardIndex';

const { TextArea } = Input;

const QustionButtons=({
    questionIndex, 
    array, 
    question,
})=>{
    const {
        handleShiftQuestion,
        handleCopyQuestion,
        handleRemoveQuestion,
    } = useContext(QuestionFuncContext);

    return (
        <div>
            <p style={{ float: 'right' }}>
                {questionIndex === 0 ? (
                    null
                ) : (
                    <Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => handleShiftQuestion(questionIndex, -1)}>上移</Button>
                )}
                {questionIndex === array.length - 1 ? (
                    null
                ) : (
                    <Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => handleShiftQuestion(questionIndex, 1)}>下移</Button>
                )}
                <Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => handleCopyQuestion(questionIndex, question.id || null)}>复用</Button>
                <Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => handleRemoveQuestion(questionIndex, question.id || null)}>删除</Button>
                {/* <Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => this.handleOpenTopicLogic(questionIndex, question)} >跳题逻辑</Button> */}
            </p>
        </div>
    );
}

const RadioItem = ({
    question,
    questionIndex,
    array,
})=> {
    const {
        handleQuestionChange,
        handleOptionChange,
        handleRemoveOption,
        handleAddOption,
        handleTextRequire,
    } = useContext(QuestionFuncContext);

    return (
        question.state === 9 ||  question.state === "9" ? "" : 
        <div className="questionsWrap" style={{ padding: 30 }} key={questionIndex} id={"anchorID" + questionIndex}>
            <span>Q{questionIndex + 1}</span>
            <Input className="refinput" value={question.title} style={{ borderStyle: 'none', width: '97%', marginLeft: 3 }} onChange={(e) => handleQuestionChange(e, questionIndex)} />
            <div style={{ margin: "16px 20px" }}>
                {question.options && question.options.map((option, optionIndex) => {
                    return (
                        option.state === 9 || option.state === "9" ? "" : 
                        <div style={{ margin: '8px 0', display: 'flex', alignItems: 'center' }} key={optionIndex}>
                            {/* <Icon type="close" className="deleteOption" style={{ display: 'inline-block', marginRight: 8 }} onClick={() => this.handleRemoveOption(questionIndex, optionIndex)} /> */}
                            <Input
                                value={option.text}
                                style={{ width: '30%', borderStyle: 'none', marginRight: 10 }}
                                prefix={<Radio disabled></Radio>}
                                onChange={(e) => handleOptionChange(e, questionIndex, optionIndex)}
                            />
                            {/* <span className="icon-btns"><PlusCircleOutlined /></span> */}
                            <span className="icon-btns" onClick={() => handleRemoveOption(questionIndex, optionIndex, option.id || null)}><MinusCircleOutlined /></span>
                        </div>
                    );
                })}
            </div>
            <div className="addOption" style={{ width: '20%', height: 28, margin: '8px 20px' }} onClick={() => handleAddOption(questionIndex)} />
            <Checkbox checked={question.required} style={{ margin: '0 20px' }} onChange={(e) => handleTextRequire(e, questionIndex)}>此题是否必填</Checkbox>
            <QustionButtons
                questionIndex={questionIndex}
                array={array} 
                question={question}
            />
        </div>
    )
}

const CheckboxItem = ({
    question,
    questionIndex,
    array,
})=>{
    const {
        handleQuestionChange,
        handleOptionChange,
        handleRemoveOption,
        handleAddOption,
        handleTextRequire,
    } = useContext(QuestionFuncContext);

    return (
        question.state === 9 ||  question.state === "9" ? "" : 
        <div className="questionsWrap" style={{ padding: 30 }} key={questionIndex} id={"anchorID" + questionIndex}>
            <span>Q{questionIndex + 1}</span>
            <Input className="refinput" value={question.title} style={{ borderStyle: 'none', width: '97%', marginLeft: 3 }} onChange={(e) => handleQuestionChange(e, questionIndex)} />
            <div style={{ margin: "16px 20px" }}>
                {question.options && question.options.map((option, optionIndex) => {
                    return (
                        option.state === 9 || option.state === "9" ? "" : 
                        <div style={{ margin: '8px 0', display: 'flex', alignItems: 'center' }} key={optionIndex}>
                            <Input 
                                value={option.text} 
                                style={{ borderStyle: 'none', width: '30%', marginRight: 10 }} 
                                prefix={<Checkbox disabled></Checkbox>}
                                onChange={(e) => handleOptionChange(e, questionIndex, optionIndex)} 
                            />
                            <span className="icon-btns" onClick={()=>handleRemoveOption(questionIndex, optionIndex, option.id || null)}><MinusCircleOutlined /></span>
                        </div>
                    );
                })}
            </div>
            <div className="addOption" style={{ width: '20%', height: 28, margin: '8px 20px' }} onClick={() => handleAddOption(questionIndex)} />
            <Checkbox checked={question.required} style={{ margin: '0 20px' }} onChange={(e) => handleTextRequire(e, questionIndex)}>此题是否必填</Checkbox>
            {/* {this.getQuestionOperator(questionIndex, array, question)} */}
            <QustionButtons
                questionIndex={questionIndex}
                array={array} 
                question={question}
            />
        </div>
    );
}

const TextAreaItem=({
    question,
    questionIndex,
    array,
})=>{
    const {
        handleQuestionChange,
        handleTextChange,
        handleTextRequire,
    } = useContext(QuestionFuncContext);

    return (
        question.state === 9 || question.state === "9" ? "" : 
        <div className="questionsWrap" style={{ padding: 30 }} key={questionIndex} id={"anchorID" + questionIndex}>
            <span>Q{questionIndex + 1}</span>
            <Input className="refinput" value={question.title} style={{ borderStyle: 'none', width: '97%', marginLeft: 3 }} onChange={(e) => handleQuestionChange(e, questionIndex)} />
            <div style={{ margin: '16px 20px' }}>
                <TextArea rows={5} value={question.text} onChange={(e) => handleTextChange(e, questionIndex)} disabled/>
            </div>
            <Checkbox checked={question.required} style={{ margin: '0 20px' }} onChange={(e) => handleTextRequire(e, questionIndex)}>此题是否必填</Checkbox>
            {/* {this.getQuestionOperator(questionIndex, array, question)} */}
            <QustionButtons
                questionIndex={questionIndex}
                array={array} 
                question={question}
            />
        </div>
    );
}

const InputItem=({
    question,
    questionIndex,
    array,
})=>{
    const {
        handleQuestionChange,
        handleTextChange,
        handleTextRequire,
    } = useContext(QuestionFuncContext);

    return (
        question.state === 9 || question.state === "9" ? "" : 
        <div className="questionsWrap" style={{ padding: 30 }} key={questionIndex} id={"anchorID" + questionIndex}>
            <span>Q{questionIndex + 1}</span>
            <Input className="refinput" value={question.title} style={{ borderStyle: 'none', width: '97%', marginLeft: 3 }} onChange={(e) => handleQuestionChange(e, questionIndex)} />
            <div style={{ margin: '16px 20px' }}>
                <Input value={question.text} onChange={(e) => handleTextChange(e, questionIndex)} disabled/>
            </div>
            <Checkbox checked={question.required} style={{ margin: '0 20px' }} onChange={(e) => handleTextRequire(e, questionIndex)}>此题是否必填</Checkbox>
            {/* {this.getQuestionOperator(questionIndex, array, question)} */}
            <QustionButtons
                questionIndex={questionIndex}
                array={array} 
                question={question}
            />
        </div>
    );
}

const QnQuestions = ({
    questions,
})=>{
    // console.log(questions)
    return questions && questions.map((question, questionIndex, array) => {
        return (
            question.type === 1 ? 
            <InputItem 
                question={question} 
                questionIndex={questionIndex} 
                array={array}
            />:
            question.type === 2 ? 
            <CheckboxItem 
                question={question} 
                questionIndex={questionIndex} 
                array={array}
            />:
            question.type === 3 ? 
            <RadioItem 
                question={question} 
                questionIndex={questionIndex} 
                array={array}
            />:
            question.type === 4 ? 
            <TextAreaItem
                question={question} 
                questionIndex={questionIndex} 
                array={array}
            />:
            <></>
        )
    })
}


export default QnQuestions