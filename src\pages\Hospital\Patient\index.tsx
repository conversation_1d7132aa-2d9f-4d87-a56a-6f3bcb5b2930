import { Drawer, Select } from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryPatientList } from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

const TableList = () => {
  /** 展示患者详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<PATIENT.PatientListItem>();
  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);

  const columns: ProColumns<PATIENT.PatientListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select {...rest} placeholder="请选择">
            {hosList &&
              hosList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '病人编号',
      dataIndex: 'patientID',
      key: 'patientID',
    },
    {
      title: '病人姓名',
      dataIndex: 'patientName',
      key: 'patientName'
    },
    {
      title: '身份证号',
      dataIndex: 'idCard',
      key: 'idCard',
    },
    {
      title: '门诊号码',
      dataIndex: 'clinicID',
      key: 'clinicID',
      hideInSearch: true,
    },
    {
      title: '医保卡号',
      dataIndex: 'medicalCard',
      key: 'medicalCard',
      hideInSearch: true,
    },
    {
      title: '性别',
      dataIndex: 'patientGender',
      key:'patientGender',
      valueEnum: {
        0: {
          text: '未知',
        },
        1: {
          text: '男',
        },
        2: {
          text: '女',
        },
      },
      hideInSearch: true,
    },
    {
      title: '出生日期',
      dataIndex: 'birthDate',
      key: 'birthDate',
      hideInSearch: true,
    },
    {
      title: '市民卡号',
      dataIndex: 'patientCard',
      key: 'patientCard'
    },
    {
      title: '电话号码',
      dataIndex: 'patientPhone',
      key: 'patientPhone',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="ID"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => []}
        request={queryPatientList}
        columns={columns}
      />
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.patientName && (
          <ProDescriptions
            column={1}
            title={currentRow?.patientName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.ID,
            }}
            columns={columns as ProDescriptionsItemProps<PATIENT.PatientListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
