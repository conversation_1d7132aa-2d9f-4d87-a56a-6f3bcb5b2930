import {useLayoutEffect, useState} from 'react';
import {
  RetweetOutlined,
  InfoCircleOutlined,
  CheckOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { Button, Form, Input, Table,  Divider, Tag, Modal, Space, message, Segmented} from 'antd';
import { useAntdTable } from 'ahooks';
import MoveToTop from './components/MoveToTop';
import MoveToDoc from './components/MoveToDoc';
import MoveToDept from './components/MoveToDept';
import Patient from './components/Patient';
import SignIn from './components/SignIn';
import { 
  getPatientCheckinList, 
  movePatient, 
  moveToDoctor, 
  patientCheckIn, 
  updatePatientState, 
  patientUpAndDown, 
  patientDeptReferral, 
  getDeptLoginDoctor, 
  getDigDeptDocList
} from '@/services/api/queue';
import styles from '../../index.less';
import { remove, } from 'lodash';
import moment from 'moment';

interface Result {
  total: number;
  list: Object[];
}

/**
 * 查询科室医生是否在线
 * @param fields
 */
const handleDeptLoginDoctor = async fields => {
  const hide = message.loading('查询原科室医生中...');
  try {
    const response = await getDeptLoginDoctor({
      ...fields
    });
    hide();
    if (response.code === '0') {
      return response.data;
    }
    return false;
  } catch (error) {
    hide();
    message.error('查询医生失败请重试！');
    return false;
  }
};

/**
 * 查询诊区医生是否有在线的
 * @param fields
 */
const handleDigDeptDocList = async fields => {
  const hide = message.loading('查询诊区内在线医生中...');
  try {
    const response = await getDigDeptDocList({
      ...fields
    });
    hide();
    if (response.code === '0' && response.data.length > 0) {
      return response.data;
    }
    return false;
  } catch (error) {
    hide();
    message.error('查询失败请重试！');
    return false;
  }
};

/**
 * 患者置顶
 * @param fields
 */
const handleMovePatient = async fields => {
  const hide = message.loading('请求中...');
  try {
    const response = await movePatient({
      patientIDs: fields.patientID ?? fields.patientIDs.join(','),
      doctorCode: fields.doctorCode,
      patientMoveType: fields.patientMoveType,
      deptCode: fields.deptCode
    });
    hide();
    if (response.code === '0') {
      message.success('操作成功，请等待列表刷新');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('操作失败请重试！');
    return false;
  }
};

/**
 * 患者指定医生
 * @param fields
 */
const handleMoveToDoctor = async fields => {
  const hide = message.loading('请求中...');
  try {
    const response = await moveToDoctor({
      patientIDs: fields.patientID ?? fields.patientIDs.join(','),
      doctorCode: fields.newDoctorCode,
      deptCode: fields.deptCode
    });
    hide();
    if (response.code === '0') {
      message.success('操作成功，请等待列表刷新');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('操作失败请重试！');
    return false;
  }
};

/**
 * 患者转移科室
 * @param fields
 */
const handleMoveToDept = async fields => {
  const hide = message.loading('请求中...');
  try {
    console.log(fields)
    const response = await patientDeptReferral({
      checkCodes: fields.checkCode ?? fields.checkCodes.join(','),
      patientIDs: fields.patientID ?? fields.patientIDs.join(','),
      transferDeptCode: fields.transferDeptCode,
      deptCode: fields.deptCode,
      newDoctorCode: fields.newDoctorCode
    });
    hide();
    if (response.code === '0') {
      message.success('操作成功，请等待列表刷新');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('操作失败请重试！');
    return false;
  }
};

/**
 * 查询患者
 * @param {*} fields 
 * @returns 
 */
const handlePatientSearch = async fields => {
  const hide = message.loading('请求中...');
  try {
    const response = await getPatientCheckinList({
      current: 1,
      pageSize: 10,
      ...fields
    });
    hide();
    if (response.code === '0') {
      return response.data;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('患者签到失败请重试！');
    return false;
  }
};

/**
 * 普通签到
 * @param {*} fields 
 * @returns 
 */
const handlePatientCheck = async fields => {
  const hide = message.loading('请求中...');
  try {
    const response = await patientCheckIn({
      doctorCode: fields.doctorCode,
      deptCode: fields.deptCode,
      patientID: fields.patientID,
      registerType: 0,
    });
    hide();
    if (response.code === '0') {
      message.success('患者签到成功');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('患者签到失败请重试！');
    return false;
  }
};

/**
 * 回诊签到
 * @param fields
 */
const handlePatientBack = async fields => {
  const hide = message.loading('请求中...');
  try {
    const response = await patientCheckIn({
      deptCode: fields.deptCode,
      doctorCode: fields.doctorCode,
      patientID: fields.patientID,
      tarDeptCode: fields.tarDeptCode,
      tarDoctorCode: fields.tarDoctorCode,
      checkCode: fields.checkCode,
      registerType: 1,
    });
    hide();
    if (response.code === '0') {
      message.success('回诊签到成功');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('回诊签到失败请重试！');
    return false;
  }
};

/**
 * 过号签到
 * @param fields
 */
const handlePatientDelay = async fields => {
  const hide = message.loading('请求中...');
  try {
    const response = await patientCheckIn({
      deptCode: fields.deptCode,
      doctorCode: fields.doctorCode,
      patientID: fields.patientID,
      tarDeptCode: fields.tarDeptCode,
      tarDoctorCode: fields.tarDoctorCode,
      checkCode: fields.checkCode,
      registerType: 2,
    });
    hide();
    if (response.code === '0') {
      message.success('过号签到成功');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('过号签到失败请重试！');
    return false;
  }
};

/**
 * 完成就诊
 * @param fields
 */
const handlePatientFinish = async fields => {
  const hide = message.loading('请求中...');
  try {
    const response = await updatePatientState({
      deptCode: fields.deptCode,
      patientIDs: fields.patientIDs,
      state: 3,    // 3 完成 4 过号
    });
    hide();
    if (response.code === '0') {
      message.success('状态更新成功');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('更新失败请重试！');
    return false;
  }
};

/**
 * 上一位
 * @param fields
 */
const handlePatientUp = async fields => {
  const hide = message.loading('请求中...');
  try {
    const response = await patientUpAndDown({
      deptCode: fields.deptCode,
      patientID: fields.patientID,
      moveType: 1,
    });
    hide();
    if (response.code === '0') {
      message.success('调整上移一位成功');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('调整上移一位失败请重试！');
    return false;
  }
}

/**
 * 下一位
 * @param fields
 */
const handlePatientDown = async fields => {
  const hide = message.loading('请求中...');
  try {
    const response = await patientUpAndDown({
      deptCode: fields.deptCode,
      patientID: fields.patientID,
      moveType: 2,
    });
    hide();
    if (response.code === '0') {
      message.success('调整下移一位成功');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('调整下移一位失败请重试！');
    return false;
  }
}

const getTableData = ({ current, pageSize, sorter, filters }, formData: Object, customParams: Object, stateType: string): Promise<Result> => {
  switch (stateType) {
    case '0':
      customParams['registerType'] = ''
      customParams['state'] = ''
    break;
    case '3':
      customParams['registerType'] = ''
      customParams['state'] = '6'
    break;
    case '4':
      customParams['registerType'] = ''
      customParams['state'] = '1'
    break;
    case '1':
      customParams['registerType'] = stateType
      customParams['state'] = ''
    break;
    case '2':
      customParams['registerType'] = ''
      customParams['state'] = '4'
    break;
    case '5':
      customParams['registerType'] = ''
      customParams['state'] = '3'  
    break;
  }
  if(formData){
    Object.entries(formData).forEach(([key, value]) => {
      if (value) {
        return {
          key: value
        }
      }
    });
  }
  if(customParams){
    Object.entries(customParams).forEach(([key, value]) => {
      if (value && value !== '') {
        return {
          key: value
        }
      }
    });
  }


  return getPatientCheckinList({
    current,
    pageSize,
    ...formData,
    ...customParams
  })
    .then((res) => ({
      total: res.total,
      list: res.data,
    }));
};

const stateColor = ['cyan', 'blue', 'green', 'grey', 'orange', 'pink'];

const DeptTable = ({
  digAreaCode,
  code,
  customParams
}) => {
  const [form] = Form.useForm();
  const [checkedData, setCheckedData] = useState<any>([]); // 选中的行数据
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]); // 选中的行id
  const [moveToDocModalVisible, handleMoveToDocModalVisible] = useState(false);
  const [moveToTopModalVisible, handleMoveToTopModalVisible] = useState(false);
  const [moveToDeptModalVisible, handleMoveToDeptModalVisible] = useState(false);
  const [signInModalVisible, handleSignInModalVisible] = useState(false);  
  const [patientSearchModalVisible, handlePatientSearchModalVisible] = useState(false);
  const [patientSearchList, setPatientSearchList] = useState(false);
  const [moveTopMode, setMoveTopMode] = useState('single');  // single 单个 || mutil 多个
  const [moveDocMode, setMoveDocMode] = useState('single');  // single 单个 || mutil 多个
  const [moveDeptMode, setMoveDeptMode] = useState('single');  // single 单个 || mutil 多个
  const [selectPatient, setSelectPatient] = useState<Object>({});
  const [searchValue, setSearchValue] = useState('');
  const [stateType, setStateType] = useState<string>();
  const [doctorSignInList, setDoctorSignInList] = useState([]);
  const [doctorSignInMode, setDoctorSignInMode] = useState<string>();

  const { tableProps, search, run, cancel} = useAntdTable( (pageData, formData) => getTableData(pageData, formData, customParams, stateType), {
    defaultPageSize: 50,
    defaultType: 'advance',
    form,
    refreshDeps: [code, stateType],
    pollingInterval: 5000,
    pollingWhenHidden: true
  });

  useLayoutEffect(() => {
    cancelSelect();
  }, [code])

  // useLayoutEffect(() => {
  //   console.log(selectedRowKeys)
  //   console.log(checkedData)
  // }, [selectedRowKeys, checkedData])

  const { type, changeType, submit, reset } = search;


  // 处理回诊逻辑
  const handleBack = async (record) =>{
    // 判断医生是否在线
    const deptDocList = await handleDeptLoginDoctor({
      deptCode: record.deptCode
    })
    if(!deptDocList){
      // 当前科室无登录医生，找寻诊区医生签到
      const digDocList = await handleDigDeptDocList({
        deptCode: record.deptCode,
        digAreaCode,
        patientID: record.patientID
      })
      // 诊区无医生登录，返回提示，无法签到
      if(!digDocList) return message.error('签到失败, 无医生可签到！');
      setDoctorSignInList(digDocList)
      handleSignInModalVisible(true)
      setDoctorSignInMode('back')
      return
    }
    // 当前科室有登录医生，先判断原医生是否在线，
    const doctor = deptDocList.find(item => item.doctorCode === record.doctorCode)
    if(!doctor){
      // 医生不在线，选择科室内其他医生签到
      setDoctorSignInList(deptDocList)
      handleSignInModalVisible(true)
      setDoctorSignInMode('back')
    }else{
      // 医生在线，确认为患者签到
      Modal.confirm({
        title: '确认回诊签到',
        icon: <InfoCircleOutlined />,
        content: <span>是否确认为<b>{record.patientName}</b>回诊签到<b>{record.doctorName}</b>医生?</span>,
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const success = await handlePatientBack({
            ...record,
            tarDeptCode: record.deptCode,
            tarDoctorCode: record.doctorCode,
          });
          if (success) {
            setSelectPatient({})
            reset();
          }
        }
      });
    }
  }

    // 处理过号逻辑
  const handleDelay = async (record) =>{
    // 判断医生是否在线
    const deptDocList = await handleDeptLoginDoctor({
      deptCode: record.deptCode
    })
    if(!deptDocList){
      // 当前科室无登录医生，找寻诊区医生签到
      const digDocList = await handleDigDeptDocList({
        deptCode: record.deptCode,
        digAreaCode,
        patientID: record.patientID
      })
      // 诊区无医生登录，返回提示，无法签到
      if(!digDocList) return message.error('签到失败, 无医生可签到！');
      setDoctorSignInList(digDocList)
      handleSignInModalVisible(true)
      setDoctorSignInMode('delay')
      return
    }
    // 当前科室有登录医生，先判断原医生是否在线，
    const doctor = deptDocList.find(item => item.doctorCode === record.doctorCode)
    if(!doctor){
      // 医生不在线，选择科室内其他医生签到
      setDoctorSignInList(deptDocList)
      handleSignInModalVisible(true)
      setDoctorSignInMode('delay')
    }else{
      // 医生在线，确认为患者签到
      Modal.confirm({
        title: '确认过号签到',
        icon: <InfoCircleOutlined />,
        content: <span>是否确认为<b>{record.patientName}</b>过号签到<b>{record.doctorName}</b>医生?</span>,
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const success = await handlePatientDelay({
            ...record,
            tarDeptCode: record.deptCode,
            tarDoctorCode: record.doctorCode,
          });
          if (success) {
            setSelectPatient({})
            reset();
          }
        }
      });
    }
  }

  const columns_modal = [
    {
      title: '序号',
      width: 40,
      render:(text, record, index) => 
      `${(tableProps.pagination.current - 1) * (tableProps.pagination.pageSize) + (index + 1)}`  
      //当前页数减1乘以每一页页数再加当前页序号+1
    },
    {
      title: '病人ID',
      width: 80,
      dataIndex: 'patientID',
      key: 'patientID'
    },
    {
      title: '病人姓名',
      width: 80,
      dataIndex: 'patientName',
      key: 'patientName'
    },
    {
      title: '就诊科室',
      dataIndex: 'deptName',
      width: 60
    },
    {
      title: '就诊医生',
      dataIndex: 'doctorName',
      width: 60
    },
    {
      title: '就诊状态',
      dataIndex: 'stateName',
      width: 130,
      key: 'stateName',
      render: (text, record) => {
        switch (record.state) {
          case 0:
          case 1:
          case 2:
          case 3:
          case 4:
            return <div><Tag color={stateColor[record.state]}>{text}</Tag> {record.registerType === 0 ? '' : <Tag color={stateColor[4]}>{`${record.registerTypeName}签到`}</Tag>}{record.patientMoveTypeName ? <Tag color={stateColor[5]}>{record.patientMoveTypeName}</Tag>: ''}</div>
          default:
            return <div><Tag color={stateColor[record.state]}>{text}</Tag> {record.patientMoveTypeName ? <Tag color={stateColor[5]}>{record.patientMoveTypeName}</Tag>: ''}</div>
        }
      }
    },
    {
      title: '挂号序号',
      dataIndex: 'checkCode',
      width: 60,
      key: 'checkCode'
    },
    {
      title: '上下午',
      dataIndex: 'noonName',
      width: 60,
      key: 'noonName'
    },
    // 0 签到未分配 1 签到已分配 2 正在叫号 3 完成 4 过号 5 回诊签到 6 his录入未签到
    {
      title: '操作',
      key: 'operation',
      fixed: 'right',
      width: 180,
      render: (text, record) => {
        switch (record.state) {
          case 0:
          case 1:
            return <><a onClick={() => {
              setSelectPatient(record)
              setMoveTopMode('single')
              handleMoveToTopModalVisible(true)
            }}>患者置顶</a>
            <Divider type="vertical" />
            <a onClick={() => {
              setSelectPatient(record)
              setMoveDocMode('single')
              handleMoveToDocModalVisible(true)
            }}>指派医生</a>
            <Divider type="vertical" />
            <a onClick={() => {
              setSelectPatient(record)
              setMoveDeptMode('single')
              handleMoveToDeptModalVisible(true)
            }}>转移科室</a>
            {/* <a onClick={()=>{
              Modal.confirm({
                title: '确认移动',
                icon: <InfoCircleOutlined />,
                content: `是否确认为${record.patientName}移动到上一位?`,
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  console.log(tableProps.pagination)
                  const success = await handlePatientUp(record);
                  if (success) {
                    setSelectPatient({})
                    run({...tableProps.pagination});
                  }
                }
              });
            }}>上移</a>
            <Divider type="vertical"/>
            <a onClick={()=>{
              Modal.confirm({
                title: '确认移动',
                icon: <InfoCircleOutlined />,
                content: `是否确认为${record.patientName}移动到下一位?`,
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  console.log(tableProps.pagination)
                  const success = await handlePatientDown(record);
                  if (success) {
                    setSelectPatient({})
                    run({...tableProps.pagination});
                  }
                }
              });
            }}>下移</a> */}
            </>
          case 6:
            return <a onClick={() => {
              Modal.confirm({
                title: '确认签到',
                icon: <InfoCircleOutlined />,
                content: `是否确认为${record.patientName}签到?`,
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  const success = await handlePatientCheck(record);
                  if (success) {
                    setSelectPatient({})
                    reset()
                  }
                }
              });
            }}>签到</a>
          case 3:
            return <a onClick={() => {
              setSelectPatient(record)
              handleBack(record);
              // Modal.confirm({
              //   title: '确认回诊签到',
              //   icon: <InfoCircleOutlined />,
              //   content: `是否确认为${record.patientName}回诊签到?`,
              //   okText: '确认',
              //   cancelText: '取消',
              //   onOk: async () => {
              //     const success = await handlePatientBack(record);
              //     if (success) {
              //       setSelectPatient({})
              //       reset();
              //     }
              //   }
              // });
            }}>回诊签到</a>
          case 4:
            return <a onClick={() => {
              setSelectPatient(record)
              handleDelay(record);
              // Modal.confirm({
              //   title: '确认过号签到',
              //   icon: <InfoCircleOutlined />,
              //   content: `是否确认为${record.patientName}过号签到?`,
              //   okText: '确认',
              //   cancelText: '取消',
              //   onOk: async () => {
              //     const success = await handlePatientDelay(record);
              //     if (success) {
              //       setSelectPatient({})
              //       reset();
              //     }
              //   }
              // });
            }}>过号签到</a>
          default:
            break;
        }
        return ''
      }
    }
  ];

  const columns = [
    {
      title: '序号',
      width: 40,
      render:(text, record, index) => 
      `${(tableProps.pagination.current - 1) * (tableProps.pagination.pageSize) + (index + 1)}`  
      //当前页数减1乘以每一页页数再加当前页序号+1
    },
    {
      title: '病人ID',
      width: 80,
      dataIndex: 'patientID',
      key: 'patientID'
    },
    {
      title: '病人姓名',
      width: 80,
      dataIndex: 'patientName',
      key: 'patientName'
    },
    {
      title: '就诊医生',
      dataIndex: 'doctorName',
      width: 60
    },
    {
      title: '就诊状态',
      dataIndex: 'stateName',
      width: 130,
      key: 'stateName',
      render: (text, record) => {
        switch (record.state) {
          case 0:
          case 1:
          case 2:
          case 3:
          case 4:
            return <div><Tag color={stateColor[record.state]}>{text}</Tag> {record.registerType === 0 ? '' : <Tag color={stateColor[4]}>{`${record.registerTypeName}签到`}</Tag>}{record.patientMoveTypeName ? <Tag color={stateColor[5]}>{record.patientMoveTypeName}</Tag>: ''}</div>
          default:
            return <div><Tag color={stateColor[record.state]}>{text}</Tag> {record.patientMoveTypeName ? <Tag color={stateColor[5]}>{record.patientMoveTypeName}</Tag>: ''}</div>
        }
      }
    },
    {
      title: '挂号序号',
      dataIndex: 'checkCode',
      width: 60,
      key: 'checkCode'
    },
    {
      title: '上下午',
      dataIndex: 'noonName',
      width: 60,
      key: 'noonName'
    },
    // 建德版本挂号时间
    {
      title: '挂号时间',
      dataIndex: 'registerTime',
      width: 120,
      key: 'registerTime',
      render: (text: string)=> { 
        if(text) {
          return moment(text).format('M月D日 HH:mm')
        }else{
          return ""
        }
      }
    },
    {
      title: '签到时间',
      dataIndex: 'checkTime',
      width: 120,
      key: 'checkTime',
      render: (text: string)=> { 
        if(text) {
          return moment(text).format('M月D日 HH:mm')
        }else{
          return ""
        }
      }
    },
    {
      title: '叫号时间',
      dataIndex: 'callTime',
      width: 120,
      key: 'callTime',
      render: (text: string)=> { 
        if(text) {
          return moment(text).format('M月D日 HH:mm')
        }else{
          return ""
        }
      }
    },
    // 0 签到未分配 1 签到已分配 2 正在叫号 3 完成 4 过号 5 回诊签到 6 his录入未签到
    {
      title: '操作',
      key: 'operation',
      fixed: 'right',
      width: 180,
      render: (text, record) => {
        switch (record.state) {
          case 0:
            return <a onClick={() => {
              setSelectPatient(record)
              setMoveDeptMode('single')
              handleMoveToDeptModalVisible(true)
            }}>转移科室</a>
          case 1:
            return <>
            <a onClick={() => {
              setSelectPatient(record)
              setMoveTopMode('single')
              handleMoveToTopModalVisible(true)
            }}>患者置顶</a>
            <Divider type="vertical" />
            <a onClick={() => {
              setSelectPatient(record)
              setMoveDocMode('single')
              handleMoveToDocModalVisible(true)
            }}>指派医生</a>
            <Divider type="vertical" />
            <a onClick={() => {
              setSelectPatient(record)
              setMoveDeptMode('single')
              handleMoveToDeptModalVisible(true)
            }}>转移科室</a>
            {/* <a onClick={()=>{
              Modal.confirm({
                title: '确认移动',
                icon: <InfoCircleOutlined />,
                content: `是否确认为${record.patientName}移动到上一位?`,
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  console.log(tableProps.pagination)
                  const success = await handlePatientUp(record);
                  if (success) {
                    setSelectPatient({})
                    run({...tableProps.pagination});
                  }
                }
              });
            }}>上移</a>
            <Divider type="vertical"/>
            <a onClick={()=>{
              Modal.confirm({
                title: '确认移动',
                icon: <InfoCircleOutlined />,
                content: `是否确认为${record.patientName}移动到下一位?`,
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  console.log(tableProps.pagination)
                  const success = await handlePatientDown(record);
                  if (success) {
                    setSelectPatient({})
                    run({...tableProps.pagination});
                  }
                }
              });
            }}>下移</a> */}
            </>
          case 6:
            return <>
              <a onClick={() => {
                Modal.confirm({
                  title: '确认签到',
                  icon: <InfoCircleOutlined />,
                  content: `是否确认为${record.patientName}签到?`,
                  okText: '确认',
                  cancelText: '取消',
                  onOk: async () => {
                    const success = await handlePatientCheck(record);
                    if (success) {
                      setSelectPatient({})
                      reset()
                    }
                  }
                });
              }}>签到</a>
              <Divider type="vertical" />
              <a onClick={() => {
                setSelectPatient(record)
                setMoveDeptMode('single')
                handleMoveToDeptModalVisible(true)
              }}>转移科室</a>
            </>
          case 3:
            return <>
              <a onClick={() => {
                setSelectPatient(record)
                handleBack(record);
              }}>回诊签到</a>
              <Divider type="vertical" />
              <a onClick={() => {
                setSelectPatient(record)
                setMoveDeptMode('single')
                handleMoveToDeptModalVisible(true)
              }}>转移科室</a>
            </>
          case 4:
            return <>
              <a onClick={() => {
                setSelectPatient(record)
                handleDelay(record);
              }}>过号签到</a>
              <Divider type="vertical" />
              <a onClick={() => {
                setSelectPatient(record)
                setMoveDeptMode('single')
                handleMoveToDeptModalVisible(true)
              }}>转移科室</a>
            </>
          default:
            return <a onClick={() => {
              setSelectPatient(record)
              setMoveDeptMode('single')
              handleMoveToDeptModalVisible(true)
            }}>转移科室</a>
            // break;
        }
        return ''
      }
    }
  ];
 
   // 手动选择/取消选择某行
  const tableSelect = (record: any, selected: any) => {
    //如果选中某一行，把选中的这行数据及id分别存到checkedData、selectedRowKeys中
    if (selected) {
      const arr = checkedData.map((item: any) => {
        return item.key;
      });
      setSelectedRowKeys([...arr, record.key]);
      setCheckedData([...checkedData, record]);
    //取消选中，则在checkedData、selectedRowKeys中过滤掉这条数据
    } else {
      const newData = checkedData.filter((item: any) => item.key !== record.key);
      setCheckedData(newData);
      const arr = newData.map((item: any) => {
        return item.key;
      });
      setSelectedRowKeys([...arr]);
    }
  };
  
  // 表格全选/取消全选
  const tableSelectAll = (selected: boolean, selectedRows: any, changeRows: any) => {
    //selected返回的是boolean，true为全选，false为取消全选
    //changeRows是操作中发生了改变的数据
    //取消全选，把取消的数据过滤掉，
    if (!selected) {
      // changeRows.forEach((row: any) => {
      //   const newData = checkedData.filter((item: any) => item.key !== row.key);
      //   setCheckedData(newData)
      // });
      const arr = changeRows.map((item: any) => item.key);
      let data = new Set(checkedData)
      var removeElements = remove(data, obj => arr.includes(obj.key));
      setCheckedData([...removeElements])
      const newArr = selectedRowKeys.filter((item: any) => !arr.some((ele: any) => ele === item));
      setSelectedRowKeys([...newArr]);
    } 
    //全选，把由未选中变为选中的数据统一放到setSelectedRowKeys中存储
    else {
      const arr = changeRows.map((item: any) => item.key);//过滤出选中数据的唯一值
      setCheckedData([...checkedData, ...changeRows]);
      setSelectedRowKeys([...selectedRowKeys, ...arr]);//存储所有选中数据的唯一值，可用于批量删除/新增/修改等操作
    }
  }

  // 提交签到
  const onCheckInSubmit = async (value) => {
    if(!value) return
    const patient =  await handlePatientSearch({
      queryStr: value
    });
    console.log(patient)
    if(patient && patient.length > 0){
      form.resetFields(['checkData'])
      setPatientSearchList(patient)
      handlePatientSearchModalVisible(true)
    }else{
      form.resetFields(['checkData'])
      return message.error('未查询到患者信息')
    }
  }

  // 取消全选
  const cancelSelect = () => {
    setCheckedData([])
    setSelectedRowKeys([])
  }

  // 设置状态
  const handleTableStateType = (value: string) => {
    setStateType(value);
  }

  // 批量置顶
  const handleMutiMoveToTop = () => {
    setMoveTopMode('mutil');
    handleMoveToTopModalVisible(true)
  }

  // 批量转诊
  const handleMutiMoveToDoc = () => {
    setMoveDocMode('mutil');
    handleMoveToDocModalVisible(true)
  }

  // 完成就诊
  const handleMutiFinish = () => {
    Modal.confirm({
      title: '确认完成就诊',
      icon: <InfoCircleOutlined />,
      content: `是否确认为选中的${selectedRowKeys.length}位患者完成就诊?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const success = await handlePatientFinish({
          patientIDs: checkedData.map(item => item.patientID).join(','),
          deptCode: customParams.deptCode
        });
        if (success) {
          reset();
        }
      }
    });
  }

  const searchForm = (
    <>
      <div className={styles.searchbox}>
        <Form form={form} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
          <Segmented options={[{
            label: '全部队列',
            value: '0',
          },{
            label: '未签到队列',
            value: '3',
          },{
            label: '签到队列',
            value: '4',
          },{
            label: '过号队列',
            value: '2',
          },{
            label: '回诊队列',
            value: '1',
          }, {
            label: '已完成队列',
            value: '5',
          }]} value={stateType} onChange={handleTableStateType} size="middle" />
          <Form.Item name="checkData" label="诊区签到" >
            <Input.Search placeholder="凭证扫码全诊区搜索签到" style={{ width: 240 }} onSearch={onCheckInSubmit}  allowClear />
          </Form.Item>
          <Form.Item name="queryStr" label="搜索" >
            <Input.Search placeholder="请输入姓名、门诊号" style={{ width: 200 }} onSearch={submit} allowClear/>
          </Form.Item>
          <Form.Item>
            <Button onClick={submit} type="primary"><RetweetOutlined />刷新列表</Button>
          </Form.Item>
        </Form>
      </div>
      {selectedRowKeys.length > 0 ?
        <div className={styles.selectedRowTip}>
          <Space><span>已选择 {selectedRowKeys.length} 项</span><a onClick={cancelSelect}>取消选择</a></Space>
          <Space color='blue'>
            {/* <Button type='primary' size='small' icon={<TagOutlined />} onClick={handleMutiMoveToTop}>批量置顶</Button> */}
            <Button type='primary' size='small' icon={<SyncOutlined />} onClick={handleMutiMoveToDoc}>转移队列</Button>
            <Button type='primary' size='small' icon={<CheckOutlined />} onClick={handleMutiFinish}>设置已就诊</Button>
          </Space>
        </div>: <div>&nbsp;</div>
      }
    </>
  );

  return (
    <div>
      {searchForm}
      <Table columns={columns} rowKey="key" size="middle" 
        rowSelection={{
          selectedRowKeys: selectedRowKeys,  //展示选中的数据
          onSelect: tableSelect,            //单条数据取消/选中的回调
          onSelectAll: tableSelectAll,   //全选/取消全选的回调
          // getCheckboxProps: (record) => ({
          //   disabled: record.state !== 1 && record.state !== 0 && record.state !== 6 && record.state !== 3,
          // }),
        }}
        rowClassName={(record, idx) => {
          if (moment(record.registerTime).isSame(moment().subtract(1, 'day'), "day"))
            return styles.bgRow;
          return ''
        }}
        {...tableProps}
        pagination={{
          showSizeChanger: true,
          showTotal: (total, range) => `第${range.join('-')}条/总共${total}条`,
          ...tableProps.pagination
        }} 
      />
      {patientSearchModalVisible && (
        <Patient
          modalVisible={patientSearchModalVisible}
          data={patientSearchList}
          columns={columns_modal}
          onCancel={() => handlePatientSearchModalVisible(false)}
        />
      )}
      {moveToTopModalVisible && (
        <MoveToTop
          values={selectPatient || {}}
          mutiValues={checkedData || []}
          modalVisible={moveToTopModalVisible}
          onCancel={() => handleMoveToTopModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value)
            const success = await handleMovePatient(value);
            if (success) {
              handleMoveToTopModalVisible(false);
              reset()
            }
          }}
          mode={moveTopMode}   // single || multi
        />
      )}
      {moveToDocModalVisible && (
        <MoveToDoc
          values={selectPatient || {}}
          mutiValues={checkedData || []}
          deptCode={customParams.deptCode}
          modalVisible={moveToDocModalVisible}
          onCancel={() => handleMoveToDocModalVisible(false)}
          onSubmit={async (value) => {
            const success = await handleMoveToDept(value); // 跨科室转诊
            if (success) {
              handleMoveToDocModalVisible(false);
              reset()
              cancelSelect()
            }
          }}
          mode={moveDocMode}  // single || multi
        />
      )}
      {
        moveToDeptModalVisible && (
          <MoveToDept
            values={selectPatient || {}}
            mutiValues={checkedData || []}
            deptCode={customParams.deptCode}
            modalVisible={moveToDeptModalVisible}
            onCancel={() => handleMoveToDeptModalVisible(false)}
            onSubmit={async (value) => {
              const success = await handleMoveToDept(value); // 跨科室转诊
              if (success) {
                handleMoveToDeptModalVisible(false);
                reset()
                cancelSelect()
              }
            }}
            mode={moveDeptMode}  // single || multi
          />
        )
      }
      {
        signInModalVisible && (
          <SignIn
            values={selectPatient || {}}
            doctorList={doctorSignInList}
            mode={doctorSignInMode}  // delay || back
            modalVisible={signInModalVisible}
            onCancel={() => handleSignInModalVisible(false)}
            onSubmit={async (value) => {
              let success
              if(doctorSignInMode === 'back'){
                success = await handlePatientBack({
                  tarDeptCode: value.newDoctorCode.split('|')[1],
                  tarDoctorCode: value.newDoctorCode.split('|')[0],
                  ...value
                }); // 回诊
              }else if(doctorSignInMode === 'delay'){
                success = await handlePatientDelay({
                  tarDeptCode: value.newDoctorCode.split('|')[1],
                  tarDoctorCode: value.newDoctorCode.split('|')[0],
                  ...value
                }); // 过号 
              }
              if (success) {
                handleSignInModalVisible(false);
                reset()
              }
            }}
          />
        )
      }
    </div>
  );
}

export default DeptTable