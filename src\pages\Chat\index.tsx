import React, { useState, useEffect, useRef, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, List, Avatar, Input, Badge, message, Switch, InputNumber, Space, Tooltip } from 'antd';
import { UserOutlined, SearchOutlined, SyncOutlined, PauseOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import ChatWindow, { DisplayChatMessage } from '@/components/ChatWindow';
import type {
  ChatConversation,
  ChatMessage,
  ApiResponse
} from '@/services/api/chat';
import {
  getConversationList,
  getChatHistory,
  sendTextMessage,
  sendImageMessage,
  sendImageMessageBase64,
  markMessagesAsRead
} from '@/services/api/chat';
import './index.less';

// 联系人类型定义
interface Contact {
  id: string;
  name: string;
  avatar: string;
  unread: number;
}

const ChatPage: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedConversation, setSelectedConversation] = useState<ChatConversation | null>(null);
  const [messages, setMessages] = useState<DisplayChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [conversationsLoading, setConversationsLoading] = useState(false);

  // 获取当前用户ID（设备号）
  const currentUserId = initialState?.currentUser?.userName || 'device001';

  // 轮询配置
  const [pollingInterval, setPollingInterval] = useState(1000); // 默认1秒
  const [isPollingEnabled, setIsPollingEnabled] = useState(true); // 是否启用轮询
  const conversationPollingRef = useRef<NodeJS.Timeout | null>(null);
  const messagePollingRef = useRef<NodeJS.Timeout | null>(null);

  // 加载会话列表
  const loadConversations = useCallback(async (showLoading = true) => {
    if (showLoading) {
      setConversationsLoading(true);
    }
    try {
      const response = await getConversationList({ userId: currentUserId });
      if (response.code === '0') {
        setConversations(response.data || []);
      } else {
        // 轮询时不显示错误提示，避免频繁弹窗
        if (showLoading) {
          message.error(response.msg || '获取会话列表失败');
        }
      }
    } catch (error) {
      // 轮询时不显示错误提示，避免频繁弹窗
      if (showLoading) {
        message.error('获取会话列表失败');
      }
    } finally {
      if (showLoading) {
        setConversationsLoading(false);
      }
    }
  }, [currentUserId]);

  // 开始轮询会话列表
  const startConversationPolling = useCallback(() => {
    if (!isPollingEnabled) return;

    // 清除现有轮询
    if (conversationPollingRef.current) {
      clearInterval(conversationPollingRef.current);
    }

    // 设置新的轮询
    conversationPollingRef.current = setInterval(() => {
      loadConversations(false); // 轮询时不显示loading
    }, pollingInterval);
  }, [isPollingEnabled, pollingInterval, loadConversations]);

  // 停止轮询会话列表
  const stopConversationPolling = useCallback(() => {
    if (conversationPollingRef.current) {
      clearInterval(conversationPollingRef.current);
      conversationPollingRef.current = null;
    }
  }, []);

  // 加载聊天记录
  const loadChatHistory = useCallback(async (conversationId: string, showLoading = true) => {
    if (showLoading) {
      setLoading(true);
    }
    try {
      const response = await getChatHistory({ conversationId });

      if (response.code === '0') {
        // 转换消息格式以适配UI显示
        const displayMessages: DisplayChatMessage[] = (response.data || []).map(msg => ({
          ...msg,
          sender: msg.senderId === currentUserId ? 'self' : 'other'
        }));
        setMessages(displayMessages);
      } else {
        // 轮询时不显示错误提示
        if (showLoading) {
          message.error(response.msg || '获取聊天记录失败');
        }
      }
    } catch (error) {
      // 轮询时不显示错误提示
      if (showLoading) {
        message.error('获取聊天记录失败');
      }
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  }, [currentUserId]);

  // 开始轮询消息
  const startMessagePolling = useCallback(() => {
    if (!isPollingEnabled || !selectedConversation) return;

    // 清除现有轮询
    if (messagePollingRef.current) {
      clearInterval(messagePollingRef.current);
    }

    // 设置新的轮询
    messagePollingRef.current = setInterval(() => {
      loadChatHistory(selectedConversation.conversationId, false); // 轮询时不显示loading
    }, pollingInterval);
  }, [isPollingEnabled, selectedConversation, pollingInterval, loadChatHistory]);

  // 停止轮询消息
  const stopMessagePolling = useCallback(() => {
    if (messagePollingRef.current) {
      clearInterval(messagePollingRef.current);
      messagePollingRef.current = null;
    }
  }, []);

  // 组件加载时获取会话列表并开始轮询
  useEffect(() => {
    loadConversations();
    startConversationPolling();

    // 组件卸载时清理轮询
    return () => {
      stopConversationPolling();
      stopMessagePolling();
    };
  }, [loadConversations, startConversationPolling, stopConversationPolling, stopMessagePolling]);

  // 页面可见性变化时控制轮询
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时停止轮询
        stopConversationPolling();
        stopMessagePolling();
      } else {
        // 页面显示时恢复轮询
        if (isPollingEnabled) {
          startConversationPolling();
          if (selectedConversation) {
            startMessagePolling();
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isPollingEnabled, selectedConversation, startConversationPolling, startMessagePolling, stopConversationPolling, stopMessagePolling]);

  // 过滤会话
  const filteredConversations = conversations.filter(conv =>
    (conv.otherUserName || '').toLowerCase().includes(searchText.toLowerCase())
  );

  // 选择会话
  const handleSelectConversation = async (conversation: ChatConversation) => {
    // 停止当前消息轮询
    stopMessagePolling();

    setSelectedConversation(conversation);

    // 加载聊天记录
    await loadChatHistory(conversation.conversationId, true);

    // 标记消息为已读
    if (conversation.unreadCount && conversation.unreadCount > 0) {
      try {
        await markMessagesAsRead({
          conversationId: conversation.conversationId,
          userId: currentUserId
        });

        // 更新本地会话列表的未读数
        setConversations(prev =>
          prev.map(conv =>
            conv.conversationId === conversation.conversationId
              ? { ...conv, unreadCount: 0 }
              : conv
          )
        );
      } catch (error) {
        console.error('标记已读失败:', error);
      }
    }

    // 开始新的消息轮询
    setTimeout(() => {
      startMessagePolling();
    }, 100); // 稍微延迟一下，确保selectedConversation已更新
  };

  // 发送消息
  const handleSendMessage = async (messageData: {
    content: string;
    messageType: number;
    imageFile?: File;
  }) => {
    if (!selectedConversation) return false;

    const receiverId = selectedConversation.otherUserId ||
      (selectedConversation.user1Id === currentUserId
        ? selectedConversation.user2Id
        : selectedConversation.user1Id);

    try {
      let response: ApiResponse<ChatMessage> | undefined;

      // 根据消息类型调用不同的API
      if (messageData.messageType === 1) {
        // 发送文字消息
        response = await sendTextMessage({
          senderId: currentUserId,
          receiverId,
          content: messageData.content,
        });
      } else if (messageData.messageType === 2 && messageData.imageFile) {
        // 先添加一个临时的预览消息到界面
        const tempMessage: DisplayChatMessage = {
          messageId: `temp_${Date.now()}`,
          conversationId: selectedConversation.conversationId,
          senderId: currentUserId,
          receiverId,
          messageType: 2,
          content: '[图片上传中...]',
          imageUrl: messageData.content, // 使用预览URL
          sendStatus: 1, // 发送中
          readStatus: 0,
          createTime: new Date().toISOString(),
          sender: 'self'
        };

        // 立即显示预览消息
        setMessages(prev => [...prev, tempMessage]);

        // 发送图片消息
        response = await sendImageMessage({
          senderId: currentUserId,
          receiverId,
          imageFile: messageData.imageFile,
        });

        // 移除临时消息
        setMessages(prev => prev.filter(msg => msg.messageId !== tempMessage.messageId));
      }

      if (response && response.code === '0') {
        // 将新消息添加到消息列表
        const newDisplayMessage: DisplayChatMessage = {
          ...response.data,
          sender: 'self'
        };
        setMessages(prev => [...prev, newDisplayMessage]);

        // 清理预览URL（如果是图片消息）
        if (messageData.messageType === 2 && messageData.content.startsWith('blob:')) {
          URL.revokeObjectURL(messageData.content);
        }

        // 更新会话列表中的最后消息信息
        setConversations(prev =>
          prev.map(conv =>
            conv.conversationId === selectedConversation.conversationId
              ? {
                  ...conv,
                  lastMessageContent: response.data.messageType === 2 ? '[图片]' : response.data.content,
                  lastMessageType: response.data.messageType,
                  lastMessageTime: response.data.createTime,
                }
              : conv
          )
        );

        return true;
      } else {
        message.error(response?.msg || '发送消息失败');
        return false;
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败');
      return false;
    }
  };

  return (
    <PageContainer header={{ breadcrumb: {} }}>
      <div className="chat-container">
        <Card className="contacts-card">
          {/* 轮询控制 */}
          <div className="polling-controls">
            <Space size="small">
              <Tooltip title={isPollingEnabled ? "停止实时更新" : "开启实时更新"}>
                <Switch
                  checked={isPollingEnabled}
                  onChange={setIsPollingEnabled}
                  checkedChildren={<SyncOutlined spin />}
                  unCheckedChildren={<PauseOutlined />}
                  size="small"
                />
              </Tooltip>
              <span style={{ fontSize: '12px', color: '#666' }}>实时更新</span>
              {isPollingEnabled && (
                <>
                  <InputNumber
                    min={500}
                    max={10000}
                    step={500}
                    value={pollingInterval}
                    onChange={(value) => setPollingInterval(value || 1000)}
                    size="small"
                    style={{ width: 80 }}
                    formatter={value => `${value}ms`}
                    parser={value => parseInt(value?.replace('ms', '') || '1000', 10)}
                  />
                </>
              )}
            </Space>
          </div>

          <Input
            placeholder="搜索联系人"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            className="search-input"
          />
          
          <List
            className="contacts-list"
            loading={conversationsLoading}
            dataSource={filteredConversations}
            renderItem={conversation => (
              <List.Item
                className={`contact-item ${selectedConversation?.conversationId === conversation.conversationId ? 'selected' : ''}`}
                onClick={() => handleSelectConversation(conversation)}
              >
                <List.Item.Meta
                  avatar={
                    <Badge count={conversation.unreadCount || 0} size="small">
                      <Avatar icon={<UserOutlined />} src={conversation.otherUserAvatar} />
                    </Badge>
                  }
                  title={conversation.otherUserName || '未知用户'}
                  description={
                    <div className="conversation-preview">
                      <span className="last-message">
                        {conversation.lastMessageType === 2 ? '[图片]' : conversation.lastMessageContent}
                      </span>
                      {conversation.lastMessageTime && (
                        <span className="last-time">
                          {new Date(conversation.lastMessageTime).toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
        
        <Card className="chat-card">
          {selectedConversation ? (
            <ChatWindow
              receiverId={selectedConversation.otherUserId ||
                (selectedConversation.user1Id === currentUserId
                  ? selectedConversation.user2Id
                  : selectedConversation.user1Id)}
              receiverName={selectedConversation.otherUserName || '未知用户'}
              receiverAvatar={selectedConversation.otherUserAvatar}
              onSendMessage={handleSendMessage}
              messages={messages}
              loading={loading}
            />
          ) : (
            <div className="no-chat-selected">
              <p>请选择一个会话开始聊天</p>
            </div>
          )}
        </Card>
      </div>
    </PageContainer>
  );
};

export default ChatPage;