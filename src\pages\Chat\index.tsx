import React, { useState, useEffect } from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import { Card, List, Avatar, Input, Badge, message } from 'antd';
import { UserOutlined, SearchOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import ChatWindow, { DisplayChatMessage } from '@/components/ChatWindow';
import type {
  ChatConversation,
  ChatMessage,
  ApiResponse
} from '@/services/api/chat';
import {
  getConversationList,
  getChatHistory,
  sendTextMessage,
  sendImageMessage,
  sendImageMessageBase64,
  markMessagesAsRead
} from '@/services/api/chat';
import './index.less';

// 联系人类型定义
interface Contact {
  id: string;
  name: string;
  avatar: string;
  unread: number;
}

const ChatPage: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedConversation, setSelectedConversation] = useState<ChatConversation | null>(null);
  const [messages, setMessages] = useState<DisplayChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [conversationsLoading, setConversationsLoading] = useState(false);

  // 获取当前用户ID（设备号）
  const currentUserId = initialState?.currentUser?.userName || 'device001';

  // 加载会话列表
  const loadConversations = async () => {
    setConversationsLoading(true);
    try {
      const response = await getConversationList({ userId: currentUserId });
      if (response.code === '0') {
        setConversations(response.data || []);
      } else {
        message.error(response.msg || '获取会话列表失败');
      }
    } catch (error) {
      message.error('获取会话列表失败');
    } finally {
      setConversationsLoading(false);
    }
  };

  // 组件加载时获取会话列表
  useEffect(() => {
    loadConversations();
  }, [currentUserId]);

  // 过滤会话
  const filteredConversations = conversations.filter(conv =>
    (conv.otherUserName || '').toLowerCase().includes(searchText.toLowerCase())
  );

  // 选择会话
  const handleSelectConversation = async (conversation: ChatConversation) => {
    setSelectedConversation(conversation);
    setLoading(true);

    try {
      // 获取聊天记录
      const response = await getChatHistory({
        conversationId: conversation.conversationId
      });

      if (response.code === '0') {
        // 转换消息格式以适配UI显示
        const displayMessages: DisplayChatMessage[] = (response.data || []).map(msg => ({
          ...msg,
          sender: msg.senderId === currentUserId ? 'self' : 'other'
        }));
        setMessages(displayMessages);

        // 标记消息为已读
        if (conversation.unreadCount && conversation.unreadCount > 0) {
          await markMessagesAsRead({
            conversationId: conversation.conversationId,
            userId: currentUserId
          });

          // 更新本地会话列表的未读数
          setConversations(prev =>
            prev.map(conv =>
              conv.conversationId === conversation.conversationId
                ? { ...conv, unreadCount: 0 }
                : conv
            )
          );
        }
      } else {
        message.error(response.msg || '获取聊天记录失败');
      }
    } catch (error) {
      message.error('获取聊天记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 发送消息
  const handleSendMessage = async (messageData: {
    content: string;
    messageType: number;
    imageFile?: File;
  }) => {
    if (!selectedConversation) return false;

    const receiverId = selectedConversation.otherUserId ||
      (selectedConversation.user1Id === currentUserId
        ? selectedConversation.user2Id
        : selectedConversation.user1Id);

    try {
      let response: ApiResponse<ChatMessage> | undefined;

      // 根据消息类型调用不同的API
      if (messageData.messageType === 1) {
        // 发送文字消息
        response = await sendTextMessage({
          senderId: currentUserId,
          receiverId,
          content: messageData.content,
        });
      } else if (messageData.messageType === 2 && messageData.imageFile) {
        // 先添加一个临时的预览消息到界面
        const tempMessage: DisplayChatMessage = {
          messageId: `temp_${Date.now()}`,
          conversationId: selectedConversation.conversationId,
          senderId: currentUserId,
          receiverId,
          messageType: 2,
          content: '[图片上传中...]',
          imageUrl: messageData.content, // 使用预览URL
          sendStatus: 1, // 发送中
          readStatus: 0,
          createTime: new Date().toISOString(),
          sender: 'self'
        };

        // 立即显示预览消息
        setMessages(prev => [...prev, tempMessage]);

        // 发送图片消息
        response = await sendImageMessage({
          senderId: currentUserId,
          receiverId,
          imageFile: messageData.imageFile,
        });

        // 移除临时消息
        setMessages(prev => prev.filter(msg => msg.messageId !== tempMessage.messageId));
      }

      if (response && response.code === '0') {
        // 将新消息添加到消息列表
        const newDisplayMessage: DisplayChatMessage = {
          ...response.data,
          sender: 'self'
        };
        setMessages(prev => [...prev, newDisplayMessage]);

        // 清理预览URL（如果是图片消息）
        if (messageData.messageType === 2 && messageData.content.startsWith('blob:')) {
          URL.revokeObjectURL(messageData.content);
        }

        // 更新会话列表中的最后消息信息
        setConversations(prev =>
          prev.map(conv =>
            conv.conversationId === selectedConversation.conversationId
              ? {
                  ...conv,
                  lastMessageContent: response.data.messageType === 2 ? '[图片]' : response.data.content,
                  lastMessageType: response.data.messageType,
                  lastMessageTime: response.data.createTime,
                }
              : conv
          )
        );

        return true;
      } else {
        message.error(response?.msg || '发送消息失败');
        return false;
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败');
      return false;
    }
  };

  return (
    <PageContainer header={{ breadcrumb: {} }}>
      <div className="chat-container">
        <Card className="contacts-card">
          <Input
            placeholder="搜索联系人"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            className="search-input"
          />
          
          <List
            className="contacts-list"
            loading={conversationsLoading}
            dataSource={filteredConversations}
            renderItem={conversation => (
              <List.Item
                className={`contact-item ${selectedConversation?.conversationId === conversation.conversationId ? 'selected' : ''}`}
                onClick={() => handleSelectConversation(conversation)}
              >
                <List.Item.Meta
                  avatar={
                    <Badge count={conversation.unreadCount || 0} size="small">
                      <Avatar icon={<UserOutlined />} src={conversation.otherUserAvatar} />
                    </Badge>
                  }
                  title={conversation.otherUserName || '未知用户'}
                  description={
                    <div className="conversation-preview">
                      <span className="last-message">
                        {conversation.lastMessageType === 2 ? '[图片]' : conversation.lastMessageContent}
                      </span>
                      {conversation.lastMessageTime && (
                        <span className="last-time">
                          {new Date(conversation.lastMessageTime).toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
        
        <Card className="chat-card">
          {selectedConversation ? (
            <ChatWindow
              receiverId={selectedConversation.otherUserId ||
                (selectedConversation.user1Id === currentUserId
                  ? selectedConversation.user2Id
                  : selectedConversation.user1Id)}
              receiverName={selectedConversation.otherUserName || '未知用户'}
              receiverAvatar={selectedConversation.otherUserAvatar}
              onSendMessage={handleSendMessage}
              messages={messages}
              loading={loading}
            />
          ) : (
            <div className="no-chat-selected">
              <p>请选择一个会话开始聊天</p>
            </div>
          )}
        </Card>
      </div>
    </PageContainer>
  );
};

export default ChatPage;