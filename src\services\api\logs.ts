import request from 'umi-request';
import defaultSettings from '../../../config/defaultSettings';

// 获取接口请求日志
export async function queryReqLogList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/statement/getRequestLogList.sp`, {
    method: 'POST',
    data: {...params},
  });
}

// 获取接口请求数据
export async function queryInterfaceCount(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/statement/countInterfaceUsage.sp`, {
    method: 'GET', params,
  });
}


// 获取平台操作日志
export async function queryOptList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/statement/getUserOperatingList.sp`, { params });
}

// 获取支付日志
export async function queryPayList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/statement/getPayList.sp`, { params });
}

// 获取挂号日志
export async function queryRegisterList(params?: Record<string, any>) {
  // eslint-disable-next-line no-param-reassign
  delete params.createTime;
  // eslint-disable-next-line no-param-reassign
  delete params.registrationTime;
  return request(`/${defaultSettings.apiName}/register/getRegistrationList.sp`, { params });
}

// 支付退款
export async function payRefund(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/pay/refundByPwd.np`, {
    method: 'POST',
    data: {...params}
  });
}

// 查询订单 用于刷新订单
export async function queryPay(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/pay/queryPay.sp`, {params});
}

// // 退款
// export async function refundPay(params?: {[key: string]: any}) {
//   return request(`/${defaultSettings.apiName}/pay/refundPay.ip?saID=${params.saID}`, {
//     method: 'POST',
//     data: { ...params.values },
//   });
// }

// 获取投币记录
export async function queryMoneyboxList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/moneybox/getMoneyboxList.sp`, {
    method: 'POST', 
    data: { ...params}
  });
}

// 获取清钞记录
export async function queryClearRecordList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/moneybox/getClearRecordList.sp`, {params});
}



// 获取问诊记录
export async function getConsultationList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/consultation/getConsultationList.sp`, {
      method: 'POST',
      data: {...params},
  });
}