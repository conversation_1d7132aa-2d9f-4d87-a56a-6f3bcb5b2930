import { memo} from 'react';
import { Card} from 'antd';
import { divisionalCountByDoctor } from '@/services/api/queue';
import { useRequest } from 'ahooks';
import { Bar } from '@ant-design/charts';
import { isEqual } from "lodash-es";

// 请求诊区运营医生就诊量排行
const queryDivisionalCountByDoctor = async(digAreaCode: string) => {
  try {
    const response = await divisionalCountByDoctor({
      digAreaCode
    });
    if (response.code === '0') {
      return response.data
    }
    return false;
  } catch(error){
    return false;
  }
}

const DigCountByDept: React.FC<{ code: string }> = ({ code }) => {
  const { data, loading } = useRequest(() => queryDivisionalCountByDoctor(code), {
    refreshDeps: [code],
		pollingInterval: 10000,
    pollingWhenHidden: true
  });

  const MemoPie: React.FC<any> = memo(
    ({data}) => {
      return <Bar 
        data={data}
        xField='count'
        yField='doctorName'
        meta = {{
            doctorName: {
                alias: '医生姓名'
            },
            count: {
                alias: '就诊量'
            }
        }}
        maxBarWidth={20}
      />
    }, (pre, next) => {
      return isEqual(pre?.data, next?.data);
    }
  );

  return (
    <Card title="诊区医生就诊量排行榜" loading={loading}>
        <MemoPie data={data} />
    </Card>
  );
};

export default DigCountByDept;