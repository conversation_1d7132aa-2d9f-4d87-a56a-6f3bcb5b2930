/*
	问卷列表
*/
import React, { useState, useRef, useEffect } from 'react';
import ProTable from '@ant-design/pro-table';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Button,  message, Tag} from 'antd';
import defaultSettings from '../../../../config/defaultSettings';
import { queryAnswerRecord, getAnswerDisplayByRecord, queryAnswerRecordPage} from '@/services/question';
import Answers from "./Components/Answers";
import { parseParam } from '@/utils/utils';
import moment from 'moment';
import 'moment/locale/zh-cn';
import "./index.less";

/**
 * 根据问卷记录查答题结果展示接口
 * @param fields
 */

const selectById = async fields => {
	const hide = message.loading('正在查询');
	try {
		const response = await getAnswerDisplayByRecord({...fields});
		if (response.code === '0') {
			hide();
			message.success('查询成功');
			return response;
		}
		message.error(response.msg);
		return false;
	} catch (error) {
		hide();
		message.error('查询失败请重试！');
		return false;
	}
};

/**
 * 导出
 * @param fields
 */

const handleExportToExcel = async (fields) => {
	const hide = message.loading('正在导出');
	try {
	  const paramsStr = parseParam(fields);
	  console.log(paramsStr);
	  const aLink = document.createElement('a');
	  document.body.appendChild(aLink);
	  aLink.style.display = 'none';
	  aLink.href = `/${defaultSettings.apiName}/answer/getAnswerRecordExcel.sp?${paramsStr}`;
	  aLink.setAttribute('download', '评价数据');
	  aLink.click();
	  document.body.removeChild(aLink);
	  hide();
	  return true;
	} catch (error) {
	  hide();
	  message.error('导出失败请重试！');
	  return false;
	}
};

const colorArr = ['magenta', 'volcano', 'orange', 'gold', 'green', 'blue']

const QuestionResult = (props) => {

	const actionRef = useRef();
    const [answerVisible, setAnswerVisible] = useState(false);
	const [answersList, setAnswerList] = useState([]);
	const [recordObj, setRecordObj] = useState({});

    const handleLookAnswers = async(record)=>{
        if(!record.ID){
			return;
		}
		const res = await selectById({questionnairerecordID: record.ID});
		if(res){
			setAnswerVisible(true);
			setRecordObj(record);
			setAnswerList(res.data || []);
		}		
	}

	const columns = [
        {
			title: '姓名',
			dataIndex: 'name',
			key: 'name',
		},
		{
			title: '评分',
			dataIndex: 'score',
			hideInSearch: true,
			renderText: (_, record) =>{
				return record.answer.split('|')[0] || ''
			}
		},
		{
			title: '标签',
			dataIndex: 'keys',
			hideInSearch: true,
			render: (_, record) =>{
				return record.answer.split('|')[1].split(',').map((item, index)=>{
					return <Tag bordered={false} color={colorArr[index]}>
					{item}
				  </Tag>
				})
			}
		},
        {
			title: '填写时间',
			dataIndex: 'createTime',
			key: 'createTime',
			hideInSearch: true,
		},
		{
			title: '统计日期',
			dataIndex: 'createTimeStr',
			width: 80,
			valueType: 'dateRange',
			hideInTable: true,
			search: {
			  transform: (value) => {
				return {
					beginTime: `${value[0]} 00:00:00`,
					endTime: `${value[1]} 23:59:59`,
				};
			  },
			},
		},
		{
			title: '操作',
			dataIndex: 'option',
			hideInSearch: true,
			render: (_, record, index) => {
				return (
					<a onClick={()=>handleLookAnswers(record)}>查看详情</a>
				)
			}
		}
	];

	return (
		<PageHeaderWrapper>
			<ProTable
				headerTitle="答卷列表"
				actionRef={actionRef}
				rowKey="ID"
				pagination={{ pageSize: 10 }}
				tableAlertRender={false}
				columns={columns}
                request={queryAnswerRecordPage}
				search={{
					optionRender: (searchConfig, formProps, dom) => [
					  ...dom.reverse(),
					  <Button key="out" onClick={() => {
						const values = searchConfig?.form?.getFieldsValue();
						console.log(values)
						handleExportToExcel(values);
					  }}>
						导出
					  </Button>,
					],
				}}
			/>
            { answerVisible && 
                <Answers 
					recordObj={recordObj}
                    data={answersList}
                    visible={answerVisible}
                    onCancel={()=>setAnswerVisible(false)}
                />
            }
		</PageHeaderWrapper>
	);
};

export default QuestionResult;
