import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Input, Button, Upload, Avatar, Spin, message } from 'antd';
import { SendOutlined, PictureOutlined, UserOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { useModel } from '@umijs/max';
import type { ChatMessage } from '@/services/api/chat';
import { formatMessageTime, shouldShowTimeGroup, fileToBase64 } from '@/services/api/chat';
import './index.less';

// 扩展ChatMessage类型以支持本地显示需求
export type DisplayChatMessage = ChatMessage & {
  sender: 'self' | 'other'; // 用于UI显示的发送者类型
};

export type ChatWindowProps = {
  receiverId: string;
  receiverName: string;
  receiverAvatar?: string;
  onSendMessage?: (message: {
    content: string;
    messageType: number; // 1-文字，2-图片
    imageFile?: File;
  }) => Promise<boolean>;
  messages?: DisplayChatMessage[];
  loading?: boolean;
};

const ChatWindow: React.FC<ChatWindowProps> = ({
  receiverId: _receiverId, // 保留参数但标记为未使用
  receiverName,
  receiverAvatar,
  onSendMessage,
  messages = [],
  loading = false,
}) => {
  const { initialState } = useModel('@@initialState');
  const [inputValue, setInputValue] = useState<string>('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 检查是否在底部
  const isScrolledToBottom = () => {
    if (!messagesContainerRef.current) return true;
    const container = messagesContainerRef.current;
    const threshold = 50; // 50px的容差
    return container.scrollHeight - container.scrollTop - container.clientHeight <= threshold;
  };

  // 检查内容是否超过一屏
  const isContentOverflowing = () => {
    if (!messagesContainerRef.current) return false;
    const container = messagesContainerRef.current;
    return container.scrollHeight > container.clientHeight;
  };

  // 处理用户滚动
  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current) return;

    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 检查是否在底部
    const atBottom = isScrolledToBottom();
    setShouldAutoScroll(atBottom);

    // 设置用户正在滚动的状态
    setIsUserScrolling(true);

    // 1秒后重置用户滚动状态
    scrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false);
    }, 1000);
  }, []);

  // 检查是否应该显示回到底部按钮
  const shouldShowScrollButton = () => {
    return !shouldAutoScroll && isContentOverflowing();
  };

  // 滚动到最新消息
  const scrollToBottom = useCallback((force = false) => {
    // 如果用户正在手动滚动且不是强制滚动，则不自动滚动
    if (!force && !shouldAutoScroll) {
      return;
    }

    const scrollAction = () => {
      // 方法1: 使用scrollIntoView
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: force ? 'auto' : 'smooth',
          block: 'end',
          inline: 'nearest'
        });
      }

      // 方法2: 直接设置scrollTop（更可靠）
      if (messagesContainerRef.current) {
        const container = messagesContainerRef.current;
        if (force) {
          container.scrollTop = container.scrollHeight;
        } else {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          });
        }
      }
    };

    // 使用requestAnimationFrame确保DOM已更新
    if (force) {
      scrollAction();
    } else {
      requestAnimationFrame(() => {
        setTimeout(scrollAction, 50);
      });
    }
  }, [shouldAutoScroll]);

  // 添加滚动事件监听器
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => {
        container.removeEventListener('scroll', handleScroll);
        // 清理定时器
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
    return () => {}; // 确保所有代码路径都有返回值
  }, [handleScroll]);

  // 当消息列表变化时滚动到底部
  useEffect(() => {
    if (messages.length > 0) {
      // 只有在应该自动滚动时才滚动
      scrollToBottom();
    }
  }, [messages, scrollToBottom]);

  // 组件挂载时也滚动到底部
  useEffect(() => {
    if (messages.length > 0) {
      // 初始加载时强制滚动
      scrollToBottom(true);
    }
  }, [messages.length, scrollToBottom]);

  // 发送文本消息
  const handleSendTextMessage = () => {
    if (!inputValue.trim()) return;

    if (onSendMessage) {
      onSendMessage({
        content: inputValue,
        messageType: 1, // 1-文字消息
      }).then((success) => {
        if (success) {
          setInputValue('');
          // 发送成功后立即滚动到底部
          setTimeout(() => scrollToBottom(), 50);
        }
      });
    }
  };

  // 处理图片上传
  const handleUpload: UploadProps['customRequest'] = async ({ file, onSuccess, onError }) => {
    if (!file || !onSendMessage) return;

    try {
      // 验证文件类型
      const fileObj = file as File;
      if (!fileObj.type.startsWith('image/')) {
        message.error('只能上传图片文件');
        if (onError) onError(new Error('文件类型不支持'));
        return;
      }

      // 验证文件大小 (限制为10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (fileObj.size > maxSize) {
        message.error('图片大小不能超过10MB');
        if (onError) onError(new Error('文件过大'));
        return;
      }

      // 生成预览URL用于立即显示
      const previewUrl = URL.createObjectURL(fileObj);

      // 先显示预览消息
      const success = await onSendMessage({
        content: previewUrl, // 临时使用预览URL
        messageType: 2, // 2-图片消息
        imageFile: fileObj,
      });

      if (success) {
        if (onSuccess) onSuccess('ok');
        setFileList([]);
        // 上传成功后立即滚动到底部
        setTimeout(() => scrollToBottom(), 50);
      } else {
        // 清理预览URL
        URL.revokeObjectURL(previewUrl);
        if (onError) onError(new Error('上传失败'));
      }
    } catch (error) {
      message.error('图片处理失败');
      if (onError) onError(error as Error);
    }
  };

  // 处理上传变化
  const handleChange = ({ fileList: newFileList }: { fileList: UploadFile[] }) => {
    setFileList(newFileList);
  };

  // 处理按键事件
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendTextMessage();
    }
  };

  return (
    <div className="chat-window">
      <div className="chat-header">
        <div className="chat-title">
          <span>{receiverName}</span>
        </div>
      </div>
      
      <div className="chat-messages" ref={messagesContainerRef}>
        {loading ? (
          <div className="chat-loading">
            <Spin tip="加载消息中..." />
          </div>
        ) : (
          messages.map((msg, index) => {
            const previousMsg = index > 0 ? messages[index - 1] : undefined;
            const showTimeGroup = shouldShowTimeGroup(msg.createTime, previousMsg?.createTime);

            return (
              <div key={msg.messageId || msg.id}>
                {showTimeGroup && (
                  <div className="message-time-group">
                    {formatMessageTime(msg.createTime)}
                  </div>
                )}
                <div
                  className={`message-item ${msg.sender === 'self' ? 'message-self' : 'message-other'}`}
                >
                  {msg.sender === 'other' && (
                    <Avatar
                      src={receiverAvatar}
                      icon={<UserOutlined />}
                      size="large"
                      className="message-avatar"
                    />
                  )}

                  <div className="message-content">
                    {msg.messageType === 1 ? (
                      <div className="message-text">{msg.content}</div>
                    ) : (
                      <div className="message-image">
                        <img src={msg.imageUrl || msg.content} alt="聊天图片" />
                      </div>
                    )}
                  </div>

                  {msg.sender === 'self' && (
                    <Avatar
                      src={initialState?.currentUser?.avatar}
                      icon={<UserOutlined />}
                      size="large"
                      className="message-avatar"
                    />
                  )}
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />

        {/* 滚动状态指示器 - 只有在内容超过一屏且不在底部时显示 */}
        {shouldShowScrollButton() && (
          <div className="scroll-indicator" onClick={() => scrollToBottom(true)}>
            <Button type="primary" size="small" shape="round">
              回到底部
            </Button>
          </div>
        )}
      </div>
      
      <div className="chat-input">
        <Input.TextArea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder="输入消息..."
          autoSize={{ minRows: 1, maxRows: 4 }}
        />
        
        <div className="chat-actions">
          <Upload
            customRequest={handleUpload}
            showUploadList={false}
            accept="image/*"
            fileList={fileList}
            onChange={handleChange}
          >
            <Button icon={<PictureOutlined />} type="text" />
          </Upload>
          
          <Button 
            type="primary" 
            icon={<SendOutlined />} 
            onClick={handleSendTextMessage}
            disabled={!inputValue.trim()}
          >
            发送
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatWindow;