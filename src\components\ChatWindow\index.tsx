import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Input, Button, Upload, Avatar, Spin, message } from 'antd';
import { SendOutlined, PictureOutlined, UserOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { useModel } from '@umijs/max';
import type { ChatMessage } from '@/services/api/chat';
import { formatMessageTime, shouldShowTimeGroup, fileToBase64 } from '@/services/api/chat';
import './index.less';

// 扩展ChatMessage类型以支持本地显示需求
export type DisplayChatMessage = ChatMessage & {
  sender: 'self' | 'other'; // 用于UI显示的发送者类型
};

export type ChatWindowProps = {
  receiverId: string;
  receiverName: string;
  receiverAvatar?: string;
  onSendMessage?: (message: {
    content: string;
    messageType: number; // 1-文字，2-图片
    imageFile?: File;
  }) => Promise<boolean>;
  messages?: DisplayChatMessage[];
  loading?: boolean;
};

const ChatWindow: React.FC<ChatWindowProps> = ({
  receiverId: _receiverId, // 保留参数但标记为未使用
  receiverName,
  receiverAvatar,
  onSendMessage,
  messages = [],
  loading = false,
}) => {
  const { initialState } = useModel('@@initialState');
  const [inputValue, setInputValue] = useState<string>('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);



  // 滚动到最新消息
  const scrollToBottom = useCallback(() => {
    const scrollAction = () => {
      // 方法1: 使用scrollIntoView
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
          inline: 'nearest'
        });
      }

      // 方法2: 直接设置scrollTop（更可靠）
      if (messagesContainerRef.current) {
        const container = messagesContainerRef.current;
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        });
      }
    };

    // 使用requestAnimationFrame确保DOM已更新
    requestAnimationFrame(() => {
      setTimeout(scrollAction, 50);
    });
  }, []);



  // 当消息列表变化时滚动到底部
  useEffect(() => {
    if (messages.length > 0) {
      // 只有在应该自动滚动时才滚动
      scrollToBottom();
    }
  }, [messages, scrollToBottom]);

  // 组件挂载时也滚动到底部
  useEffect(() => {
    if (messages.length > 0) {
      // 初始加载时滚动
      scrollToBottom();
    }
  }, [messages.length, scrollToBottom]);

  // 发送文本消息
  const handleSendTextMessage = () => {
    if (!inputValue.trim()) return;

    if (onSendMessage) {
      onSendMessage({
        content: inputValue,
        messageType: 1, // 1-文字消息
      }).then((success) => {
        if (success) {
          setInputValue('');
          // 发送成功后立即滚动到底部
          setTimeout(() => scrollToBottom(), 50);
        }
      });
    }
  };

  // 处理图片上传
  const handleUpload: UploadProps['customRequest'] = async ({ file, onSuccess, onError }) => {
    if (!file || !onSendMessage) return;

    try {
      // 验证文件类型
      const fileObj = file as File;
      if (!fileObj.type.startsWith('image/')) {
        message.error('只能上传图片文件');
        if (onError) onError(new Error('文件类型不支持'));
        return;
      }

      // 验证文件大小 (限制为10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (fileObj.size > maxSize) {
        message.error('图片大小不能超过10MB');
        if (onError) onError(new Error('文件过大'));
        return;
      }

      // 生成预览URL用于立即显示
      const previewUrl = URL.createObjectURL(fileObj);

      // 先显示预览消息
      const success = await onSendMessage({
        content: previewUrl, // 临时使用预览URL
        messageType: 2, // 2-图片消息
        imageFile: fileObj,
      });

      if (success) {
        if (onSuccess) onSuccess('ok');
        setFileList([]);
        // 上传成功后立即滚动到底部
        setTimeout(() => scrollToBottom(), 50);
      } else {
        // 清理预览URL
        URL.revokeObjectURL(previewUrl);
        if (onError) onError(new Error('上传失败'));
      }
    } catch (error) {
      message.error('图片处理失败');
      if (onError) onError(error as Error);
    }
  };

  // 处理上传变化
  const handleChange = ({ fileList: newFileList }: { fileList: UploadFile[] }) => {
    setFileList(newFileList);
  };

  // 处理按键事件
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendTextMessage();
    }
  };

  return (
    <div className="chat-window">
      <div className="chat-header">
        <div className="chat-title">
          <span>{receiverName}</span>
        </div>
      </div>
      
      <div className="chat-messages" ref={messagesContainerRef}>
        {loading ? (
          <div className="chat-loading">
            <Spin tip="加载消息中..." />
          </div>
        ) : (
          messages.map((msg, index) => {
            const previousMsg = index > 0 ? messages[index - 1] : undefined;
            const showTimeGroup = shouldShowTimeGroup(msg.createTime, previousMsg?.createTime);

            return (
              <div key={msg.messageId || msg.id}>
                {showTimeGroup && (
                  <div className="message-time-group">
                    {formatMessageTime(msg.createTime)}
                  </div>
                )}
                <div
                  className={`message-item ${msg.sender === 'self' ? 'message-self' : 'message-other'}`}
                >
                  {msg.sender === 'other' && (
                    <Avatar
                      src={receiverAvatar}
                      icon={<UserOutlined />}
                      size="large"
                      className="message-avatar"
                    />
                  )}

                  <div className="message-content">
                    {msg.messageType === 1 ? (
                      <div className="message-text">{msg.content}</div>
                    ) : (
                      <div className="message-image">
                        <img src={msg.imageUrl || msg.content} alt="聊天图片" />
                      </div>
                    )}
                  </div>

                  {msg.sender === 'self' && (
                    <Avatar
                      src={initialState?.currentUser?.avatar}
                      icon={<UserOutlined />}
                      size="large"
                      className="message-avatar"
                    />
                  )}
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>
      
      <div className="chat-input">
        <Input.TextArea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder="输入消息..."
          autoSize={{ minRows: 1, maxRows: 4 }}
        />
        
        <div className="chat-actions">
          <Upload
            customRequest={handleUpload}
            showUploadList={false}
            accept="image/*"
            fileList={fileList}
            onChange={handleChange}
          >
            <Button icon={<PictureOutlined />} type="text" />
          </Upload>
          
          <Button 
            type="primary" 
            icon={<SendOutlined />} 
            onClick={handleSendTextMessage}
            disabled={!inputValue.trim()}
          >
            发送
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatWindow;