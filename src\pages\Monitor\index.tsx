import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { queryDataRecord } from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import DetailModal from './components/DetailModal';
import {Select} from 'antd';
import moment from 'moment';
import 'moment/locale/zh-cn';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

const TableList = () => {

  /** 展示数据详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const [printContent, setPrintContent] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [currentRow, setCurrentRow] = useState<MONITOR.DataListItem>();
  const { dicList, fetchDicList } = useModel('dictionary');
  const { dicList2, fetchDicList2 } = useModel('dictionary');

  useEffect(()=>{
    fetchDicList('Scenario');
    fetchDicList2('BusinessType')
  }, [])

  const columns: ProColumns<MONITOR.DataListItem>[]  = [
    {
      title: '日期选择',
      dataIndex: 'timeSelect',
      key: 'timeSelect',
      valueType: "dateRange",
      hideInTable: true,
      fieldProps: {
        // presets: [
        //   { label: '昨天', value: dayjs().add(-1, 'd') },
        //   { label: '上一周', value: dayjs().add(-7, 'd') },
        //   { label: '上一个月', value: dayjs().add(-1, 'month') },
        // ],
        disabledDate: (current: any) => {
          return current && current > moment().endOf('day');
        },
      },
      search: {
        transform: (value: any) => ( { beginTime: value ? `${value[0]} 00:00:00` : '', endTime: value ? `${value[1]} 23:59:59` : '' }),
      },
      initialValue: [ moment().subtract(7, 'day'), moment() ]
    },
    {
      title: '编号',
      dataIndex: 'key',
      key: 'key',
      hideInSearch: true,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '身份证号',
      dataIndex: 'idCard',
      key: 'idCard',
    },
    {
      title: '设备号',
      dataIndex: 'deviceCode',
      key: 'deviceCode',
    },
    {
      title: '核验时间',
      dataIndex: 'verificationTime',
      key: 'verificationTime',
      valueType: "dateTime",
      hideInSearch: true,
    },
    {
      title: '核验类型',
      dataIndex: 'businessType',
      key: 'businessType',
      renderText: (_, record) => {
        return record.businessTypeName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        return (
          <Select {...rest} placeholder="请选择">
            {dicList2 &&
              dicList2.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`businessType${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '场景',
      dataIndex: 'scenario',
      key: 'scenario',
      renderText: (_, record) => {
        return record.scenarioName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        return (
          <Select {...rest} placeholder="请选择">
            {dicList &&
              dicList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`scenario${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '核验信息',
      dataIndex: 'readFaceData',
      key: 'readFaceData',
      hideInSearch: true,
      render: (_, record) => (
        record.brushFaceIDCard && <>
          <div>姓名: {record.brushFaceName}</div>
          <div>身份证：{record.brushFaceIDCard}</div>
        </>
      )
    },
    {
      title: '核验状态',
      dataIndex: 'state',
      valueEnum: {
        '0': {
          text: '核验中',
          status: 'Process',
        },
        '1': {
          text: '核验成功',
          status: 'Success',
        },
        '2': {
          text: '核验失败',
          status: 'Error',
        },
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 100,
      hideInDescriptions: true,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="uid"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => []}
        request={queryDataRecord}
        columns={columns}
      />
      {showDetail && (
        <DetailModal
          modalVisible={showDetail}
          onCancel={() => setShowDetail(false)}
          data={currentRow || {}}
        />
      )}
    </PageContainer>
  );
};

export default TableList;
