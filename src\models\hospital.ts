// 医院管理相关查询接口 处理SELECT中的查询数据
import {
  useState,
  useCallback
} from 'react';
import {
  queryHosList,
  queryDeptList,
  queryClinicList,
  queryDiagAreaListNoPage,
  getDeptInfo,
  getDiagAreaInfo,
  getDoctorInfo,
} from '@/services/api/hospital';
import {
  message
} from 'antd'

const MAX_LENGTH = 999;
export type ListType = {
  value?: any;
  label?: string;
};

export default () => {
  const [hosList, setHosList] = useState<ListType[]>([]);
  const [deptList, setDeptList] = useState<ListType[]>([]);
  const [clinicList, setClinicList] = useState<ListType[]>([]);
  const [digAreaList, setDigAreaList] = useState<ListType[]>([]);
  const [deptStatus, setDeptStatus] = useState<string>();
  const [doctorStatus, setDoctorStatus] = useState<string>();
  const [diagStatus, setDiagStatus] = useState<string>();
  const [deptListByID, setDeptListByID] = useState<ListType[]>();

  // 查询机构列表方法
  const fetchHosList = useCallback(async () => {
    const result = await queryHosList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el:{[key: string]: any }) => {
        return { label: el.hospitalName, value: el.saID}
      });
      setHosList(data)
      return data
    } else {
      message.error('请求机构信息失败,请刷新重试');
      return false
    }
  }, []);
  
  // 查询诊室列表方法
  const fetchClinicList = useCallback(async () => {
    const result = await queryClinicList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data =  result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.roomName,
          value: el.roomID.toString(),
        }
      });
      setClinicList(data)
      return data
    } else {
      message.error('请求诊室信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询科室列表方法
  const fetchDeptList = useCallback(async () => {
    const result = await queryDeptList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.deptName,
          value: el.deptCode,
        };
      });
      setDeptList(data)
      return data
    } else {
      message.error('请求科室信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询科室ID列表方法
  const fetchDeptListByID = useCallback(async () => {
    const result = await queryDeptList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.deptName,
          value: el.deptID,
        };
      });
      setDeptListByID(data)
      return data
    } else {
      message.error('请求科室信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询诊区列表方法
  const fetchDigAreaList = useCallback(async () => {
    const result = await queryDiagAreaListNoPage({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.digAreaName,
          value: el.digID,
        };
      });
      setDigAreaList(data)
      return data
    } else {
      message.error('请求诊区信息失败,请刷新重试');
      return false
    }
  }, []);

   // 查询科室信息
  const fetchDeptStatus = useCallback(async (deptID) => {
    const result = await getDeptInfo({
      deptID
    });
    if (result.code === '0' && result.data) {
      setDeptStatus(result.data)
      return result.data
    } else {
      message.error('请求科室信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询医生信息
  const fetchDoctorStatus = useCallback(async(params) => {
    const result = await getDoctorInfo({
      ...params
    });
    if (result.code === '0' && result.data) {
      setDoctorStatus(result.data)
      return result.data
    } else {
      message.error('请求医生信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询诊区信息
  const fetchDiagStatus = useCallback(async (digAreaCode) => {
    const result = await getDiagAreaInfo({
      digAreaCode
    });
    if (result.code === '0' && result.data) {
      setDiagStatus(result.data)
      return result.data
    } else {
      message.error('请求诊区信息失败,请刷新重试');
      return false
    }
  }, []);

  return {
    hosList,
    deptList,
    clinicList,
    digAreaList,
    deptStatus,
    diagStatus,
    doctorStatus,
    deptListByID,
    fetchHosList,
    fetchClinicList,
    fetchDeptList,
    fetchDigAreaList,
    fetchDeptStatus,
    fetchDiagStatus,
    fetchDoctorStatus,
    fetchDeptListByID
  };
};
