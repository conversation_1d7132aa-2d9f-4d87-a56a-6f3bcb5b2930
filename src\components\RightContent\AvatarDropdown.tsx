import { LogoutOutlined, SettingOutlined, UserOutlined, ToolOutlined, FieldTimeOutlined} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Avatar, Menu, Spin } from 'antd';
import type { ItemType } from 'antd/es/menu/hooks/useItems';
import { stringify } from 'querystring';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, { useCallback, useState} from 'react';
import { updateUserPwd, updateRegCode, updateSysTime } from '@/services/api/system';
import { flushSync } from 'react-dom';
import MD5 from 'js-md5';
import { handleOperateMethod } from '@/utils/index';
import defaultSettings from '../../../config/defaultSettings'
import HeaderDropdown from '../HeaderDropdown';
import PwdModal from './PwdModal';
import CodeModal from './CodeModal';
import styles from './index.less';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

/**
 * @zh-CN 修改密码
 * @param fields
 */
const handleUpdate = (fields: USER.UserListItem) => handleOperateMethod(updateUserPwd, fields, 'update');

/**
 * @zh-CN 修改注册码
 * @param fields
 */
const handleUpdateCode = (fields: any) => handleOperateMethod(updateRegCode, fields, 'update');

/**
 * @zh-CN 修改系统注册时间
 * @param fields
 */
const handleUpdateTime = (fields: any) => handleOperateMethod(updateSysTime, fields, 'update');

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu }) => {
  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = async () => {
    // await outLogin();
    const { search, pathname } = window.location;
    const urlParams = new URL(window.location.href).searchParams;
    /** 此方法会跳转到 redirect 参数所在的位置 */
    const redirect = urlParams.get('redirect');
    // Note: There may be security issues, please note
    if (window.location.pathname !== '/user/login' && !redirect) {
      history.replace({
        pathname: '/user/login',
        search: stringify({
          redirect: pathname.replace(`/${defaultSettings.platfromRoute}`, '') + search,
        }),
      });
    }
  };
  const { initialState, setInitialState } = useModel('@@initialState');

  const [updateModalVisible, handleUpdateModalVisible] = useState(false); // 密码弹出框
  const [updateCodeModalVisible, handleUpdateCodeModalVisible] = useState(false); // 注册码弹出框

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'logout') {
        flushSync(() => {
          setInitialState((s) => ({ ...s, currentUser: undefined, menuData: []}));
        });
        loginOut();
        return;
      }
      if (key === 'modify') {
        handleUpdateModalVisible(true)
        return;
      }
      if(key === 'checkCode') {
        handleUpdateCodeModalVisible(true)
        return;
      } 
      if(key === 'updateTime') {
        handleUpdateTime({});
        return;
      }

      history.push(`/account/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.realName) {
    return loading;
  }

  const menuItems: ItemType[] = [
    ...(menu
      ? [
          {
            key: 'center',
            icon: <UserOutlined />,
            label: '个人中心',
          },
          {
            key: 'settings',
            icon: <SettingOutlined />,
            label: '个人设置',
          },
          {
            type: 'divider' as const,
          },
        ]
      : []),
    {
      key: 'checkCode',
      icon: <ToolOutlined />,
      label: '更新系统注册码',
    },
    {
      key: 'updateTime',
      icon: <FieldTimeOutlined />,
      label: '更新系统记录时间',
    },
    {
      key: 'modify',
      icon: <SettingOutlined />,
      label: '修改密码',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const menuHeaderDropdown = (
    <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick} items={menuItems} />
  );

  return (
    <>
      <HeaderDropdown overlay={menuHeaderDropdown}>
        <span className={`${styles.action} ${styles.account}`}>
          {currentUser.avatar ? <Avatar size="small" className={styles.avatar} src={currentUser.avatar} alt="avatar" /> : <UserOutlined />}
          <span className={`${styles.name} anticon`}>{currentUser.realName}</span>
        </span>
      </HeaderDropdown>
      <PwdModal
        onSubmit={async value => {
          const success = await handleUpdate({
            pwd: MD5(value.pwd),
            newPwd: MD5(value.newPwd),
            userID: currentUser.userID,
            userName: currentUser.userName
          });
          if (success) {
            handleUpdateModalVisible(false);
            loginOut();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
        modalVisible={updateModalVisible}
      />
      <CodeModal
        onSubmit={async value => {
          const success = await handleUpdateCode({
            ...value
          });
          if (success) {
            handleUpdateCodeModalVisible(false);
            loginOut();
          }
        }}
        onCancel={() => {
          handleUpdateCodeModalVisible(false);
        }}
        modalVisible={updateCodeModalVisible}
      />

    </>
  );
};

export default AvatarDropdown;
