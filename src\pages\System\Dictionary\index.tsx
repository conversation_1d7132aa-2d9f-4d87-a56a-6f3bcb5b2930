import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, <PERSON>u } from 'antd';
import { useState, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { ProCard } from '@ant-design/pro-components';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryDictionaryTypeList, queryDictionaryList, updateDictionary, addDictionary } from '@/services/api/system';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { handleOperateMethod } from '@/utils/index';
import { useRequest } from 'ahooks';

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 查询字典类型
 * @param 
 */
const fetchDictionaryTypeList = async () => {
  try {
    const response = await queryDictionaryTypeList({});
    if (response.code === '0') {
      return response.data.map(item => {
        return {
          label: item.typeName,
          key: item.type,
        };
      })
    }
    return false;
  } catch (error) {
    console.log(error)
    return false;
  }
}


/**
 * @zh-CN 添加字典
 * @param fields
 */
const handleAdd = (fields: DICTIONARY.DicListItem) => handleOperateMethod(addDictionary, fields, 'add');
/**
 * @zh-CN 修改字典
 * @param fields
 */
const handleUpdate = (fields: DICTIONARY.DicListItem) => handleOperateMethod(updateDictionary, fields, 'update');
/**
 * @zh-CN 删除字典
 * @param deviceID
 */
const handleRemove = (id?: number) =>
  handleOperateMethod(updateDictionary, { id, state: 9 }, 'delete');
/**
 * @zh-CN 启用字典
 * @param id
 */
const handleStart = (id?: number) =>
handleOperateMethod(updateDictionary, { id, state: 1 }, 'start');

/**
* @zh-CN 停用字典
* @param id
*/
const handleStop = (id?: number) => handleOperateMethod(updateDictionary, { id, state: 0 }, 'stop');

const TableList = () => {

  const { data, runAsync} = useRequest(() => fetchDictionaryTypeList());

  /** 新增字典的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改字典的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示字典详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<DICTIONARY.DicListItem>();
  const [typeCode, setTypeCode] = useState<string>();
  const [] = useState<any>();

  const handleMenuSelect = ({key}) => {
    setTypeCode(key)
  }

  const columns: ProColumns<DICTIONARY.DicListItem>[]  = [
    {
      title: "类型名称",
      dataIndex: "typeName",
      hideInSearch: true,
    },
    {
      title: "类型",
      dataIndex: "type",
      hideInSearch: true,
    },
    {
      title: '内容',
      dataIndex: 'content',
      hideInSearch: true,
    },
    {
      title: '参数值',
      dataIndex: 'code',
      hideInSearch: true,
    },
    {
      title: '排序',
      dataIndex: 'sort',
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 200,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.content}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.id);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                    runAsync()
                  }
                }
              },
            });
          }}
        >
          删除
        </a>
      ],
    },
  ];
  return (
    <PageContainer>
      <ProCard split="vertical">
        <ProCard title="字典类型菜单" colSpan="15%" bodyStyle={{padding: '20px 0', maxHeight: '665px', overflowY: 'scroll'}}>
          <Menu
            onClick={handleMenuSelect}
            // defaultSelectedKeys={[menuListSelect]}
            // defaultOpenKeys={[menuListSelect]}
            // selectedKeys={[menuListSelect]}
            mode="inline"
            items={data}
          />
        </ProCard>
        <ProCard title="" headerBordered>
          <ProTable
            headerTitle={'字段列表'}
            actionRef={actionRef}
            rowKey="key"
            search={false}
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
            }}
            toolBarRender={() => [
              <Button
                type="primary"
                key="primary"
                onClick={() => {
                  handleModalVisible(true);
                }}
              >
                <PlusOutlined /> 新增
              </Button>,
            ]}
            params={{type: typeCode}}
            request={queryDictionaryList}
            columns={columns}
          />
        </ProCard>
      </ProCard>
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value);
            const success = await handleAdd(value);
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
              runAsync()
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, id: currentRow?.id });

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
                runAsync()
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.content && (
          <ProDescriptions
            column={1}
            title={currentRow?.content}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<DICTIONARY.DicListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
