/*
	问卷列表
*/
import React, { useState, useRef, useEffect } from 'react';
import ProTable from '@ant-design/pro-table';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Select } from 'antd';
import { queryGoodValue } from '@/services/question';

const { Option } = Select;

const FavorableRate = (props) => {
	
	const actionRef = useRef();

	const columns = [
        {
			title: '日期',
			dataIndex: 'datea', 
			key: 'datea',
			renderFormItem: (item, { defaultRender, ...rest }) => {
				return (
				  	<Select {...rest} defaultValue="30">
					  	<Option key="7"  value="7">一周内</Option>
						<Option key="21" value="21">三周内</Option>
					  	<Option key="30" value="30">一个月内</Option>
					  	<Option key="90" value="90">一个季度内</Option>
				  	</Select>
				)
			},
		},
        {
			title: '总评数',
			dataIndex: 'zps',
			key: 'zps',
			hideInSearch: true,
		},
		{
			title: '好评率',
			dataIndex: 'haoPingLv',
			key: 'haoPingLv',
			hideInSearch: true,
		},
		{
			title: '好评数量',
			dataIndex: 'hps',
			key: 'hps',
			hideInSearch: true,
		}
	];

	return (
		<PageHeaderWrapper>
			<ProTable
				headerTitle="答卷列表"
				actionRef={actionRef}
				rowKey={record=>record.datea+Math.random()+1}
				pagination={{ pageSize: 10 }}
				tableAlertRender={false}
				columns={columns}
                request={params=> {
					const dayNumber = params.datea ? params.datea : "30";
					let fields = {
						current: params.current,
						pageSize: params.pageSize,
						dayNumber
					}
					return queryGoodValue(fields);
				}}
			/>
           
		</PageHeaderWrapper>
	);
};

export default FavorableRate;
