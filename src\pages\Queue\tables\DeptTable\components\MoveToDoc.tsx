import { ModalForm , ProFormSelect, ProFormText, ProFormDependency} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const MoveToDoc = ({ modalVisible, onCancel, onSubmit, values, mode, mutiValues, deptCode}) => {
    const { fetchDeptDoctorList } = useModel('queue');
    const { fetchDicList } = useModel('dictionary');
    const { deptList, fetchDeptList } = useModel('hospital');

    return (
        <ModalForm
            title={mode === 'mutil'? '批量转诊': '指派医生'}
            layout="horizontal"
            width={640}
            open={modalVisible}
            modalProps={{
                destroyOnClose: true,
                onCancel: () => onCancel(),
                maskClosable: false,
            }}
            onFinish={onSubmit}
            {...formItemLayout}
            initialValues={{ ...values, deptCode, patientIDs: mutiValues.map(item => item.patientID), checkCodes: mutiValues.map(item => item.checkCode)}}
            className="_modal-wrapper"
        >
            
            { mode === 'single' ? <>
                <ProFormText name="patientID" label="患者ID" hidden/>
                <ProFormText name="checkCode" label="患者序号" hidden/> 
                <ProFormText name="patientName" label="患者姓名" readonly/>
                <ProFormText name="deptCode" label="就诊科室" readonly/>
                <ProFormSelect
                    name="doctorCode"
                    label="原就诊医生"
                    placeholder="请选择就诊医生"
                    request={() => fetchDeptDoctorList({deptCode: values.deptCode})}
                    readonly
                />
            </> : <><ProFormText name="patientIDs" label="患者ID" hidden/> 
            <ProFormText name="deptCode" label="就诊科室" hidden/>
            <ProFormText name="checkCodes" label="患者序号"/> 
            </> }
            <ProFormSelect
                name="transferDeptCode"
                label="转诊科室"
                placeholder="请选择转诊科室"
                fieldProps={{
                    showSearch: true
                }}
                request={() => fetchDeptList()}
            />
            <ProFormDependency name={['transferDeptCode']}>
                {({ transferDeptCode }) => {
                    return (
                        <ProFormSelect
                            name="newDoctorCode"
                            label="转诊医生"
                            placeholder="请选择转诊医生"
                            rules={[
                                {
                                    required: true,
                                    message: '转诊医生为必填项',
                                },
                            ]}
                            fieldProps={{
                                showSearch: true
                            }}
                            params={{deptCode: transferDeptCode}}
                            request={async (params) => {
                                const data = await fetchDeptDoctorList({deptCode: params.deptCode})
                                return data.map( (item: any) => { return {
                                    ...item,
                                    // disabled: item.value === values.doctorCode && mode === 'single'
                                }})
                            }}
                        />
                    );
                }}
            </ProFormDependency>
            {/* <ProFormSelect
                name="newDoctorCode"
                label="转诊医生"
                placeholder="请选择转诊医生"
                rules={[
                    {
                        required: true,
                        message: '转诊医生为必填项',
                    },
                ]}
                fieldProps={{
                    showSearch: true
                }}
                request={async () => {
                    const data = await fetchDeptDoctorList( mode === 'single'? {deptCode: values.deptCode} : { deptCode })
                    return data.map( (item: any) => {return {
                        ...item,
                        disabled: item.value === values.doctorCode && mode === 'single'
                    }})
                }}
            /> */}
        </ModalForm>
    );
};

export default MoveToDoc;