import { ProFormText, ProFormSelect} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchDicList } = useModel('dictionary');

  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '角色名称为必填项',
          },
        ]}
        name="roleName"
        label="角色名称"
        placeholder="请输入角色名称"
      />
      <ProFormSelect
        rules={[
          {
            required: true,
            message: '系统类型为必填项',
          },
        ]} 
        name="sysType" 
        label="系统类型"
        placeholder="请输入系统类型"
        request={() => fetchDicList('SysType')}
      />
      <ProFormText name="remark" label="备注" placeholder="请输入备注" />
    </>
  );
};

export default FormList;
