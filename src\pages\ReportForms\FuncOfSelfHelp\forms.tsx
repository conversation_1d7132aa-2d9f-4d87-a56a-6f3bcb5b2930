//报表


import './index.less';

const ReportForms = ({
    data,
    date,
}) => {
   
    // const onShowSizeChange = (current, pageSize) => {
    //     console.log(current, pageSize);
    //     changeSize(current,pageSize)
    // }
    // const onChange = page => {
    //     console.log(page);
    //     changeSize(page,pageSize)

    // };
    return (
        <div className="center">
            <h1 style={{ textAlign: 'center', fontSize: '22px', lineHeight: '35px',marginBottom:0 }}><b>自助机使用统计报表</b></h1>
            {/* <Descriptions>
                    <Descriptions.Item label="机构应用名称" span={2}>台州市中西医结合医院-本部-京威盛自助机</Descriptions.Item>
                    <Descriptions.Item label="日报日期" style={{ textAlign: 'right' }}>2021-04-06</Descriptions.Item>
                </Descriptions> */}
            <div className="search-list-row">
                <span>汇总日期：</span>
                <span>{`${date[0].format('YYYY-MM-DD')} - ${date[1].format('YYYY-MM-DD')}`}</span>
            </div>
            <table className="recon-table">
                <thead>
                    <tr>
                        <th rowSpan={2} style={{ width: 100 }}>操作员</th>
                        <th colSpan={1}>门诊挂号（人次）</th>
                        <th colSpan={1}>门诊缴费（人次）</th>
                        <th colSpan={1}>住院预交款（人次）</th>
                    </tr>
                </thead>
                <tbody className="scroll-tbody-wrapper">
                    {data && data.map((item, index) => {
                        return (
                            <tr key={index}>
                                <td style={{ color: '#000' }}>{item.DeviceCode}</td>
                                <td>{item.ghNum || 0}</td>
                                <td>{item.jfNum || 0}</td>
                                <td>{item.zyyjNum || 0}</td>
                            </tr>
                        )
                    })}
                
                    {/* 合计 */}
                    {/* <tr> */}
                        {/* <td style={{ color: '#000' }}>合计</td> */}

                        {/* <td>{total.finalCountTotalWeiXin}</td> */}
                        {/* <td>{item.finalTotalWeiXin}</td> */}
                        {/* <td>{total.countWeiXinAmount}</td> */}
                        {/* <td>{total.totalWeiXinSuccess}</td> */}
                        {/* <td>{total.countWeiXinAmountRefund}</td> */}
                        {/* <td>{total.totalWeiXinAmountRefund}</td> */}
                        {/* <td>{total.booltotalWeiXin}</td> */}
                    {/* </tr> */}
                    {/* <tr>
                        <td colSpan="17" style={{height: 40}}>
                            <Pagination 
                                defaultCurrent={1} 
                                total={singleDevice.length} 
                                showSizeChanger 
                                onShowSizeChange={onShowSizeChange}
                                onChange={onChange}
                            />

                        </td>
                    </tr> */}
                </tbody>

            </table>
            {/* <Descriptions>
                    <Descriptions.Item label="制表时间">2021-04-13 16:18:52</Descriptions.Item>
                    <Descriptions.Item label="制表"></Descriptions.Item>
                    <Descriptions.Item label="审核"></Descriptions.Item>
                </Descriptions> */}
        </div>


    )
}


export default ReportForms;