import { ProFormText, ProFormSelect} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchDicList3 } = useModel('dictionary');
  const { fetchHosList } = useModel('hospital');
  const { initialState } = useModel('@@initialState');
  return (
    <>
      { initialState.currentUser.saID ? <ProFormText
          initialValue={initialState.currentUser.saID}
          name="saID"
          label="机构名称"
          hidden
        /> : <ProFormSelect
          name="saID"
          label="机构名称"
          rules={[
            {
              required: true,
              message: '机构名称为必填项',
            },
          ]}
          colProps={{ span: 12 }}
          request={() => fetchHosList()}
          showSearch
        />
      }
      <ProFormText
        rules={[
          {
            required: true,
            message: '药品名称为必填项',
          },
        ]}
        name="ypmc"
        label="药品名称"
        placeholder="请输入药品名称"
      />
      <ProFormText 
        rules={[
          {
            required: true,
            message: '医保代码为必填项',
          },
          {
            pattern: /^.{20}$|^.{23}$/,
            message: '医保代码长度为20位或23位'
          }
        ]} 
        name="ybdm" 
        label="医保代码"
        placeholder="请输入医保代码"
      />
      <ProFormText
        name="ypxh"
        label="条形码"
        placeholder="请输入药品条形码"
      />
      <ProFormText
        name="jx"
        label="剂型"
        placeholder="请选择药品剂型"
      />
      <ProFormText
        name="gg"
        label="药品规格"
        rules={[
          {
            required: true,
            message: '药品规格为必填项',
          },
        ]}
        placeholder="请输入药品总规格"
      />
      <ProFormText
        name="ypgg"
        label="小计量规格"
        placeholder="请输入药品小计量规格"
      />
      <ProFormText
        name="dw"
        label="单位"
        // request={() => fetchDicList3('dwType')}
        placeholder="请输入药品包装单位"
        // showSearch
      />
      <ProFormText
        name="jldw"
        label="小计量单位"
        // request={() => fetchDicList3('jldwType')}
        placeholder="请输入药品小计量单位"
        // showSearch
      />
      <ProFormText
        name="yfyl"
        label="用法用量"
        // request={() => fetchDicList3('jldwType')}
        placeholder="请输入用法用量"
        // showSearch
      />
      <ProFormText
        name="sccj"
        label="生产厂家"
        rules={[
          {
            required: true,
            message: '生产厂家为必填项',
          },
        ]}
        placeholder="请输入药品生产厂家"
      />
      {/* <ProFormText
        name="pydm"
        label="拼音代码"
        placeholder="请输入药品拼音代码"
      /> */}
      <ProFormText
        name="state"
        label="状态"
        hidden
      />
    </>
  );
};

export default FormList;
