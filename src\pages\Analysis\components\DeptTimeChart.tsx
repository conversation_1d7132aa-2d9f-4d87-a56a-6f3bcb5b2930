import { useEffect, useRef, memo } from 'react';
import * as echarts from 'echarts'
// 引入柱状图
import 'echarts/lib/chart/bar';
// 引入提示框和标题组件
import 'echarts/lib/component/tooltip';
import { getDeptVisitsAvgTime } from '@/services/api/analysis';
import { useRequest } from 'ahooks';
import styles from '../index.less';

// 请求科室平均时长
const queryDeptVisitsAvgTime = async () => {
    try {
      const response = await getDeptVisitsAvgTime({});

      if (response.code === '0') {
        return response.data;
      }
      return false;
    } catch (error) {
      return false;
    }
};

const EchartsTest = memo(() => {
    const ref = useRef<any>();
    const chartRef = useRef<any>();
    const clearTime = useRef<any>();

    const { data } = useRequest(queryDeptVisitsAvgTime, {
        refreshDeps: [],
        pollingInterval: 30000,
        pollingWhenHidden: true
    });

    const handleResize = () => {
        chartRef.current.resize();
    }

    useEffect(() => {
        if(data?.length > 0){
            chartRef.current = echarts.init(ref.current);
            const series: number[] = [];
            const depts = data.map((v: any) => v.deptName);
            const timeArr = data.map((v: any) => v.avgVisitsTime);
            const obj: any = {
                name: '时长',
                type: 'bar',
                barGap: 0,
                barWidth: '30%',
                data: timeArr,
                color: '#4083E2',
                label: { //柱状图显示数字
                    show: true,
                    position: 'top',
                    color: '#fff',
                    fontSize: 12,
                    formatter: (v: any) => { // 设置圆饼图中间文字排版
                        if (v.value == 0) {
                            return ''
                        } else {
                            return `${v.value}`
                        }
                    }
                },
            }
            series.push(obj)
            // 绘制图表
            const option = {
                itemStyle: {
                    normal: {
                        label: {
                            formatter: "{c}" + "时间",
                            show: true,
                            position: "top",
                            textStyle: {
                                fontWeight: "bolder",
                                fontSize: "12",
                                color: "#fff"
                            }
                        },
                        opacity: 1
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    top: '10%',
                    left: '5%',
                    right: '3%',
                    bottom: '8%',
                    containLabel: true
                },
                yAxis: {
                    type: 'value',
                    name: '单位：分钟',
                    nameLocation: 'end',
                    boundaryGap: [0, 0.1],
                    // boundaryGap: false,
                    axisLine: { // y轴线的颜色以及宽度
                        show: false,
                        lineStyle: {
                            color: "#A4A0DE",
                            width: 1,
                            type: "solid"
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: { //y轴文字的配置
                        show: true,
                        // rotate: 50, //设置倾斜角度，数值 可设置 正负 两种，
                        textStyle: {
                            color: "#A4A0DE",
                        }
                    },
                    splitLine: { //分割线配置
                        show: true,
                        lineStyle: {
                            color: "#232252",
                        }
                    },
                },
                xAxis: {
                    type: 'category',
                    inverse: false, //顺序
                    axisLine: { //y轴线的颜色以及宽度
                        show: false,
                        lineStyle: {
                            color: "#A4A0DE",
                            width: 1,
                            type: "solid"
                        },
                    },
                    splitLine: { //分割线配置
                        show: false,
                        lineStyle: {
                            color: "#A4A0DE",
                        }
                    },
                    axisTick: {
                        show: false,
                    },
                    // boundaryGap: false, //留白
                    axisLabel: { //y轴文字的配置
                        show: true,
                        textStyle: {
                            color: "#A4A0DE",
                        }
                    },
                    data: depts
                },
                // 自动滚动
                series
            };
            chartRef.current.dispatchAction({
                type: 'showTip',
                seriesIndex: 0,
                dataIndex: 0
            });
            let index = 0;
            clearTime.current = setInterval(() => {
                ++index;
                if (index == depts.length) {
                    index = 0;
                }
                chartRef.current.dispatchAction({
                    type: 'showTip',
                    seriesIndex: 0,
                    dataIndex: index
                });
            }, 5000);
            chartRef.current.setOption(option);
            window.addEventListener('resize', handleResize);
        }
        return () => {
            clearInterval(clearTime.current);
            window.removeEventListener('resize', handleResize)
        }
    }, [data])

    return (
        <div className={styles.main2} id="main2" ref={ref}></div>
    );

});

export default EchartsTest;

