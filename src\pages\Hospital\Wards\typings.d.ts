declare namespace DEPT {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 科室详情
    type DeptListItem = {
        hospitalName?:string, // 医院名称
        saID?:string, // saID
        deptName?:string, // 科室名称
        deptID?:number, // 科室ID
        deptCode?:string,  // 科室编码 同HIS
        state?:number, // 状态
        remark?: string, // 备注
        estimatedTime?: string // 科室排队预估时间
        overNum?: number,   // 过号规则
        backNum?: number,   // 回诊规则
        doctorNum?: number  // 患者等候阈值
    };
    // 科室列表
    type DeptList = {
        /** 列表的内容 **/
        data?: DeptListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}