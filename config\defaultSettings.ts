import { Settings as LayoutSettings } from '@ant-design/pro-components';

/**
 * @name
 */
const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
  apiName?: string;
  queueApiName?: string;
  devHost?: string;
  platfromType?: 'SelfService' | 'Queue' | 'PlatformForDrugstore'; // 定义平台类型  SelfService 自助服务管理平台（全功能） Queue 排队叫号  PlatformForDrugstore 药店手持平台
  platfromRoute?: 'hsss-yj' | 'hsss-hzss'
} = {
  navTheme: 'light',
  // 拂晓蓝
  colorPrimary: '#1890ff',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  // title: '妇幼人证核验平台',
  title: '三医联动医保外配处方流转系统',
  // title: '智慧医疗服务管理平台',
  pwa: false,
  iconfontUrl: '',
  apiName: 'hsss-hospital-api-pt',
  queueApiName: 'jws-divisional',
  devHost: 'http://*************:8099', 
  logo: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAN1klEQVRogc1aC5CdZXl+vst/O5c92eydzT2hQUmGAILtEKAgiRSMApqRoU6VptOKgqJjK+OMdpiqHWc62I7KWGhTbJmIFjFEChOjVCRlqh0RS0IQ0CSQbBI2Z8/Zs+fy377v7Xz/+c+yl7N7dhPo+M78e875//+7PN/7vs/3fu+77FLPwz15C8+NVvB5YigxgKGzEBHW2BZ2D/YgLwlHwxg2Eyg49kcF538jgS5J9M0DtcYnK7Fedll3fq8C3h4RjYVK3TXmh/d7XIK0wrAtcDCI8O6R0zCjs4VMwMwBwDYCPsIBubAm84siQk6Kt7vS2ZOVYm2sdTKIFvwTKzPedYKBA1gTE5kBl9pS3qdctl1F6rqQKH4z5sBnImx9drqQakUTgXN5W4/rHcwKsTZMQRiJNSEv+Los52t8okTT2twnQsESW3pc+2VibFBrWpAVdATC0os6vz9djCYc66uuZd+ryExQT5uQ+R4RITRgZzQ1IIljVSHjvOAJsc5o8WxEmsGqiXkAd4JQJUB06NHYAmcMm4d6v+bZ1u0NpRPtLFYiTfAE78459rOq4b8DwEtmORerH/N24iOrLYGxLonzdKqZebzNPDkdRrhwafdd67u7bn/ZDybvL1ZMG6UJxVjlN3Xlfvaxhn/RveXa4cV0o1NLYitsG7v6CmCcUAMDJ2NvGjEBqg2DGR84x3GuGcxm9vmhStYvAWJbUMUyohPFjqxjlCe6srCWD4CiOPGZQcuCzVG8+/DIhYdr/mvdYPOaujFVH8BaAMMcYIyxpONzpMQDfUtgWRIjcQSuFfLMIDZomjOjxBw01uVyz/VY4gJfvWHXulKBc/4Gylx5tdL1WrPTuSbR1YXg4AFWf+pJzjPZpH/jS0ttC0u1PvKT3x67oBjEFW+Gw1FCFMasgWWCwQEwTsDoG/RLsLTCCk6wpWEhCxmy0MeBRhBBxCpprZSGm3E/yC15QV2paQ4cv15G4UMXo+/zd3dysUQm9nwfE997GHy5BzABizFUwghwnFV9y3r/5Xjp9PuVEMkEzTqGiiEjGNZIG3VF+MJYBWUAHm9y++Q+QgzwCTDkUdcEToSaFIhtiQkCjCcwAfQKfkO+DQs1DbWpuk5uPxdDmubVMETB8m56R+/gRtLq+Zgx+LFGTjPUGcMSySCjGD+NKmhMaTtrQ5w6gNGAlAJ5x4YVa3DSsKXMzslQU51jPhZjqZ208SVK/9oRYg6OCWNHSsODQp0YGrHRjpps2hpy1sJOHxAgTZBKNVWslHHOh/Rcc1xobDGPcC5Qqzd2VsPGIVg8IQJFGqqDptsCmbQPSugtJxn/jCf4v2Yt+wuVMNrViMJPOKJN07MAYmgjKwSqcfTQ8Sja0e159zDGX8gK8YQnxBYTQRDNvcPMMq2W1k385Ar5xyTETgvMluau4Fiaz/zlRL2x8WStURvIZv7ZsNikqbH5FTyfZAXHUT/4TkPFO9YXcr8k8E2ktXHmt3mOfW0c87+nMPzUXBY7a2QDQMCEHvZ3XUs+KBmzY1CyYklcRchlXfcw4+z5cd+/RWmd7PJNHIvXiIkockIgUvp7QRh9rM92nuHgm1ohi0pMiyAteWfOth9pEsVsNFOAsKQBgRVWufaBHltsD3QzKJw6PfNbMoY+1/15Q+kT9Ti+iYOlQdvigWQEx4sNf/eJMPrUioz3VF7yCwKtZ5mQiQByUt7Y53l7VdPk2wOJDUMJPrzUdQ45XJwfz+nRTTDGpIZymf/UWjeK9fpWRxjLWwwQguTMkMeeSrX2WUuKH1qSb4jmiYSTReVi6xLH3bvOEmkvKZDWlwszztA5ucwvGGNDZkWazkJQ4+PQ5TFo81lpXZXkapRKKEThE1Sv2r+KsSksVxcMg9dqKNfpsV+XK5+92HP29MfhebVaDeT7zSswV9C8fL+5wTGGsTjG8oy7ddfywUdyaNK88Ru5gQg9UuS/tmLgaUuIgbEwTkKAVkPZP9DUs9Y8aZEg18lni0WWBv5jR6v+ZeE1W/4UwM6FABFr1/2Cdzl39ajwIR/eehWqJLpqOjOhNRaRTvYRQ8vMssCVwvEwwsasd+P9y/vvu+3Y638+ZBHYtwFsXX3OkyKXuWrED5JQAWnsJHp7afmevbG1eo1QxdMcusnJlH4mgxlTIA3ifAK9fUXLslZ1DOmbYxxUx45FVCpuUlKmdE+zLuIMYkk3vf6ZT1L1P/ZwObxs0pxWuzYOlEufe7JY+lu5YfXwp6uefVUYhJMgmhprupMoFAQDuOzp7bTIeSLKL+hc0nznfLlsGbBsGazOLZhY2gPtN+fUCnFGggj9ma4vLw/ET2SXa10bxbNPZyx1udbEzuTg1EkW0idrLe4MJjE/zPHZZYRLCu5HeDWMx/ibEFq8VdKCyix71vbB0+dFFR/hHur/ROwMzuv/z8JEm9MBY9BaHckF0T38f09WfhT4wcNWuxd/d4RgWXrmagvOUWo0vlJu1AP+Elkb8pa1Zd6w+3dBI5Y1y2xMKGVL50O/rCvwbSv6/44kL0Tq7NIxb7EQk3LWBEOl0ZNxLls/UPgHKTVtDBm9GUeJt1SY7bQ1mXoUY10ms10yJjSRmvVCkyIYsTQ0ZwtEuhiaXmifCdtyztv2zDgafvCiHNC1XZGd/6tTYdTM1rWeC7PbahYcfF7L5Su1Gi9NmS2mHEoBmzT3wX+j159X7spkLu4ExgCIG42Xw4MHMhw0TJy/YTZt2lorVip1epRxZ/p9wRiylji2b2T8g+xbLrBmYPDRlRnvvYGaopk0aNSVCpmMOVp5hSnjmB8u5zwIo/DAWP2GS3bev33FrX9260KAjO3b+4NDW6/9Rk/BfljYTk5PNpqySi0xGZtcnvFcjlE6R7P3BUq9khH8nT8aKY7JD/vA9mL5fbuy3mOvcX69auVgW4c+12HMxFZNINNsweIMvib9m5h29APXDwT+rfMimNq2XN7mAiM117uuJ+t9l8AGp4Tw023OBKfSlCDSsIkzkzv+WSkIN1uuEzsmuG29q7V+j1b6+4LzGxTRJBJmz9BnKjzV2LGGf9uSXOadK8uVj1elrDlAdiFAyLaR8/AXMp9zT0Rqa78t93i2WOW3OVRNBcQ5QxSr/SpWl2NmFsXEVSaSmQiCGytRvNPifN5cbpI0ZgyjYXhnt+Dr13ru7eUYiw4QjKIdIT7MwO56plS5sqbUIYfPfe43m3ag2P5KXV1uEqpTyWJSIzw5DTCU/XAHA/02b1tfNNNSM+2dARYYimF4hxRiqMeyPl2NlVkIzdg8s5hDGrEyB6VbYhAv+f5m6Xn7BOcXqRllBsOetaD+9ETArxBmtkxPcyU+/eVmXnUijr407odbtSbfNmmfFLhplhcCrwfhBwJN+W5pfS6aNiCd0W5k0q9rM97N/Z77QLHeuDqM1X95QkxahfHFGHjyxET1CujYMPGsPmbdaZlNqPS+V/2gdyyI/i3J0DOGDGdjz5cn1ta1Xjrg2F/WNC38YR0Tfm2EpRn/qkkCCrmtYFkPHiuPbz7hh/eah8YnJoLoH4MgepdITL79WrUfONVMRFQrBdGfqCA+txbr64t+rSfP6dI+170vqW3MbnjG8UFS3VIKWdd5Tz7rfqUUVj8+HkWDMWG4GkUfTRIP8/TeMWXaTOfTK0ypxzXjGMpl7/DSMgCb/faZZ+hS8ZXCgOe9f20mYypnpzQwYqyh0wp1rOpyxhGBQxjq04Y1UDPBQtuOW+MtRDFtUvLmp5l0F7ESIo4lRLBtwmHOkzrkGQFJmjGeFHvyqgGTbTQMlte0C3Zmy3gUz6w1JlyBWbvZ3DjaSa+U+Hmtct9hv4Es4/AjhnO5jWHLScobczWcE4jxEbNjPjs2jnq9DkuKJCBsqPEHNg8P3rA6n3nfeBA1F9/srINLMPGDRxEde41MBmY+4fk8gkMvQPT2sFa+2Cz4Ks/GI5Xqt28+Mnr/JFoCtnkO/n31MIqRMGFscqCyBIeJOFqKmhOI2RT9IMYrEw3UCLDjpmubFBydGL2p3xn6H9eSFwXpfdHTg/pP97PK7sc75rJNgkYUsrCWNWuIZoG6XAfH6/7Tz7x66pb3AuhPN0xTJ8w3Ajx6fBQFEaPXsRHqCDWKkxonT3MS82iEoaF14hu9U0LuLBFKYaxLQfgH/VzsB2OXJPmnMILoXZJcCxUDwrBRxrbgh+GLvzpy8t1XacIgS/aNyQnWARwbn0i2BbAgeaa1RjbnQazINll2zlUzVYSU41t7KKWU65rdnSEcbTQujZT+8ZlmYYzPdVkWuohOPXv05B+OxnHDlNpeA3Aivcz3okl2G6Y037XGKa2T+1rpNB45i/9FMSxSEBLLmb7GkeKHp0hsKUYR7EUcwLqkRMARfuPoyatf9sNTSzq0jZNFZBgCDPgkjmhp4oyB9EuB5/wIX6zU4HK29eburqc2ZtwrSvHsbXKmJKUJITBkC3zn1ZPv2j3ReGHYVHXnaSNSy/hvAFcA+EB6r0XKZwykRwj8OqjjWxP15PdGzq68POvtH2fsMk1TTo+MweYMVROepzlwV4iEEb90fPS6PaXq/hWGXDrUCE1V+dz0vaDNNvSm/JsT0vis7Puba8SeyNvyWlNfMSBKSp0uNuK717r2HQr4PZmyTLkycf2DxfEnXsTCIxvjF+vmmPRZhxSTHTFT8WIIo/CPtNZ/bYrKnLGoGMU7Xqr7X6cw/n0C7TZ+OuoHmyth9Lih+ATHAq6WtNUagP8DHGOutZZPOwUAAAAASUVORK5CYII=',
  platfromType: 'PlatformForDrugstore', // 定义平台类型
  platfromRoute: 'hsss-yj',   
};

export default Settings;
