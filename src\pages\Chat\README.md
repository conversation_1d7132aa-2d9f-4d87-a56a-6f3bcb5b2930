# 1对1聊天功能

## 功能概述

本模块实现了完整的1对1聊天功能，支持文字消息和图片消息的发送与接收，包含会话管理、消息历史、未读消息提醒等功能。

## 主要特性

### 1. 会话管理
- 自动获取用户的会话列表
- 显示最后一条消息预览
- 显示未读消息数量
- 支持会话搜索功能

### 2. 消息功能
- 发送文字消息
- 发送图片消息
- 消息状态显示（发送中、发送成功、发送失败）
- 消息已读状态管理

### 3. 时间显示
- 智能时间格式化（刚刚、几分钟前、今天、昨天、本周、具体日期）
- 消息时间分组显示（超过5分钟显示时间分隔符）
- 会话列表显示最后消息时间

### 4. 用户体验
- 实时消息滚动到底部
- 支持Enter键发送消息（Shift+Enter换行）
- 图片预览和上传
- 加载状态提示

## 文件结构

```
src/pages/Chat/
├── index.tsx           # 聊天主页面组件
├── index.less          # 样式文件
├── test-data.ts        # 测试数据（开发用）
└── README.md           # 说明文档

src/components/ChatWindow/
├── index.tsx           # 聊天窗口组件
└── index.less          # 样式文件

src/services/api/
└── chat.ts             # 聊天API服务层
```

## API接口

### 会话相关
- `createOrGetConversation` - 创建或获取会话
- `getConversationList` - 获取会话列表
- `getConversationDetail` - 获取会话详情

### 消息相关
- `sendTextMessage` - 发送文字消息
- `sendImageMessage` - 发送图片消息
- `getChatHistory` - 获取聊天记录
- `getMessageDetail` - 获取消息详情
- `markMessagesAsRead` - 标记消息为已读
- `getUnreadCount` - 获取未读消息数

### 工具函数
- `formatMessageTime` - 格式化消息时间显示
- `shouldShowTimeGroup` - 判断是否显示时间分组

## 数据类型

### ChatConversation（会话对象）
```typescript
interface ChatConversation {
  id?: number;
  conversationId: string;
  user1Id: string;
  user2Id: string;
  lastMessageContent?: string;
  lastMessageType?: number; // 1-文字，2-图片
  lastMessageTime?: string;
  otherUserId?: string;
  otherUserName?: string;
  otherUserAvatar?: string;
  unreadCount?: number;
  // ... 其他字段
}
```

### ChatMessage（消息对象）
```typescript
interface ChatMessage {
  id?: number;
  messageId: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  messageType: number; // 1-文字，2-图片
  content: string;
  imageUrl?: string;
  fileSize?: number;
  sendStatus?: number; // 1-发送中，2-发送成功，3-发送失败
  readStatus?: number; // 0-未读，1-已读
  createTime: string;
  // ... 其他字段
}
```

## 使用方法

### 1. 基本使用
```tsx
import ChatPage from '@/pages/Chat';

// 在路由中使用
<Route path="/chat" component={ChatPage} />
```

### 2. 单独使用ChatWindow组件
```tsx
import ChatWindow from '@/components/ChatWindow';

<ChatWindow
  receiverId="device002"
  receiverName="张医生"
  receiverAvatar=""
  onSendMessage={handleSendMessage}
  messages={messages}
  loading={loading}
/>
```

### 3. API调用示例
```tsx
import { 
  getConversationList, 
  sendTextMessage, 
  getChatHistory 
} from '@/services/api/chat';

// 获取会话列表
const conversations = await getConversationList({ userId: 'device001' });

// 发送文字消息
const result = await sendTextMessage({
  senderId: 'device001',
  receiverId: 'device002',
  content: '你好'
});

// 获取聊天记录
const history = await getChatHistory({ 
  conversationId: 'conv_device001_device002_1718776200000' 
});
```

## 配置说明

### 1. 用户标识
系统使用设备号（deviceSn）作为用户唯一标识，从 `initialState.currentUser.deviceSn` 获取。

### 2. 图片上传
- 支持的格式：jpg, jpeg, png, gif, bmp, webp
- 最大文件大小：1024MB
- 上传后返回相对路径，需要拼接服务器地址显示

### 3. 时间格式化
- 1分钟内：显示"刚刚"
- 1小时内：显示"X分钟前"
- 今天：显示"HH:mm"
- 昨天：显示"昨天 HH:mm"
- 本周：显示"周X HH:mm"
- 更早：显示"MM-dd HH:mm"

## 样式定制

### 主要CSS类名
- `.chat-container` - 聊天容器
- `.contacts-card` - 会话列表卡片
- `.chat-card` - 聊天窗口卡片
- `.message-item` - 消息项
- `.message-self` - 自己发送的消息
- `.message-other` - 对方发送的消息
- `.message-time-group` - 时间分组显示

### 自定义样式
可以通过修改 `index.less` 文件来自定义聊天界面的样式。

## 注意事项

1. **网络请求**：所有API调用都包含错误处理，失败时会显示错误提示
2. **消息状态**：消息发送后会立即显示在界面上，发送失败时需要重新发送
3. **图片显示**：图片消息需要拼接完整的服务器地址才能正确显示
4. **实时更新**：目前不支持实时消息推送，需要定期刷新获取新消息
5. **分页加载**：聊天记录支持分页，默认每页20条消息

## 开发调试

### 测试数据
`test-data.ts` 文件包含了模拟的会话和消息数据，可用于开发和测试。

### 错误处理
所有API调用都包含完整的错误处理，错误信息会通过 `message.error()` 显示给用户。

### 日志调试
可以在浏览器控制台查看网络请求和响应数据进行调试。
