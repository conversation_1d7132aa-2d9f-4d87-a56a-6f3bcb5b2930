import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON>, Modal, Drawer, Select} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryClinicList, updateRoom, addRoom } from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { handleOperateMethod } from '@/utils/index';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加科室
 * @param fields
 */
const handleAdd = (fields: CLINIC.ClinicListItem) => handleOperateMethod(addRoom, fields, 'add');
/**
 * @zh-CN 修改科室
 * @param fields
 */
const handleUpdate = (fields: CLINIC.ClinicListItem) => handleOperateMethod(updateRoom, fields, 'update');
/**
 * @zh-CN 删除科室
 * @param deviceID
 */
const handleRemove = (roomID?: number) =>
  handleOperateMethod(updateRoom, { roomID, state: 9 }, 'delete');

const TableList = () => {
  /** 新增诊室的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改诊室的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示诊室详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<CLINIC.ClinicListItem>();
  const { hosList, fetchHosList } = useModel('hospital');
  const { digAreaList, fetchDigAreaList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
    fetchDigAreaList();
  }, []);

  const columns: ProColumns<CLINIC.ClinicListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select {...rest} placeholder="请选择">
            {hosList &&
              hosList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '诊室名称',
      dataIndex: 'roomName',
      key: 'roomName',
    },
    {
      title: '诊室代码',
      dataIndex: 'roomCode',
      key: 'roomCode',
    },
    {
      title: '诊室IP',
      dataIndex: 'roomIP',
      key: 'roomIP',
    },
    {
      title: '诊区名称',
      dataIndex: 'digID',
      renderText: (_, record) => {
        return record.digAreaName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select {...rest} placeholder="请选择">
            {digAreaList &&
              digAreaList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 300,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.roomName}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.roomID);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        request={queryClinicList}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value);
            const success = await handleAdd(value);
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}} 
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, roomID: currentRow?.roomID });

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.roomName && (
          <ProDescriptions
            column={1}
            title={currentRow?.roomName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.roomID,
            }}
            columns={columns as ProDescriptionsItemProps<CLINIC.ClinicListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
