import { useEffect, useRef, memo } from 'react';
import styles from '../index.less';
import { getDeptVisitInfo } from '@/services/api/analysis';
import { useRequest } from 'ahooks';

// 请求科室挂号详情
const queryDeptVisitInfo = async () => {
    try {
      const response = await getDeptVisitInfo({});

      if (response.code === '0') {
        return response.data;
      }
      return false;
    } catch (error) {
      return false;
    }
};

const stateFunc = (value) => {
    if(value >= 20){
        return '繁忙'
    }else if(value < 20 && value >= 10){
        return '一般'
    }else
        return '空闲'
}

const DeptNumTable = memo(() => {
    const ref = useRef<any>();

    const { data } = useRequest(queryDeptVisitInfo, {
        refreshDeps: [],
        pollingInterval: 30000,
        pollingWhenHidden: true
    });

    return (
        <div className={styles.main3} ref={ref}>
            <table>
                <thead>
                    <tr>
                        <th>科室</th>
                        <th>已就诊人次</th>
                        <th>候诊人次</th>
                        <th>出诊医生</th>
                        <th>平均就诊时长</th>
                        <th>繁忙程度</th>
                    </tr>
                </thead>
                <tbody>
                    { data && data?.map((item, index) => {
                        return <tr key={item.deptName} className={index % 2 === 1 ? styles.lineStyle : ''}>
                            <td>{item.deptName}</td>
                            <td>{item.yjzrc}</td>
                            <td>{item.hzrc}</td>
                            <td>{item.czys}</td>
                            <td>{item.avgVisitsTime}</td>
                            <td>{stateFunc(item.hzrc)}</td>
                        </tr>
                    })}
                </tbody>
            </table>
        </div>
    );

});

export default DeptNumTable;