import { ProFormText, ProFormSelect} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

export type formListType = {
  mode: string
}

const FormList: React.FC<formListType>= ({mode}) => {
  const { fetchRoleList } = useModel('system');
  const { fetchHosList, fetchDigAreaList} = useModel('hospital');

  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '登录名为必填项',
          },
        ]}
        name="userName"
        label="登录名"
        placeholder="请输入登录名（登录凭据）"
      />
      <ProFormText 
        rules={[
          {
            required: true,
            message: '用户名称为必填项',
          },
        ]} 
        name="realName" 
        label="用户名称"
        placeholder="请输入用户名称"
      />
      { mode === 'add' && <ProFormText.Password 
        rules={[
          {
            required: true,
            message: '密码为必填项',
          },
        ]} 
        name="pwd" 
        label="密码"
        placeholder="请输入密码"
      />}
      <ProFormText rules={[
          {
            required: true,
            message: '手机号为必填项',
          },
        ]} name="phone" label="手机号" placeholder="请输入手机号" />
      <ProFormSelect
        name="roleID"
        label="角色"
        rules={[
          {
            required: true,
            message: '角色为必填项',
          },
        ]}
        colProps={{ span: 12 }}
        request={() => fetchRoleList()}
      />
      <ProFormSelect
        name="saID"
        label="关联医院"
        rules={[
          {
            required: true,
            message: '医院为必填项',
          },
        ]}
        colProps={{ span: 12 }}
        request={() => fetchHosList()}
      />
      <ProFormSelect
        name="digID"
        label="关联诊区"
        rules={[
          {
            required: true,
            message: '医院为必填项',
          },
        ]}
        colProps={{ span: 12 }}
        request={() => fetchDigAreaList()}
        fieldProps={{
          mode: "multiple"
        }}
      />
    </>
  );
};

export default FormList;
