export default [
  {
    path: '/user',
    layout: false,
    routes: [
      { name: '登录', path: '/user/login', component: './User/Login' },
      { component: './404' },
    ],
  },
  { path: '/welcome', name: '欢迎', icon: 'smile', component: './Welcome' },
  {
    path: '/admin',
    name: '管理页',
    icon: 'crown',
    access: 'canAdmin',
    routes: [
      { path: '/admin', redirect: '/admin/sub-page' },
      { path: '/admin/sub-page', name: '二级管理页', component: './Admin' },
    ],
  },
  {
    name: '首页',
    icon: 'BarChartOutlined',
    path: '/analysis',
    component: './Analysis',
  },
  {
    name: '设备管理',
    icon: 'AppstoreOutlined',
    path: '/devices',
    routes: [
      {
        path: '/devices/terminal',
        name: '终端管理',
        icon: 'DesktopOutlined',
        component: './Devices/Terminal',
      },
      { path: '/devices', redirect: '/devices/terminal' },
    ],
  },
  {
    name: '医院管理',
    icon: 'HomeOutlined',
    path: '/hospital',
    routes: [
      {
        path: '/hospital/home',
        name: '机构管理',
        icon: 'ShopOutlined',
        component: './Hospital/Home',
      },
      {
        path: '/hospital/area',
        name: '诊区管理',
        icon: 'ShopOutlined',
        component: './Hospital/Area',
      },  
      {
        path: '/hospital/wards',
        name: '科室管理',
        icon: 'InsertRowBelowOutlined',
        component: './Hospital/Wards',
      },
      {
        path: '/hospital/clinic',
        name: '诊室管理',
        icon: 'MedicineBoxOutlined',
        component: './Hospital/Clinic',
      },
      {
        path: '/hospital/doctors',
        name: '医生管理',
        icon: 'UserAddOutlined',
        component: './Hospital/Doctors',
      },
      {
        path: '/hospital/patient',
        name: '患者管理',        
        icon: 'TeamOutlined',
        component: './Hospital/Patient',
      },
      { path: '/hospital', redirect: '/hospital/home' },
    ],
  },
  {
    path: '/queue',
    name: '排队管理',
    icon: 'HeartOutlined',
    component: './Queue',
  },
  {
    path: '/mtqueue',
    name: '医技管理',
    icon: 'HeartOutlined',
    component: './MedTecQueue',
  },
  {
    path: '/monitor',
    name: '核验记录',
    icon: 'HeartOutlined',
    component: './Monitor',
  },
  {
    path: '/logs',
    name: '日志管理',
    icon: 'FileZipOutlined',
    routes: [
      {
        path: '/logs/pay',
        name: '支付日志',
        icon: 'PayCircleOutlined',
        component: './LogsPage/PayPage',
      },
      {
        path: '/logs/register',
        name: '挂号记录',
        icon: 'ProfileOutlined',
        component: './LogsPage/RegisterPage',
      },
      {
        path: '/logs/interface',
        name: '接口调用日志',
        icon: 'DeploymentUnitOutlined',
        component: './LogsPage/Interface',
      },
      {
        path: '/logs/operation',
        name: '后台操作日志',
        icon: 'FileUnknownOutlined',
        component: './LogsPage/Operation',
      }
    ],
  },
  {
    name: '统计报表',
    icon: 'FileTextOutlined',
    path: '/reportForms',
    routes: [
      {
        name: '叫号数据汇总',
        icon: 'TableOutlined',
        path: '/reportForms/deptCount',
        component: './ReportForms/DeptCount'
      },
      {
        name: '科室叫号数据',
        icon: 'TableOutlined',
        path: '/reportForms/deptData',
        component: './ReportForms/DeptData'
      },
      {
        name: '医生叫号数据',
        icon: 'TableOutlined',
        path: '/reportForms/queueData',
        component: './ReportForms/QueueData'
      },
    ]
  },
  {
    name: '系统管理',
    icon: 'DesktopOutlined',
    path: '/system',
    routes: [
      {
        path: '/system/user',
        name: '用户管理',
        icon: 'UserSwitchOutlined',
        component: './System/User',
      },
      {
        path: '/system/role',
        name: '角色管理',
        icon: 'KeyOutlined',
        component: './System/RolePage',
      },
      {
        path: '/system/dictionary',
        name: '数据字典',
        icon: 'ConsoleSqlOutlined',
        component: './System/Dictionary',
      },
      {
        path: '/system/update',
        name: '更新管理',
        icon: 'CloudDownloadOutlined',
        component: './System/Update',
      },
      { path: '/system', redirect: '/system/user' },
    ]
  },
  {
    name: '问诊数据',
    icon: 'FileDoneOutlined',
    path: '/consultation',
    component: './Consultation'
  },
  {
    path: '/medicine',
    name: '药品信息',
    icon: 'DisconnectOutlined',
    component: './Medicine',
  },
  {
    path: '/billing',
    name: '开单项目',
    icon: 'BarsOutlined',
    component: './Billing',
  },
  {
    path: '/diagnostic',
    name: '诊断信息',
    icon: 'ReconciliationOutlined',
    component: './Diagnostic',
  },
  {
    path: '/disease',
    name: '病情描述',
    icon: 'SolutionOutlined',
    component: './Disease',
  },
  {
    path: '/chat',
    name: '聊天',
    icon: 'message',
    component: './Chat',
  },
  { path: '/', redirect: '/user/login' },
  { path: '*', layout: false, component: './404' },
];



