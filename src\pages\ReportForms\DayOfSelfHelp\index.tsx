import React,{useEffect, useState} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { <PERSON><PERSON>, Card, DatePicker, Space, message } from 'antd';
import ReportForms from './forms';
import moment from 'moment';
import { queryDayForms, queryDayFormsNew} from '@/services/api/reportForms'
import { parseParam } from "@/utils";
import defaultSettings from '../../../../config/defaultSettings';

/**
 * 查询
 * @param fields
 */

const handleQuery = async fields => {
    const hide = message.loading('正在查询');

    try {
        const response = await queryDayFormsNew({
            ...fields
        });
        if (response.code === '0') {
            hide();
            // message.success('查询成功');
            return response;
        }
        message.error(response.msg);
        return false;
    } catch (error) {
        hide();
        message.error('查询失败请重试！');
        return false;
    }
};


/**
 * 导出Excel
 * @param fields
 */

const handleExportToExcel = async fields => {
    const hide = message.loading('正在导出Excel');

    const formatFields = {
        ...fields,
    }
    try {
        const paramsStr = parseParam(formatFields);
        const aLink = document.createElement('a');
        document.body.appendChild(aLink);
        aLink.style.display = 'none';
        aLink.href = `/${defaultSettings.apiName}/statement/getDailyReportExcelNew.dp?${paramsStr.substr(1)}`;
        aLink.setAttribute('download', '自助机日结报表');
        aLink.click();
        document.body.removeChild(aLink);
        hide();
        return true;
    } catch (error) {
        hide();
        message.error('导出Excel失败请重试！');
        return false;
    }
};

const TableList = ({
    dispatch,
}) => {
    const [dateString, setDateString] = useState(moment().format('YYYY-MM-DD')); 
    const [defaultString, setDefaultString] = useState(moment().format('YYYY-MM-DD'));
    const [current, setCurrent] = useState(1)
    const [pageSize, setPageSize] = useState(999)

    const [dayData, setDayData] = useState({})
        const queryLists = async (fields) => {
        const res = await handleQuery(fields);
        setDayData(res ? res.data : {});
    } 
    
    useEffect(() => {
        queryLists({
            current,
            pageSize,
            date: dateString
        });
    }, [])



    const onChangeDate = (date, dateString) =>{
        // console.log(date);
        // console.log(dateString);
        setDateString(dateString);

    }
    const handleSearch = ()=>{
        // 发请求
        if(!dateString){
            message.info('汇总日期不能为空');
            return;
        }
        queryLists({
            current,
            pageSize,
            date: dateString
        }).then(()=>{
            setDefaultString(dateString)
        })
        
    } 

    // 导出
    const handleExport = ()=>{
        handleExportToExcel({
            current,
            pageSize,
            date: dateString,
        })
    }

    return (
        <PageContainer>
            <Card className="reconciliation-box"
                title={
                    <Space className="content">
                        <div >
                            <span>汇总日期：</span>
                            <DatePicker onChange={onChangeDate} defaultValue={moment(dateString,'YYYY-MM-DD')}/>
                        </div>
                        <Button type="primary" onClick={handleSearch}>查询</Button>
                    </Space>
                }
                extra={
                    <Button type="primary" onClick={handleExport}>导出</Button>
                }
            >
                <ReportForms 
                    data={dayData}
                    date={defaultString}
                />
            </Card>
        </PageContainer>
    )
}


export default TableList