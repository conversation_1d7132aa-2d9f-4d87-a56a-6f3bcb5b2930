import React, { useState } from 'react';
import { Modal, Descriptions, Divider, List, Avatar  } from 'antd';

// const data = [
//     // {
//     //   title: '您就诊得科室',
//     //   answer: "急诊科"
//     // },
//     {
//       title: '1.在本次就医中，您对医务人员的交流过程是否满意？',
//       answer: "满意"
//     },
//     {
//       title: '2.您觉得医院环境舒适干净吗？',
//       answer: "干净"
//     },
//     {
//       title: '3.在就医过程中，医务人员是否友好、尊重？',
//       answer: "尊重"
//     },
//     {
//         title: '4.您对挂号收费感到？',
//         answer: "满意"
//     },
//     {
//         title: '5.您对取药感到？',
//         answer: "满意"
//     },
//     {
//         title: '6.您对排队等候时间感到？',
//         answer: "满意"
//     },
//     {
//         title: '7.您对医生的技术水平感到？',
//         answer: "满意"
//     },
//     {
//         title: '8.您对出，入院手续办理感到？',
//         answer: "满意"
//     },
//     {
//         title: '9.您对接诊医生解说的病情内容感到？',
//         answer: "满意"
//     },
//     {
//         title: '10.您对窗口人员的服务态度感到？',
//         answer: "满意"
//     },
//   ];


const Answers = ({
   data,
   recordObj,
   visible,
   onCancel,

}) => {

    return (
        <Modal 
            title={null}
            visible={visible}
            maskClosable={false}
            width={800}
            onCancel={onCancel}
            footer={null}
            bodyStyle={{
                padding: '45px 40px 45px',
            }}
            className="answer-modal"
        >
            <Descriptions title="答卷信息" contentStyle={{background: "#fafafa"}} className="answer-descriptions">
                <Descriptions.Item label="姓名">{recordObj.PatientName || ""}</Descriptions.Item>
                <Descriptions.Item label="ID">{recordObj.PatientID || ""}</Descriptions.Item>
                <Descriptions.Item label="填写时间">{recordObj.createTime || ""}</Descriptions.Item>
            </Descriptions>
            <Divider style={{margin: "0 0 10px", borderTopWidth: 2 }}/>
            <List
                itemLayout="vertical"
                dataSource={data}
                // split={false}
                style={{padding: "10px"}}
                renderItem={(item,index) => (
                <List.Item>
                    <h3 style={{marginBottom: 0, color: "#000", }}>{item.questionTitle}</h3>
                    <p style={{marginBottom: 0}}>{item.Answer}</p>
                </List.Item>
                )}
            />

        </Modal>
    );
}

export default Answers;
