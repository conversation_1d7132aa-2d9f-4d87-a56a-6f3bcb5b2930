import { memo } from 'react';
import { Card } from 'antd';
import { divisionalCountByHour } from '@/services/api/queue';
import { useRequest } from 'ahooks';
import { Line } from '@ant-design/charts';
import { isEqual } from "lodash-es";

// 请求诊区运营当日就诊人次趋势图
const queryDivisionalCountByHour = async (digAreaCode: string) => {
  try {
    const response = await divisionalCountByHour({
      digAreaCode,
    });
    if (response.code === '0') {
      return response.data;
    }
    return false;
  } catch (error) {
    return false;
  }
};


const DigCountByHour: React.FC<{ code: string }> = ({ code }) => {
  const { data, loading} = useRequest(() => queryDivisionalCountByHour(code), {
    refreshDeps: [code],
		pollingInterval: 10000,
    pollingWhenHidden: true
  });

	const MemoPie: React.FC<any> = memo(
    ({data}) => {
      return <Line 
				data={data}
				xField='hour'
				yField='count'
				point={{
					size: 5,
					shape: 'diamond',
				}}
				meta = {{
					hour: {
						alias: '小时'
					},
					count: {
						alias: '人次'
					}
				}}
			/>
    }, (pre, next) => {
      return isEqual(pre?.data, next?.data);
    }
  );
	
  return (
    <Card title="诊区当日分时段就诊人次趋势图" loading={loading}>
			<MemoPie data={data} />
    </Card>
  );
};

export default DigCountByHour;
