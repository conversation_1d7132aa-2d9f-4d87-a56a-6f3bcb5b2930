import { ModalForm, ProFormText} from '@ant-design/pro-form';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
}

type FormValueType = Partial<USER.UserListItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
};

const PwdForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, onSubmit }) => {

  return (
    <ModalForm
      title="更新注册码"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={onSubmit}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      <ProFormText name="regCode" rules={[{ required: true, message: '注册码为必填项' }]} placeholder="请输入注册码" label="输入注册码" /> 
    </ModalForm>
  );
};

export default PwdForm;