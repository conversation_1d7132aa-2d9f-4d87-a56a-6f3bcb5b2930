// 测试数据文件 - 用于开发和测试聊天功能
import { ChatConversation, ChatMessage } from '@/services/api/chat';

// 模拟会话数据
export const mockConversations: ChatConversation[] = [
  {
    id: 1,
    conversationId: 'conv_device001_device002_1718776200000',
    user1Id: 'device001',
    user2Id: 'device002',
    lastMessageContent: '你好，张医生',
    lastMessageType: 1,
    lastMessageTime: '2025-06-19 10:31:00',
    otherUserId: 'device002',
    otherUserName: '张医生',
    otherUserAvatar: '',
    unreadCount: 2,
    status: 1,
    createTime: '2025-06-19 10:30:00',
    updateTime: '2025-06-19 10:31:00'
  },
  {
    id: 2,
    conversationId: 'conv_device001_device003_1718776300000',
    user1Id: 'device001',
    user2Id: 'device003',
    lastMessageContent: '[图片]',
    lastMessageType: 2,
    lastMessageTime: '2025-06-19 09:45:00',
    otherUserId: 'device003',
    otherUserName: '李护士',
    otherUserAvatar: '',
    unreadCount: 0,
    status: 1,
    createTime: '2025-06-19 09:30:00',
    updateTime: '2025-06-19 09:45:00'
  },
  {
    id: 3,
    conversationId: 'conv_device001_device004_1718776400000',
    user1Id: 'device001',
    user2Id: 'device004',
    lastMessageContent: '请问今天的检查结果出来了吗？',
    lastMessageType: 1,
    lastMessageTime: '2025-06-19 08:20:00',
    otherUserId: 'device004',
    otherUserName: '王主任',
    otherUserAvatar: '',
    unreadCount: 5,
    status: 1,
    createTime: '2025-06-19 08:00:00',
    updateTime: '2025-06-19 08:20:00'
  }
];

// 模拟消息数据
export const mockMessages: Record<string, ChatMessage[]> = {
  'conv_device001_device002_1718776200000': [
    {
      id: 1,
      messageId: 'msg1234567890abcdef',
      conversationId: 'conv_device001_device002_1718776200000',
      senderId: 'device001',
      receiverId: 'device002',
      messageType: 1,
      content: '您好，张医生',
      sendStatus: 2,
      readStatus: 1,
      status: 1,
      createTime: '2025-06-19 10:30:00',
      updateTime: '2025-06-19 10:30:00',
      senderName: '患者',
      senderAvatar: ''
    },
    {
      id: 2,
      messageId: 'msg0987654321fedcba',
      conversationId: 'conv_device001_device002_1718776200000',
      senderId: 'device002',
      receiverId: 'device001',
      messageType: 1,
      content: '你好，有什么可以帮助你的吗？',
      sendStatus: 2,
      readStatus: 0,
      status: 1,
      createTime: '2025-06-19 10:31:00',
      updateTime: '2025-06-19 10:31:00',
      senderName: '张医生',
      senderAvatar: ''
    }
  ],
  'conv_device001_device003_1718776300000': [
    {
      id: 3,
      messageId: 'msg1111222233334444',
      conversationId: 'conv_device001_device003_1718776300000',
      senderId: 'device001',
      receiverId: 'device003',
      messageType: 1,
      content: '李护士，请问今天的检查结果出来了吗？',
      sendStatus: 2,
      readStatus: 1,
      status: 1,
      createTime: '2025-06-19 09:30:00',
      updateTime: '2025-06-19 09:30:00',
      senderName: '患者',
      senderAvatar: ''
    },
    {
      id: 4,
      messageId: 'msg5555666677778888',
      conversationId: 'conv_device001_device003_1718776300000',
      senderId: 'device003',
      receiverId: 'device001',
      messageType: 2,
      content: '[图片]',
      imageUrl: '/uploads/chat/images/sample.jpg',
      fileSize: 102400,
      sendStatus: 2,
      readStatus: 1,
      status: 1,
      createTime: '2025-06-19 09:45:00',
      updateTime: '2025-06-19 09:45:00',
      senderName: '李护士',
      senderAvatar: ''
    }
  ]
};

// 工具函数：生成新的消息ID
export function generateMessageId(): string {
  return 'msg' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 工具函数：生成新的会话ID
export function generateConversationId(user1Id: string, user2Id: string): string {
  return `conv_${user1Id}_${user2Id}_${Date.now()}`;
}

// 工具函数：创建新消息
export function createNewMessage(
  conversationId: string,
  senderId: string,
  receiverId: string,
  content: string,
  messageType: number = 1,
  imageUrl?: string
): ChatMessage {
  return {
    messageId: generateMessageId(),
    conversationId,
    senderId,
    receiverId,
    messageType,
    content,
    imageUrl,
    sendStatus: 2,
    readStatus: 0,
    status: 1,
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString(),
  };
}
