import { request } from '@umijs/max';

/** aliyun 获取token 开启房间 POST /aliyunRTC/generateToken */
export async function queryGenerateToken(params?: {[key: string]: any}) {
    return request(`/wyyx/aliyunRTC/generateToken`, {
      method: 'POST',
      data: {
        ...params
      },
    });
}

/** aliyun 修改房间状态 POST /aliyunRTC/editChannel */
export async function editChannel(params?: {[key: string]: any}) {
  return request(`/wyyx/aliyunRTC/editChannel`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/** aliyun 获取房间状态 POST /aliyunRTC/getChannelStatusByChannel */
export async function getChannelStatusByChannel(params?: {[key: string]: any}) {
  return request(`/wyyx/aliyunRTC/getChannelStatusByChannel`, {
    method: 'POST',
    data: {
      ...params
    },
  });
} 

/** SRS-webrtc 创建房间 POST /srsRTC/createRoom */
export async function createRoom(params?: {[key: string]: any}) {
  return request(`/wyyx/srsRTC/createRoom`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/** SRS-webrtc 修改房间状态 POST /srsRTC/editRoom */
export async function editRoom(params?: {[key: string]: any}) {
  return request(`/wyyx/srsRTC/editRoom`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/** SRS-webrtc 查询房间状态 POST /srsRTC/editRoom */
export async function getRoomStatusByRoomId(params?: {[key: string]: any}) {
  return request(`/wyyx/srsRTC/getRoomStatusByRoomId`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}