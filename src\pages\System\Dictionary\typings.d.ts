declare namespace DICTIONARY {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 字典详情
    type DicListItem = {        
        code?: string, // 代码
        stateName?: string, // 状态名称
        createTime?: string,  // 创建时间
        remark?: string,  // 字段名称
        id?: number,   // id
        state?: number,  // 状态
        sort?: number,  // 排序
        type?: string,  // 字段类别
        key?: number,   // key
        content?: string  // 字段类别
    };
    // 字典列表
    type DicList = {
        /** 列表的内容 **/
        data?: DicListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}