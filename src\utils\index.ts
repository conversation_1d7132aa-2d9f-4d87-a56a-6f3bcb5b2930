import {
  message
} from 'antd';
import defaultSettings from '../../config/defaultSettings';
import CryptoJS from 'crypto-js'

export const parseParam = (param: any, key?: string, encode?: any) => {
  if (param == null) return '';
  let paramStr = '';
  const t = typeof (param);
  if (t === 'string' || t === 'number' || t === 'boolean') {
    paramStr += `&${key}=${(encode == null || encode) ? encodeURIComponent(param) : param}`;
  } else {
    for (let i in param) {
      let k = key == null ? i : key + (param instanceof Array ? '[' + i + ']' : '.' + i);
      paramStr += parseParam(param[i], k, encode);
    }
  }
  return paramStr;
};

export const arrayGroup = (arr: any, key?: string) => {
  console.log(arr, key)
  let map = {};
  let dest = [];
  for (let i = 0; i < arr.length; i++) {
    let ai = arr[i];
    if (!map[ai[key]]) {
      dest.push({
        key: ai[key],
        data: [ai]
      });
      map[ai[key]] = ai;
    } else {
      for (let j = 0; j < dest.length; j++) {
        let dj = dest[j];
        if (dj.key == ai[key]) {
          dj.data.push(ai);
          break;
        }
      }
    }
  }
  return dest;
}

export const getServerFilesHost = () => {
  const {
    NODE_ENV
  } = process.env;
  if (NODE_ENV === 'development') {
    return `${defaultSettings.devHost}/serverFiles/`
  }
  return `http://${window.location.host}/serverFiles/`
}

const OPERATION_TYPE_ARRAY = {
  'add': {
    loading: '正在添加',
    success: '添加成功',
    fail: '添加失败请重试'
  }, // 添加
  'update': {
    loading: '正在修改',
    success: '修改成功',
    fail: '修改失败请重试'
  }, // 修改
  'delete': {
    loading: '正在删除',
    success: '删除成功，即将刷新',
    fail: '删除失败，请重试'
  }, // 删除
  'start': {
    loading: '正在启用',
    success: '启用成功',
    fail: '启用失败，请重试'
  }, // 启用
  'stop': {
    loading: '正在停用',
    success: '停用成功',
    fail: '停用失败，请重试'
  }, // 停用 
  'send': {
    loading: '正在发送',
    success: '发送成功',
    fail: '发送失败，请重试'
  }, // 发送 
  'close': {
    loading: '正在关机',
    success: '关机成功',
    fail: '关机失败，请重试'
  },
  'restart': {
    loading: '正在重启',
    success: '重启成功',
    fail: '重启失败，请重试'
  },  // 重启
  'reset': {
    loading: '正在重置',
    success: '重置成功',
    fail: '重置失败，请重试'
  }, // 重置
  'upload': {
    loading: '正在上传',
    success: '上传成功',
    fail: '上传失败，请重试'
  },
}

/**
 * @zh-CN 页面中操作方法
 * @param method 方法名
 * @param params 参数 对象形式
 * @param type 类型
 * @param loadingMessage 加载提示
 * @param successMessage 成功提示
 * @param failMessage 失败提示
 */
export const handleOperateMethod = async (method: (params: Object) => any, params: Object, type: string, loadingMessage?: string, successMessage ?: string, failMessage ?: string) => {
  const hide = message.loading(type === 'default' ? loadingMessage : OPERATION_TYPE_ARRAY[type].loading);

  try {
    const response = await method(params);
    hide();
    if (response.code === '0') {
      message.success(type === 'default' ? successMessage : OPERATION_TYPE_ARRAY[type].success);
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error(type === 'default' ? failMessage : OPERATION_TYPE_ARRAY[type].fail);
    return false;
  }
}

const SECRET_KEY = CryptoJS.enc.Utf8.parse("jws1236547896325")

export function encrypt(value: string) {
  const iv = CryptoJS.lib.WordArray.random(16)
  const encrypted = CryptoJS.AES.encrypt(value, SECRET_KEY, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Base64.stringify(iv).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '') + 
  ':' + 
  CryptoJS.enc.Base64.stringify(encrypted.ciphertext).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')
}