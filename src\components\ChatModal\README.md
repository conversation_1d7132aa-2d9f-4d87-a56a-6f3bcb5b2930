# ChatModal 组件说明

## 功能概述

ChatModal 是一个基于 Modal 的一对一聊天组件，从原有的 Chat 页面中提取聊天功能，提供更灵活的聊天体验。

## 主要特性

### 1. Modal 形式展示
- 使用 Ant Design Modal 组件作为容器
- 支持响应式设计，适配不同屏幕尺寸
- 可以在任何页面中调用，无需页面跳转

### 2. 一对一聊天
- 专注于单个用户的聊天功能
- 去除了会话列表，简化界面
- 直接指定聊天对象进行对话

### 3. 保留轮询功能
- 保持 1 秒间隔的消息轮询
- 支持实时消息更新
- 页面可见性检测，优化性能

### 4. 完整聊天功能
- 支持文字和图片消息发送
- 消息状态管理（发送中、成功、失败）
- 自动滚动到最新消息
- 消息已读状态处理

## 组件接口

### Props
```typescript
export interface ChatModalProps {
  visible: boolean;           // Modal 显示状态
  onCancel: () => void;       // 关闭 Modal 回调
  receiverId: string;         // 接收者 ID（设备号）
  receiverName: string;       // 接收者姓名
  receiverAvatar?: string;    // 接收者头像
  patientInfo?: {             // 患者信息（可选）
    patientID?: string;
    patientName?: string;
    invoice?: string;
    hospitalName?: string;
  };
}
```

### 使用示例
```tsx
import ChatModal from '@/components/ChatModal';

const [chatModalVisible, setChatModalVisible] = useState(false);
const [chatReceiver, setChatReceiver] = useState(null);

// 打开聊天
const openChat = (receiverId: string, receiverName: string) => {
  setChatReceiver({
    receiverId,
    receiverName,
    receiverAvatar: '',
    patientInfo: {
      patientName: receiverName,
      // ... 其他患者信息
    }
  });
  setChatModalVisible(true);
};

// 渲染组件
<ChatModal
  visible={chatModalVisible}
  onCancel={() => {
    setChatModalVisible(false);
    setChatReceiver(null);
  }}
  receiverId={chatReceiver?.receiverId}
  receiverName={chatReceiver?.receiverName}
  receiverAvatar={chatReceiver?.receiverAvatar}
  patientInfo={chatReceiver?.patientInfo}
/>
```

## 技术实现

### 1. 组件结构
```
ChatModal
├── Modal (Ant Design)
│   ├── Header (标题 + 患者信息)
│   └── Body
│       └── ChatWindow (复用现有聊天窗口组件)
```

### 2. 状态管理
```typescript
const [conversation, setConversation] = useState<ChatConversation | null>(null);
const [messages, setMessages] = useState<DisplayChatMessage[]>([]);
const [loading, setLoading] = useState(false);
const [isPollingEnabled, setIsPollingEnabled] = useState(true);
```

### 3. 生命周期管理
- **Modal 打开时**：初始化会话、加载历史消息、开始轮询
- **Modal 关闭时**：清理状态、停止轮询、释放资源
- **页面隐藏时**：暂停轮询，节省资源

### 4. 轮询机制
```typescript
// 开始轮询
const startMessagePolling = useCallback(() => {
  if (!isPollingEnabled || !conversation) return;
  
  messagePollingRef.current = setInterval(() => {
    loadChatHistory(conversation.conversationId, false);
  }, pollingInterval);
}, [isPollingEnabled, conversation, pollingInterval, loadChatHistory]);

// 停止轮询
const stopMessagePolling = useCallback(() => {
  if (messagePollingRef.current) {
    clearInterval(messagePollingRef.current);
    messagePollingRef.current = null;
  }
}, []);
```

## 与问诊记录集成

### 1. 问诊记录页面修改
```typescript
// 添加状态管理
const [chatModalVisible, setChatModalVisible] = useState(false);
const [chatReceiver, setChatReceiver] = useState(null);

// 打开聊天 Modal
const handleOpenChatModal = (record: CONSULTATION.ItemListItem) => {
  if (!record.deviceCode) {
    message.error('该记录未绑定设备号，无法创建聊天会话');
    return;
  }

  setChatReceiver({
    receiverId: record.deviceCode,
    receiverName: record.patientName || '患者',
    patientInfo: {
      patientID: record.patientID,
      patientName: record.patientName,
      invoice: record.invoice,
      hospitalName: record.hospitalName,
    }
  });

  setChatModalVisible(true);
};
```

### 2. 按钮事件修改
```tsx
// 原来的跳转逻辑
onClick={async () => {
  await handleCreateChatAndNavigate(record);
}}

// 修改为打开 Modal
onClick={() => {
  handleOpenChatModal(record);
}}
```

## 样式设计

### 1. Modal 样式
- 宽度：800px（桌面端）
- 高度：600px
- 响应式：移动端占满屏幕

### 2. 聊天窗口适配
- 隐藏原有的聊天窗口标题
- 使用 Modal 的标题显示聊天对象信息
- 调整消息区域高度适配 Modal

### 3. 患者信息显示
- 在 Modal 标题中显示患者姓名
- 副标题显示医院名称和发票号
- 提供更好的上下文信息

## 优势对比

### 相比原有 Chat 页面
| 特性 | 原有 Chat 页面 | ChatModal 组件 |
|------|---------------|----------------|
| 使用方式 | 页面跳转 | Modal 弹窗 |
| 会话管理 | 会话列表 | 直接指定对象 |
| 界面复杂度 | 复杂（列表+聊天） | 简单（仅聊天） |
| 集成难度 | 需要路由配置 | 直接引入组件 |
| 用户体验 | 需要跳转 | 无缝集成 |
| 资源占用 | 整个页面 | 仅 Modal |

### 使用场景
- ✅ 问诊记录中的快速聊天
- ✅ 客服系统中的一对一咨询
- ✅ 任何需要临时聊天的场景
- ✅ 不需要会话管理的简单聊天

## 性能优化

### 1. 资源管理
- Modal 关闭时自动清理所有状态
- 停止轮询，释放定时器资源
- 使用 `destroyOnClose` 确保组件完全销毁

### 2. 轮询优化
- 页面隐藏时暂停轮询
- 页面显示时恢复轮询
- 避免不必要的网络请求

### 3. 内存管理
- 及时清理图片预览 URL
- 正确管理 useEffect 的清理函数
- 避免内存泄漏

## 相关文件

- `src/components/ChatModal/index.tsx` - 主组件文件
- `src/components/ChatModal/index.less` - 样式文件
- `src/components/ChatModal/README.md` - 说明文档
- `src/pages/Consultation/index.tsx` - 集成示例

## 后续扩展

1. **多人聊天支持**：扩展为支持群聊功能
2. **消息类型扩展**：支持文件、语音等消息类型
3. **聊天记录导出**：提供聊天记录导出功能
4. **消息搜索**：在聊天记录中搜索特定内容
5. **自定义主题**：支持聊天界面主题定制
