import { Menu, Tag} from 'antd';
import { useState, useLayoutEffect, useEffect} from 'react';
import { getDigDeptDoctorList} from '@/services/api/queue';
import { useRequest } from 'ahooks';
import { find } from 'lodash';
  // 请求诊区内科室医生列表
  const queryDigDeptDoctorList = async (digData,digAreaCode) => {
    try {
      const response = await getDigDeptDoctorList({
        digAreaCode
      });
      if (response.code === '0') {
        return formatMenuList(digData, response.data)
      }
      return false;
    } catch (error) {
      console.log(error)
      return false;
    }
  }

  // 处理菜单列表
  const formatMenuList = (digData, data) => {
    const list = [{label: `${digData.label}运营总览`, key: `${digData.value}|dig|${digData.label}`}]
    return list.concat(data.map((item)=>{
      if(item.children && item.children.length > 0){
        const children = item.children.map(i => {
          return {
            label: <>{i.label}&nbsp;<Tag color="blue">在线</Tag></>,
            key: `${i.key}|${i.type}|${i.label}|${i.deptCode}|${i.roomName}`
          }
        })
        return {
          label: item.label,
          key: `${item.key}`,
          children: [
            {
              label: `科室运行总览`,
              key: `${item.key}|${item.type}|${item.label}|${item.deptID}|HASDOC`,
            },
            ...children
            // {
            //   label: `在值医生`,
            //   key: `G${item.key}`,
            //   children: children,
            //   type: 'group'
            // }
          ]
        }
      }else{
        return {
          label: `${item.label}`,
          key: `${item.key}|${item.type}|${item.label}|${item.deptID}|NO_DOC`
        }
      }
    }))
  }
  
  
  const DigSelectMenu = ({
    type,
    code,
    digData,
    handleMenuSelect,
    setMenuListSelect,
    menuListSelect,
    openKeys,
    setOpenKeys
  }) => {
    
    const { data } = useRequest(() => queryDigDeptDoctorList(digData, code), {
      refreshDeps: [code, type],
      pollingInterval: 10000,
      pollingWhenHidden: true
    });

    useLayoutEffect(()=>{
      if(data && !menuListSelect){
        setMenuListSelect(data[0].key)
        handleMenuSelect({key: data[0].key})
      }
    }, [data])

    const onOpenChange = (keys: string[]) => {
      setOpenKeys(keys)
    };

    return (
      <>
        {menuListSelect && 
        <Menu
          onClick={handleMenuSelect}
          defaultSelectedKeys={[menuListSelect]}
          defaultOpenKeys={[menuListSelect]}
          selectedKeys={[menuListSelect]}
          openKeys={openKeys}
          onOpenChange={onOpenChange}
          mode="inline"
          items={data}
          forceSubMenuRender={true}
        />}
      </>
    );
  };
  
  export default DigSelectMenu;
  