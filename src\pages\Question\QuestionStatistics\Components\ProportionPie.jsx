import {Card, Radio} from 'antd';
import React from 'react';
import * as numeral from "numeral";
import {Pie} from './Charts';
import '../index.less';

const salesPieData = [
  {
    x: '公众号',
    y: Math.floor(Math.random() * 100) + 20,
  },
  {
    x: '小程序',
    y: Math.floor(Math.random() * 100) + 20,
  },
  {
    x: '自助机',
    y: Math.floor(Math.random() * 100) + 20,
  },
  {
    x: '其他',
    y: Math.floor(Math.random() * 100) + 20,
  },
];

const ProportionPie = ({
    dropdownGroup,
    salesType,
    loading,
    // salesPieData,
    handleChangeSalesType,
  }) => (
  <Card
    loading={loading}
    bordered={false}
    title="1.您预约挂号的方式为"
    style={{
      height: '100%',
    }}
    className="question-look-piecard"
  >
    <div>
      <Pie
        hasLegend
        subTitle="总计"
        total={numeral(12321).format('0,0')}
        data={salesPieData}
        valueFormat={(value) => value}
        height={248}
        lineWidth={4}
      />
    </div>
  </Card>
);

export default ProportionPie;
