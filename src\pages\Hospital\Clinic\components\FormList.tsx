import { ProFormText, ProFormSelect } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchDigAreaList } = useModel('hospital');
  const { fetchDeptList } = useModel('hospital');

  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '诊室名称为必填项',
          },
        ]}
        name="roomName"
        label="诊室名称"
        placeholder="请输入诊室名称"
      />
      <ProFormText 
        rules={[
          {
            required: true,
            message: '诊室代码为必填项',
          },
        ]} 
        name="roomCode" 
        label="诊室代码"
        placeholder="请输入诊室代码"
      />
      <ProFormText
        name="roomIP"
        label="诊室IP"
        placeholder="请输入诊室IP，多个以英文逗号（,）隔开"
      />
      <ProFormSelect
        name="digID"
        label="关联诊区"
        colProps={{ span: 12 }}
        request={() => fetchDigAreaList()}
      />
      <ProFormSelect
        name="deptCode"
        label="关联默认科室"
        colProps={{ span: 12 }}
        request={() => fetchDeptList()}
        showSearch
      />
      <ProFormText name="remark" label="备注" placeholder="请输入备注" />

    </>
  );
};

export default FormList;
