import React,{useState} from 'react';
import { CheckCircleOutlined, CheckSquareOutlined, FileTextOutlined, PlusOutlined, CloseOutlined, EditOutlined, PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { Button, Input, Space } from 'antd';

const { TextArea } = Input;

const QnTitle = ({
    questionnaireValues,
    setQuestionsnanire
})=>{

    // console.log(questionnaireValues)

    const [isEditable, setIsEditable] = useState(false);
    const [title, setTitle] = useState(questionnaireValues.title || "此处添加问卷标题");

    const handleTitleClick=()=>{
		setIsEditable(true)
	}

    const handleTitleChange=(e)=>{
        // console.log(e)
        setTitle(e.target.value)
        setQuestionsnanire("title", e.target.value)
	}

	const handleTitleBlur=()=>{
        setIsEditable(false)
	}

    return (
        isEditable ? (
        <div 
            className="editTitle" 
            style={{ padding: 3, textAlign: 'center' }} 
            onClick={handleTitleClick}
        >
            <Input 
                style={{ fontSize: 18, fontWeight: 'bold', padding: 30, textAlign: 'center' }} 
                value={title} 
                onChange={handleTitleChange} 
                onBlur={handleTitleBlur} 
            />
        </div>
    ) : (
        <div 
            className="editTitle" 
            style={{ padding: 20, textAlign: 'center' }} 
            onClick={handleTitleClick}
        >
            <h2><strong>{title}</strong></h2>
        </div>
    )
    )
}

const QnRemark = ({
    questionnaireValues,
    setQuestionsnanire,
})=>{

    const [remarkEditable, setRemarkEditable] = useState(false);
    const [remark, setRemark] = useState(questionnaireValues.remark || "此处添加问卷描述");

	const handleRemarkClick=()=>{
        setRemarkEditable(true)
	}

	const handleRemarkChange=(e)=>{
        setRemark(e.target.value)
        setQuestionsnanire("remark", e.target.value)
	}

	const handleRemarkBlur=()=>{
		setRemarkEditable(false)
	}


    return (
        remarkEditable ? (
            <div 
                className="editRemark" 
                style={{ margin: '0 20px 20px 20px', padding: 3 }} 
                onClick={handleRemarkClick}
            >
                <TextArea value={remark} onChange={handleRemarkChange} onBlur={handleRemarkBlur} />
            </div>
        ) : (
            <div 
                className="editRemark" 
                style={{ margin: '0 20px 20px 20px', padding: 20 }} 
                onClick={handleRemarkClick}
            >
                <h4><strong>{remark}</strong></h4>
            </div>
        )
    )
}
const QnActions = ({
    addAreaVisible,
    handleAddInput,
    handleAddRadio,
    handleAddCheckbox,
    handleAddTextArea
})=>{
    return (
        addAreaVisible ? (
            <div style={{ padding: 30, textAlign: 'center', border: '1px solid #eee' }}>
                <Button icon={<EditOutlined />} size="large" onClick={handleAddInput}>填空</Button>
                <Button icon={<CheckCircleOutlined />} size="large" style={{ marginLeft: 16 }} onClick={handleAddRadio}>单选</Button>
                <Button icon={<CheckSquareOutlined />} size="large" style={{ marginLeft: 16 }} onClick={handleAddCheckbox}>多选</Button>
                <Button icon={<FileTextOutlined />} size="large" style={{ marginLeft: 16 }} onClick={handleAddTextArea}>文本</Button>
            </div>
        ) : ''
    )
}

const QnSubmitButton = ({
    detailType,
    handleSubmit,
    handleSave,
    setQuestionsnanire
}) => {
    const [date, setDate] = useState("");
    const disabledDate = (current) => current && current.valueOf() < Date.now();
    
    const handleDatePick=(date, dateString)=>{
        setDate(dateString);
        setQuestionsnanire("date",dateString);
	}


    return (
        <div style={{ padding: 20, textAlign: "center" }}>
            {/* <div style={{ float: 'left' }}>
                <span>问卷截止日期：</span>
                <DatePicker onChange={handleDatePick} disabledDate={disabledDate} />
                <span style={{ marginLeft: 16 }}>你选择的日期为: {date}</span>
            </div> */}
            <Space>
                {/* <Button type="primary" style={{ marginLeft: 16 }} onClick={this.handleReleaseQuestionnaire}>发布问卷</Button> */}
                { detailType === "add" ?
                    <>
                        <Button type="primary" onClick={()=>handleSubmit()}>提交问卷</Button>
                    </> :
                    <>
                        <Button type="primary" onClick={()=>handleSave()} >保存问卷</Button>
                    </>
                }
            </Space>
        </div>
    );
}



export {
    QnActions,
    QnTitle,
    QnRemark,
    QnSubmitButton,

}





