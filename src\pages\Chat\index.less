.chat-container {
  display: flex;
  height: calc(100vh - 150px);
  gap: 16px;
  
  .contacts-card {
    width: 280px;
    display: flex;
    flex-direction: column;
    
    .ant-card-body {
      padding: 12px;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .search-input {
      margin-bottom: 12px;
    }
    
    .contacts-list {
      flex: 1;
      overflow-y: auto;
      
      .contact-item {
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 4px;

        &:hover {
          background-color: #f5f5f5;
        }

        &.selected {
          background-color: #e6f7ff;
        }

        .conversation-preview {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 4px;

          .last-message {
            flex: 1;
            color: #666;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 8px;
          }

          .last-time {
            color: #999;
            font-size: 11px;
            flex-shrink: 0;
          }
        }
      }
    }
  }
  
  .chat-card {
    flex: 1;
    
    .ant-card-body {
      padding: 0;
      height: 100%;
    }
    
    .no-chat-selected {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #999;
      font-size: 16px;
    }
  }
}