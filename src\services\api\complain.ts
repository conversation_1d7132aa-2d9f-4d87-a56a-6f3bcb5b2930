import { request } from '@umijs/max';
import defaultSettings from '../../../config/defaultSettings';

// 获取
// export async function queryEmailUserInfo(params) {
//   return request(`/${defaultSettings.apiName}/email/getEmailUserInfo.sp`, {
//     method: 'POST',
//     data: { ...params}
//   });
// }
// // 添加email推送人信息
// export async function addEmailUserInfo(params) {
//   return request(`/${defaultSettings.apiName}/email/addEmailUserInfo.sp`, {
//     method: 'POST',
//     data: { ...params },
//   });
// }
// // 更新email推送人信息
// export async function updateEmailUserInfo(params) {
//   return request(`/${defaultSettings.apiName}/email/updateEmailUserInfo.sp`, {
//     method: 'POST',
//     data: { ...params },
//   });
// }

// // 删除email推送人信息
// export async function deleteEmailUserInfo(params) {
//   return request(`/${defaultSettings.apiName}/email/deleteEmailUserInfo.sp`, {
//     method: 'POST',
//     data: { ...params },
//   });
// }

// 获取患者投诉列表
export async function queryComplaintList(params) {
  return request(`/${defaultSettings.apiName}/complaintPush/getComplaintList.sp`, {
    method: 'POST',
    data: { ...params}
  });
}

// 处理投诉
export async function updateComplaint(params) {
  return request(`/${defaultSettings.apiName}/complaintPush/updateComplaint.sp`, {
    method: 'POST',
    data: { ...params}
  });
}

// 获取投诉接收人列表
export async function queryComplaintPushList(params) {
  return request(`/${defaultSettings.apiName}/complaintPush/getComplaintPushList.sp`, {
    method: 'POST',
    data: { ...params}
  });
}

// 移除投诉接收人
export async function removeComplaintPush(params) {
  return request(`/${defaultSettings.apiName}/complaintPush/removeComplaintPush.sp`, {
    method: 'POST',
    data: { ...params}
  });
}

// 添加投诉接收人
export async function addComplaintPush(params) {
  return request(`/${defaultSettings.apiName}/complaintPush/addComplaintPush.sp`, {
    method: 'POST',
    data: { ...params}
  });
}

// 更新投诉接收人
export async function updateComplaintPush(params) {
  return request(`/${defaultSettings.apiName}/complaintPush/updateComplaintPush.sp`, {
    method: 'POST',
    data: { ...params}
  });
}


