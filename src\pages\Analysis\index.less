.wrapper{
    margin: -32px -40px;
    background-color: #0f183b;
    .title{
        width: 100%;
        height: 137px;
        background-image: url(../../assets/home/<USER>
        background-size: 100% 100%;
        color: #fff;
        font-size: 38px;
        text-align: center;
    }
    .content{
        padding: 60px 30px 20px;
        --borderColor: #63A1F7;
        .rightContent{
            background: rgba(11,22,50,0.5);
            position: relative;
            height: 860px;
            border: 1px solid #494F5D;
            background: linear-gradient(to left, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat;
            background-size: 4px 40px, 40px 4px, 4px 40px, 40px 4px;
            .subTitle{
                font-size: 28px;
                text-shadow: #4083E2 0 2px 4px;
                color: #fff;
                margin: 20px 0 0 20px;
            }
            .dataTitle{
                margin-top: 30px;
                width: 100%;
                font-size: 20px;
                text-shadow: #4083E2 0 2px 4px;
                color: #fff;
                text-align: center;
            }
            .main1{
                width: 100%;
                height: 480px;
            }
        }
        .midTopContent{
            height: 381px;
            border: 1px solid #494F5D;
            background: rgba(11,22,50,0.5);
            background: linear-gradient(to left, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat;
            background-size: 4px 40px, 40px 4px, 4px 40px, 40px 4px;
            .subTitle{
                margin-top: 30px;
                width: 100%;
                font-size: 20px;
                text-shadow: #4083E2 0 2px 4px;
                color: #fff;
                text-align: center;
            }
            .main2{
                width: 100%;
                height: 330px;
            }
        }
        .midBottomContent{
            height: 441px;
            border: 1px solid #494F5D;
            margin-top: 38px;
            background: rgba(11,22,50,0.5);
            background: linear-gradient(to left, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat;
            background-size: 4px 40px, 40px 4px, 4px 40px, 40px 4px;
            .subTitle{
                margin-top: 30px;
                width: 100%;
                font-size: 20px;
                text-shadow: #4083E2 0 2px 4px;
                color: #fff;
                text-align: center;
            }
            .main3{
                width: 100%;
                margin-top: 33px;
                padding: 10px;
                table{
                    width: 100%;
                    thead{
                        font-size: 15px;
                        color: #fff;
                        background-color: #4083E2;
                        text-align: center;
                        height: 43px;
                    }
                    tbody{
                        color: #fff;
                        text-align: center;
                        tr{
                            height: 47px;
                            &.lineStyle{
                                background-color: rgba(46,98,165, 30%);
                            }
                        }
                    }
                }
            }
        }
        .leftTopContent{
            height: 190px;
            border: 1px solid #494F5D;
            background: rgba(11,22,50,0.5);
            background: linear-gradient(to left, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat;
            background-size: 4px 40px, 40px 4px, 4px 40px, 40px 4px;
            .dataContent{
                display: flex;
                padding: 20px 20px 0;
                .item{
                    width: 100%;
                    text-align: center;
                    margin: 0 auto;
                    .text{
                        font-size: 20px;
                        text-shadow: #4083E2 0 2px 4px;
                        color: #fff;
                        font-weight: bold;
                        text-align: center;
                        margin-bottom: 10px;
                    }
                    .bar{
                        width: 456px;
                        height: 25px;
                        border-radius: 25px;
                        margin: 0 auto;
                        position: relative;
                        &::after{
                            content: '';
                            width: 220px;
                            height: 25px;
                            background: linear-gradient(97deg, rgba(255,255,255,0.0300) 0%, rgba(255,255,255,0.2000) 40%, rgba(255,255,255,0) 100%);
                            opacity: 0.5;
                            transform: skewX(-16deg);
                            position: absolute;
                            top: 0;
                            left: -80px;
                            animation: count 2s infinite ;
                            animation-duration: alternate;
                            animation-timing-function: ease-in;
                        }
                        &.blue{
                            background: linear-gradient(to right, #5087EC, #5087EC00);
                        }
                        &.red{
                            width: 220px;
                            background: linear-gradient(to right, #FF9A9E, #FBC4BC22,#FAD0C401);
                        }
                        &.yellow{
                            width: 220px;
                            background: linear-gradient(to right, #FCA13F, #FCCD8601);
                        }
                    }
                    @keyframes count {
                        to {
                            left: -50px;
                        }
                        from {
                            left: 456px
                        }
                    }
                }
            }
        }
        .leftBottomContent{
            height: 649px;
            border: 1px solid #494F5D;
            margin-top: 20px;
            background: rgba(11,22,50,0.5);
            background: linear-gradient(to left, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) right top no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to bottom, var(--borderColor), var(--borderColor)) left bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat, linear-gradient(to left, var(--borderColor), var(--borderColor)) right bottom no-repeat;
            background-size: 4px 40px, 40px 4px, 4px 40px, 40px 4px;
            display: flex;
            justify-content: row;
            padding: 17px 35px 17px 0;
            .subTitle{
                width: 108px;
                height: 649px;
                font-size: 40px;
                text-shadow: #4083E2 0 2px 4px;
                color: #fff;
                padding: 125px 30px;
            }
            .chartContent{
                flex: 1;
                .main4{
                    width: 100%;
                    height: 322px;
                    background-color: rgba(20, 38, 140, 0.15);
                    border-radius: 10px;
                }
                .main5{
                    margin-top: 9px;
                    width: 100%;
                    height: 286px;
                    background-color: rgba(20, 38, 140, 0.15);
                    border-radius: 10px;
                }
            }
        }
    }

}