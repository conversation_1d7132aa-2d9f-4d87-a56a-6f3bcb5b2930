declare namespace DEVICES {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 设备详情
    type DeviceListItem = {
        deviceType?: number, // 设备类型
        deviceTypeName?: string,  // 设备类型名称
        deviceIP?: string,   // 设备IP
        roleID?: number,     // 设备角色
        roleName?: string,   // 设备角色名称
        errorRemark?: string, // 设备报错提示
        cpu?: string,  // 设备CPU占用
        network?: string,  // 设备网络占用
        diskRemark?: string,  // 设备硬盘状态
        memory?: string,  // 设备内存占用
        remark?: string,  // 备注
        hospitalName?: string,  // 设备绑定机构
        deviceCode?: string,  // 设备编码
        avatar?: string,    // 设备图片
        deviceID?: number,  // 设备ID
        deviceName?: string,  // 设备名称
        roomID?: string[] | string,  // 设备绑定诊室ID
        roomName?: string,  // 设备绑定诊室名称
        deviceAddress?: string,  // 设备地址
        hisDeviceCode?: string,   // 设备HIS编号
        lastOperatingTime?: string,  // 上次操作时间
        state?: number,   // 设备状态
        stateName?: string,   // 设备状态名称
        saID?: string,    // 园区ID
        key?: string,      // key值 同 设备ID
        deptName?: string,  // 设备关联科室名称
        digID?: string,     // 诊区ID
        digName?: string,   // 诊区名称
    };
    type CmdTypeItem = {
        cmdContent?: string,  // 命令类型
        cmdSrc?: string,  // 命令src
        deviceIDs?: string, // 设备ID  多个
        deviceID?: number,
        optType?: number  // 操作类型
    };
    type HardwareItem = {
        state?: string,   // 状态
        content?: string, // 硬件名称
        errorRemark?: string  // 异常情况
    }
    // 设备列表
    type DeviceList = {
        /** 列表的内容 **/
        data?: DeviceListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}