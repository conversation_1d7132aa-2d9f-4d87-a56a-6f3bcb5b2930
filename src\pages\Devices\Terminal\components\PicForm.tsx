import { ModalForm, ProFormTextArea, ProFormSelect } from '@ant-design/pro-form';
import { queryDevicesDetail } from '@/services/api/devices';
import { useRequest } from 'ahooks';
import {Image, message} from 'antd';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
}

type FormValueType = Partial<DEVICES.CmdTypeItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  modalVisible: boolean;
  code: string;
};

/**
 * @zh-CN 请求设备详情
 * @param fields
 */
const handleDeviceDetail = async (code) => {
    try {
      const response = await queryDevicesDetail({ deviceCode: code});
      if (response.code === '0') {
        return response.data;
      }
      message.error(response.msg);
      return false;
    } catch (error) {
      message.error('获取设备信息失败！');
      return false;
    }
};

const PicForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, code}) => {

    const { data, loading, run} = useRequest(() => handleDeviceDetail(code), {
        refreshDeps: [code],
        pollingInterval: 30000,
        pollingWhenHidden: true
    });

  return (
    <ModalForm
      title="设备截屏"
      layout="horizontal"
      width={980}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
        okText: '刷新',
      }}
      onFinish={() => {
        run()
      }}
      {...formItemLayout}
      className="_modal-wrapper"
    >
        <Image
            width={932}
            src={data?.deviceInfo?.devicePic}
        />
    </ModalForm>
  );
};

export default PicForm;