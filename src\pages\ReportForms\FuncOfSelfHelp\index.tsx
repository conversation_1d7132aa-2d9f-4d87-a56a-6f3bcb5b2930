import React,{useEffect, useState} from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import { <PERSON><PERSON>, Card, DatePicker, Space, Descriptions, message } from 'antd';
import ReportForms from './forms';
import moment from 'moment';
import { queryFuncForms } from '@/services/api/reportForms'
import { parseParam, arrayGroup} from "@/utils";
import defaultSettings from '../../../../config/defaultSettings';

const { RangePicker } = DatePicker;
/**
 * 查询
 * @param fields
 */

const handleQuery = async fields => {
    const hide = message.loading('正在查询');

    try {
        const response = await queryFuncForms({
            ...fields
        });
        if (response.code === '0') {
            hide();
            // message.success('查询成功');
            return response;
        }
        message.error(response.msg);
        return false;
    } catch (error) {
        hide();
        message.error('查询失败请重试！');
        return false;
    }
};

const TableList = ({
    dispatch,
}) => {
    const [dateString, setDateString] = useState([moment().subtract('30', 'days'), moment()]);
    const [current, setCurrent] = useState(1)
    const [pageSize, setPageSize] = useState(999)
    const [dayData, setDayData] = useState([])
    const typeArray = ['', 'gh','jf','cz', 'zyyj', 'yb', 'cy']

    const queryLists = async (fields) => {
        const res = await handleQuery(fields);
        
        const data =  arrayGroup( res ? res.data : [], 'DeviceCode').map((item)=>{
            const Obj = { 'DeviceCode': item.key}
            for(let i = 0; i <  item.data.length ; i++){
                Obj[`${typeArray[item.data[i].PayType]}Num`] = item.data[i].PersonCount
            }
            return Obj
        })
        setDayData(data);
    } 
    
    useEffect(() => {
        queryLists({
            current,
            pageSize,
            beginTime: dateString[0].format('YYYY-MM-DD'),
            endTime: dateString[1].format('YYYY-MM-DD')
        });
    }, [])



    const onChangeDate = (date) =>{
        setDateString(date);
    }
    const handleSearch = ()=>{
        // 发请求
        if(!dateString){
            message.info('查询日期不能为空');
            return;
        }
        queryLists({
            current,
            pageSize,
            beginTime: dateString[0].format('YYYY-MM-DD'),
            endTime: dateString[1].format('YYYY-MM-DD')
        })
    }

    return (
        <PageContainer>
            <Card className="reconciliation-box"
                title={
                    <Space className="content">
                        <div >
                            <span>查询日期：</span>
                            <RangePicker onChange={onChangeDate} defaultValue={dateString}/>
                        </div>
                        <Button type="primary" onClick={handleSearch}>查询</Button>
                    </Space>
                }
            >
                <ReportForms 
                    data={dayData}
                    date={dateString}
                />
            </Card>
        </PageContainer>
    )
}


export default TableList