# 图片消息上传功能 - Base64实现

## 功能概述

图片消息上传功能已从FormData方式改为Base64编码方式，提供更好的兼容性和用户体验。

## 主要改进

### 1. Base64编码上传
- 将图片文件转换为Base64字符串
- 通过JSON格式发送到后端
- 包含文件元信息（文件名、大小、类型）

### 2. 即时预览
- 选择图片后立即显示预览
- 上传过程中显示加载状态
- 上传完成后显示最终图片

### 3. 文件验证
- 验证文件类型（只允许图片）
- 验证文件大小（限制10MB）
- 友好的错误提示

### 4. 用户体验优化
- 图片hover效果
- 平滑的加载动画
- 自动滚动到新消息

## API接口变化

### 原接口（FormData）
```typescript
// 旧版本
export async function sendImageMessage(params: {
  senderId: string;
  receiverId: string;
  imageFile: File;
}) {
  const formData = new FormData();
  formData.append('senderId', params.senderId);
  formData.append('receiverId', params.receiverId);
  formData.append('imageFile', params.imageFile);

  return request('/wyyx/chat/message/image', {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
```

### 新接口（Base64）
```typescript
// 新版本
export async function sendImageMessage(params: {
  senderId: string;
  receiverId: string;
  imageFile: File;
}) {
  const base64Data = await fileToBase64(params.imageFile);
  
  return request('/wyyx/chat/message/image', {
    method: 'POST',
    data: {
      senderId: params.senderId,
      receiverId: params.receiverId,
      imageBase64: base64Data,
      fileName: params.imageFile.name,
      fileSize: params.imageFile.size,
      fileType: params.imageFile.type,
    },
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
```

## 工具函数

### fileToBase64
```typescript
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        // 移除data:image/jpeg;base64,前缀，只保留base64数据
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = error => reject(error);
  });
}
```

## 前端处理流程

### 1. 文件选择
```typescript
const handleUpload = async ({ file, onSuccess, onError }) => {
  // 1. 验证文件类型
  if (!file.type.startsWith('image/')) {
    message.error('只能上传图片文件');
    return;
  }

  // 2. 验证文件大小
  if (file.size > 10 * 1024 * 1024) {
    message.error('图片大小不能超过10MB');
    return;
  }

  // 3. 生成预览URL
  const previewUrl = URL.createObjectURL(file);
  
  // 4. 发送消息
  await onSendMessage({
    content: previewUrl,
    messageType: 2,
    imageFile: file,
  });
};
```

### 2. 消息发送
```typescript
const handleSendMessage = async (messageData) => {
  if (messageData.messageType === 2) {
    // 1. 显示临时预览消息
    const tempMessage = {
      messageId: `temp_${Date.now()}`,
      content: '[图片上传中...]',
      imageUrl: messageData.content, // 预览URL
      sendStatus: 1, // 发送中
    };
    setMessages(prev => [...prev, tempMessage]);

    // 2. 调用API上传
    const response = await sendImageMessage({
      senderId: currentUserId,
      receiverId: receiverId,
      imageFile: messageData.imageFile,
    });

    // 3. 移除临时消息，添加真实消息
    setMessages(prev => prev.filter(msg => msg.messageId !== tempMessage.messageId));
    setMessages(prev => [...prev, response.data]);
  }
};
```

## 后端接口要求

### 请求格式
```json
{
  "senderId": "device001",
  "receiverId": "device002", 
  "imageBase64": "iVBORw0KGgoAAAANSUhEUgAA...",
  "fileName": "image.jpg",
  "fileSize": 102400,
  "fileType": "image/jpeg"
}
```

### 响应格式
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": {
    "messageId": "msg1234567890abcdef",
    "conversationId": "conv_device001_device002_1718776200000",
    "senderId": "device001",
    "receiverId": "device002",
    "messageType": 2,
    "content": "[图片]",
    "imageUrl": "/uploads/chat/images/abc123def456.jpg",
    "fileSize": 102400,
    "sendStatus": 2,
    "readStatus": 0,
    "createTime": "2025-06-19 10:32:00"
  }
}
```

## 测试步骤

### 1. 基本上传测试
1. 选择一张图片文件
2. 检查是否立即显示预览
3. 检查上传进度提示
4. 验证最终图片显示

### 2. 文件验证测试
1. 尝试上传非图片文件（应该被拒绝）
2. 尝试上传超大文件（应该被拒绝）
3. 检查错误提示是否友好

### 3. 网络异常测试
1. 断网情况下上传图片
2. 检查错误处理是否正确
3. 验证临时消息是否正确清理

## 注意事项

### 1. 内存管理
- 及时清理预览URL：`URL.revokeObjectURL(previewUrl)`
- 避免大量图片同时加载导致内存溢出

### 2. 文件大小限制
- 前端限制：10MB
- 建议后端也设置相应限制
- Base64编码会增加约33%的数据量

### 3. 错误处理
- 网络错误时清理临时消息
- 提供重试机制
- 友好的错误提示

### 4. 性能考虑
- 大图片可考虑压缩后上传
- 可添加上传进度显示
- 考虑图片懒加载

## 浏览器兼容性

- Chrome: ✅ 完全支持
- Firefox: ✅ 完全支持
- Safari: ✅ 完全支持
- Edge: ✅ 完全支持
- IE11: ⚠️ 需要FileReader polyfill

## 相关文件

- `src/services/api/chat.ts` - API接口和工具函数
- `src/components/ChatWindow/index.tsx` - 图片上传组件
- `src/pages/Chat/index.tsx` - 消息处理逻辑
- `src/components/ChatWindow/index.less` - 图片样式
