import React from 'react';
import { Modal } from 'antd';
import { RollbackOutlined, InfoCircleOutlined } from '@ant-design/icons';

const TipModal = (props)=>{

    // const { visible, onCancel, title, content, width } = props;
    return (
        <Modal 
            {...props}
            title = {<div><InfoCircleOutlined/>{props.title}</div>}
        >
            <p>{props.content}</p>
        </Modal>
    )    

}

export default TipModal;