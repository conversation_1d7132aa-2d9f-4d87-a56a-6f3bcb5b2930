declare namespace LOGS {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 日志详情
    type LogListItem = {
        id: number;   // id 
        state: number;  // 状态
        operatingName?: string;
        operatingType?: string;
        createUserName?: string;
        createTime?: string;
        patientID?: string; // 患者ID
        deviceCode?: string; // 终端编号
    };
    // 设备列表
    type LogList = {
        /** 列表的内容 **/
        data?: LogListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}