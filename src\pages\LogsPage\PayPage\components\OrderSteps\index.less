.pay-order-flowPath-box {
  width: 100%;
  display: flex;
  .line {
    width: calc(50% - 26px);
    height: 1px;
    background-color: #39f;
  }

  .item-flowPath {
    // background-color: pink;
    width: calc(100% / 4);
    text-align: center;
    color: #999999;
    font-size: 16px;
    line-height: 26px;
    display: flex;
    flex-direction: column;
    align-items: center;

    // justify-content: center;
    // border: 1px solid red;
    .circle-box {
      position: relative;
      width: 100%;
      margin: 25px 0;

      .box {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 0 26px;
        background-color: #fff;
        z-index: 3;
      }

      .circle {
        display: inline-block;
        width: 26px;
        height: 26px;
        border-radius: 50%;
        background-color: #39f;
        color: #fff;
        line-height: 26px;
      }

      .line {
        // display: inline-block;
        position: absolute;
        // left: 0;
        // right: 0;
        // width: 100%;
        top: -50%;
        height: 1px;
        background-color: #39f;
        z-index: 2,
      }




    }

    .create-time {
      // line-height: 20px;
      color: #000;
    }


  }

  .item-flowPath:first-child {
    .circle-box {
      .line {
        left: calc(50% + 40px) !important;
        right: 0 !important;
        width: calc(50% - 26px) !important;
      }
    }
  }

  .item-flowPath:not(:first-child) {
    .circle-box {
      .line {
        left: 0;
        right: 0;
        width: 100%;
      }
    }
  }

  .item-flowPath:last-child {
    .circle-box {
      .line {
        left: 0;
        width: calc(50% - 26px);
      }
    }
  }

}