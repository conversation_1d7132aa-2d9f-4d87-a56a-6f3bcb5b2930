import { request } from '@umijs/max';
import defaultSettings from '../../../config/defaultSettings';

/***************** 分诊叫号数据统计接口 *****************/
// 左侧上半部分：门诊总人次
export async function getVisitsNum(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/getVisitsNum`, {
        params: data
    })
}

// 左侧上半部分：今日科室就诊量Top10
export async function getDeptVisitNum(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/getDeptVisitNum`, {
        params: data
    })
}

// 中间上半部分：重点科室平均就诊时长
export async function getDeptVisitsAvgTime(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/getDeptVisitsAvgTime`, {
        params: data
    })
}

// 中间下半部分：各科室就诊人次Top6
export async function getDeptVisitInfo(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/getDeptVisitInfo`, {
        params: data
    })
}

// 右侧上半部分：统计全院科室、医生数据
export async function getHospitalVisitInfo(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/getHospitalVisitInfo`, {
        params: data
    })
}

// 右侧下半部分（第一个）：统计患者初诊、复诊数据
export async function getPatientRegisterTypeInfo(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/getPatientRegisterTypeInfo`, {
        params: data
    })
}


// 右侧下半部分（第二个）：统计患者年龄段就诊数据
export async function getPatientAgeGroupVisits(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/getPatientAgeGroupVisits`, {
        params: data
    })
}

