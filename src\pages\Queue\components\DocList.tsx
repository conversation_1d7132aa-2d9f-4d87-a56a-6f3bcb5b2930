import { ModalForm, ProFormSelect} from '@ant-design/pro-form';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import { queryDeptDoctorList } from '@/services/api/queue';
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

export type TableListItem = {
  doctorName: string;
  doctorCode: string;
  pjhs: number;
  hz: number;
  yjz: number;
  hzz: number;
  zjj: number;
  ygh: number;
};

type FormValueType = Partial<DEPT.DeptListItem>;

type UpdateFormProps = {
	onCancel: (flag?: boolean, formVals?: FormValueType) => void;
	onSubmit: (values: FormValueType) => Promise<void>;
	modalVisible: boolean;
  values: FormValueType;
};

const columns: ProColumns<TableListItem>[] = [
    {
      title: '医生工号',
      width: 80,
      dataIndex: 'doctorCode',
      render: (_) => <a>{_}</a>,
    },
    {
      title: '医生姓名',
      dataIndex: 'doctorName',
      width: 120,
    },
    {
      title: '候诊中',
      width: 80,
      dataIndex: 'hzz',
    },
    {
      title: '已就诊',
      width: 80,
      dataIndex: 'yjz',
    },
    {
      title: '初诊',
      width: 80,
      dataIndex: 'czrs',
    },
    {
      title: '总就诊',
      width: 80,
      dataIndex: 'zjj',
    },
    {
      title: '过号',
      width: 80,
      dataIndex: 'ygh',
    },
    {
      title: '回诊',
      width: 80,
      dataIndex: 'hz',
    },
    {
      title: '平均就诊时长',
      width: 80,
      dataIndex: 'avgVisitsTime',
    },
    {
      title: '平均等待时长',
      width: 80,
      dataIndex: 'avgWaitTime',
    },
    {
      title: '平均在院时间',
      width: 80,
      dataIndex: 'avgInHospTime',
    },
  ];

const SettingsForm: React.FC<UpdateFormProps> = ({ modalVisible, onCancel, onSubmit , values }) => {
  return (
    <ModalForm
      title="科室医生"
      layout="horizontal"
      width={980}
      open={modalVisible}
      modalProps={{
        footer: <Button key="back" onClick={onCancel}>关闭</Button>,
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      {...formItemLayout}
      initialValues={{ ...values }}
      className="_modal-wrapper"
    >
      <ProTable<TableListItem>
        request={queryDeptDoctorList}
        params={{
          deptCode: values.deptCode
        }}
        rowKey="key"
        pagination={false}
        columns={columns}
        search={false}
        dateFormatter="string"
        headerTitle="科室医生数据统计"
      />
    </ModalForm>
  );
};

export default SettingsForm;