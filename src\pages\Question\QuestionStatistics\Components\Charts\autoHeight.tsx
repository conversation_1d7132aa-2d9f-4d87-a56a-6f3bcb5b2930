import React, { useState, useEffect, useRef } from 'react';

interface AutoHeightProps {
  height?: number;
  [key: string]: any;
}

function computeHeight(node: HTMLElement): number {
  const { style } = node;
  style.height = '100%';
  const totalHeight = parseInt(`${getComputedStyle(node).height}`, 10);
  const padding =
    parseInt(`${getComputedStyle(node).paddingTop}`, 10) +
    parseInt(`${getComputedStyle(node).paddingBottom}`, 10);
  return totalHeight - padding;
}

function getAutoHeight(n: HTMLElement | null): number {
  if (!n) {
    return 0;
  }

  const node = n;
  let height = computeHeight(node);
  const { parentNode } = node;

  if (parentNode && parentNode instanceof HTMLElement) {
    height = computeHeight(parentNode);
  }

  return height;
}

function autoHeight<P extends AutoHeightProps>() {
  return (WrappedComponent: React.ComponentType<P>) => {
    const AutoHeightComponent: React.FC<P> = (props) => {
      const [computedHeight, setComputedHeight] = useState(0);
      const rootRef = useRef<HTMLDivElement>(null);

      useEffect(() => {
        const { height } = props;

        if (!height && rootRef.current) {
          let h = getAutoHeight(rootRef.current);
          setComputedHeight(h);

          if (h < 1) {
            // 延迟重新计算，确保DOM已渲染
            setTimeout(() => {
              h = getAutoHeight(rootRef.current);
              setComputedHeight(h);
            }, 100);
          }
        }
      }, [props.height]);

      const { height } = props;
      const h = height || computedHeight;

      return (
        <div ref={rootRef}>
          {h > 0 && <WrappedComponent {...props} height={h} />}
        </div>
      );
    };

    return AutoHeightComponent;
  };
}

export default autoHeight;
