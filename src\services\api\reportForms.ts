import request from 'umi-request';
import defaultSettings from '../../../config/defaultSettings';

// 查询报表
export async function queryDayForms(params: any) {
    return request(`/${defaultSettings.apiName}/statement/getDailyReport.dp`, {
        method: 'GET',
        params,
    })
}

// 查询报表
export async function queryDayFormsNew(params: any) {
    return request(`/${defaultSettings.apiName}/statement/getDailyReportNew.dp`, {
        method: 'GET',
        params,
    })
}

// 异常订单报表查询接口
export async function getAbnormalReport(params: any) {
    return request(`/${defaultSettings.apiName}/statement/getAbnormalReport.dp`, {
        method: 'GET',
        params,
    })
}

// 查询自助机功能报表接口
export async function queryFuncForms(params: any) {
    return request(`/${defaultSettings.apiName}/statement/personTimeStatistics.dp`, {
        method: 'GET',
        params,
    })
}

// 西北妇幼 查询报表接口
export async function queryXBFYFuncForms(params: any) {
    return request(`/${defaultSettings.apiName}/statement/xbfypersonTimeStatistics.dp`, {
        method: 'GET',
        params,
    })
}