import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON>, Modal, Drawer, Select} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryUserList, updateUser, addUser } from '@/services/api/system';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { handleOperateMethod } from '@/utils/index';
import MD5 from 'js-md5';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加用户
 * @param fields
 */
const handleAdd = (fields: USER.UserListItem) => handleOperateMethod(addUser, fields, 'add');
/**
 * @zh-CN 修改用户
 * @param fields
 */
const handleUpdate = (fields: USER.UserListItem) => handleOperateMethod(updateUser, fields, 'update');
/**
 * @zh-CN 删除用户
 * @param deviceID
 */
const handleRemove = (userID?: number) =>
  handleOperateMethod(updateUser, { userID, state: 9 }, 'delete');
/**
 * @zh-CN 用户重置密码
 * @param userID
 */
const handleResetPwd = (userID?: number) =>
  handleOperateMethod(updateUser, { userID, pwd: MD5('123456') }, 'reset');

const TableList = () => {
  /** 新增用户的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改用户的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示用户详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<USER.UserListItem>();
  const { roleList, fetchRoleList } = useModel('system');

  useEffect(() => {
    fetchRoleList();
  }, []);

  const columns: ProColumns<USER.UserListItem>[]  = [
    {
      title: '用户编号',
      dataIndex: 'userID',
      key: 'userID',
      hideInSearch: true,
    },
    {
      title: '登录名',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '用户名称',
      dataIndex: 'realName',
      key: 'realName',
      hideInSearch: true,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
    },
    {
      title: '用户角色',
      dataIndex: 'roleID',
      width: 150,
      renderText: (_, record) => {
        return record.roleName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select {...rest} placeholder="请选择">
            {roleList &&
              roleList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`role${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      hideInForm: true,
      valueEnum: {
        0: {
          text: '未启用',
          status: 'Default',
        },
        1: {
          text: '正常',
          status: 'Processing',
        },
        9: {
          text: '停用',
          status: 'Processing',
        }
      },
    },
    {
      title: '最后登录时间',
      dataIndex: 'lastLoginTime',
      valueType: 'dateTime',
      hideInSearch: true,
      hideInForm: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 300,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a key="resetPwd"
          onClick={() => {
            Modal.confirm({
              title: '确认重置密码',
              icon: <InfoCircleOutlined />,
              content: `是否确认重置${record.realName}的密码?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleResetPwd(record.userID)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              }
            });
          }}
        >重置密码</a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.userName}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.userID);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        request={queryUserList}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value);
            const success = await handleAdd({...value, pwd: MD5(value.pwd), digID: value.digID?.join(',')});
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            console.log(value)
            const success = await handleUpdate({ ...value, digID: value.digID?.join(','), userID: currentRow?.userID });

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.userName && (
          <ProDescriptions
            column={1}
            title={currentRow?.userName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.userID,
            }}
            columns={columns as ProDescriptionsItemProps<USER.UserListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
