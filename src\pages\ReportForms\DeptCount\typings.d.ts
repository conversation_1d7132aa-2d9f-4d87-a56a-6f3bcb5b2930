declare namespace QUEUEDATA {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 日志详情
    type QueueDataListItem = {
        id?: number;
        doctorCode?: number;
        doctorName?: string;
        deptCode?: number;
        deptName?: string;
        hzz?: number;
        yjz?: number;
        zjj?: number;
        ygh?: number;
        hz?: number;
        czrs?: number;
        avgWaitTime?: number;
        avgInHospTime?: number;
        avgVisitsTime?: number;
        reportDate?: number;
        createTime?: number;
        reportDateStr?: string;
        createTimeStr?: string;
    };
    // 列表
    type LogList = {
        /** 列表的内容 **/
        data?: QueueDataListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}