/*
    支付平台--订单详情
*/
import { useState, useEffect } from 'react';
import { useModel } from '@umijs/max';
import { Modal, Descriptions, Card, Collapse, Table, message, Image} from 'antd';
import Refundement from './Refundment';
import { typeArrayFormat } from '../utils';
import { payRefund, queryMoneyboxList} from '@/services/api/logs';
import './index.less';

const { Panel } = Collapse;

export type DetailProps = {
  onCancel: () => void;
  visible: boolean;
  data?: PAYS.PayListItem;
};

/**
 * 查询充值记录
 * @params fields
 */
const fetchMoneyBoxList = async fields => {
    try {
        const response = await queryMoneyboxList(fields);
        if (response.code === '0') {
            return response.data;
        }
        return false;
    } catch (error) {
        return false;
    }
};


/**
 * 处理退款
 * @params fields
 */
const handleRefund = async fields => {
    const hide = message.loading('正在退款');
    try {
        const response = await payRefund(fields);
        if (response.code === '0') {
            hide();
            message.success('退款成功');
            return true;
        }
        message.error(response.msg);
        return false;
    } catch (error) {
        hide();
        message.error('退款失败请重试！');
        return false;
    }
};

const Details: React.FC<DetailProps> = ({
    visible,
    data,
    onCancel,
}) => {
    const { initialState } = useModel('@@initialState');
    const [ refundmentVisible, setRefundmentVisible] = useState(false);
    const [ chargeList, setChargeList ] = useState([])

    const handleOpenRefundModal = () => {
        setRefundmentVisible(true);
    }

    const handleQueryRechargeList = async (outTradeNo) => {
        const res = await fetchMoneyBoxList({
            current: 1,
            pageSize: 999,
            outTradeNo
        })
        if(!res) return
        setChargeList(res)
    } 

    useEffect(()=>{
        handleQueryRechargeList(data.outTradeNo)
    }, [data])

    const onRefund = async values => {
        const obj = {values, saID: data.saID || ''}
        const success = await handleRefund(obj);
        if (success) {
            setRefundmentVisible(false);
        }
    }
    const modalProps = {
        visible: refundmentVisible,
        formValues: {
            outTradeNo: data?.outTradeNo || '',
            refundPeople: initialState.currentUser.realName || '',
            refundPeopleName: initialState.currentUser.userName || '',
        },
        onRefund,
        onCancel: () => setRefundmentVisible(false),
    }

    const columns = [
        {
          title: '设备编号',
          dataIndex: 'deviceCode',
          key: 'deviceCode',
        },
        {
          title: '投币金额',
          dataIndex: 'currencyValue',
          key: 'currencyValue',
          hideInSearch: true,
        },
        {
          title: '时间',
          dataIndex: 'createTime',
          hideInSearch: true,
        },
        {
          title: '现金状态',
          dataIndex: 'stateName',
          key: 'stateName',
          hideInSearch: true,
        },
      ];

    /*
        CashFee: "500" //现金支付金额
        ContactName: "徐振阳"  //联系人名称
        ContactTel: "18767100720"  //联系人电话
        CreateTime: "2021-02-02 18:31:40"  //创建时间
        HospitalName: "山西晋城人民医院"   //单位名称  机构
        ID: 64
        InteriorStatus: "0"  //内部状态  0：支付中   1：支付成功   2：支付失败   3：退款   4：  关闭订单
        NonceStr: "82NXYVXvENH9niYc"   //微信返回的随机字符串
        OperatorID: "jws001"        //操作人id
        OperatorName: "jws001"    //操作人名称
        OutTradeNo: "wx1841025704_2678"      //商户订单编号
        PayFrom: 2    //支付业务类型
        PrepayID: "wx021831402019493f8ad23e526f55db0000"      //交易会话辨识
        QrCode: "weixin://wxpay/bizpayurl?pr=pGwkcJhzz"      //支付二维码地址
        saID: "2c0f5cd1-30c6-47b1-9e78-e4901f3e800c"
        SpbillCreateIp: "*************"     //客户端的真实IP
        State: 1    //状态：1启用    2停用     9删除
        SystemKey: "111"    //系统授权key
        SystemName: "山西"    //系统名称
        TotalFee: "500"    //标价金额  单价为分
        TradeState: "QR_WAIT_BUYER_PAY"     
        TradeStateDesc: "创建支付订单"      //交易状态描述对当前查询订单状态的描述和下一步操作的指引
        TradeType: 1
        WxTradeType: "NATIVE"     //微信交易类型
    */

    return (
        <>
        <Modal
            destroyOnClose
            width={1200}
            open={visible}
            title={'订单信息详情'}
            onCancel={() => onCancel()}
            // bodyStyle={{
            //     maxHeight: '60vh',
            //     overflow: 'auto',
            //     // padding: '10px 0'
            // }}
            footer={null}
            className="order-details-modal"
        >
            <Card className="box2">
                <Collapse defaultActiveKey={['1', '2', '3', '4','5','6']}>
                    <Panel header="订单信息" key="2">
                        <Descriptions>
                            <Descriptions.Item label="支付订单编码">{data.outTradeNo}</Descriptions.Item>
                            <Descriptions.Item label="业务订单编码">{data.businessID}</Descriptions.Item>
                            <Descriptions.Item label="第三方支付流水号">{data.productID}</Descriptions.Item>
                            <Descriptions.Item label="订单名称">{data.subject}</Descriptions.Item>
                            <Descriptions.Item label="订单金额（单位：元）">{data.totalAmount}</Descriptions.Item>
                            <Descriptions.Item label="订单类型">{typeArrayFormat('PAY_TYPE')[data.payType]}</Descriptions.Item>
                            <Descriptions.Item label="订单状态">{typeArrayFormat('PAY_STATE')[data.state]}</Descriptions.Item>
                            <Descriptions.Item label="支付时间">{data.payTime}</Descriptions.Item>
                            <Descriptions.Item label="创建时间">{data.createTime}</Descriptions.Item>
                        </Descriptions>
                    </Panel>
                    <Panel header="付款人" key="3">
                        <Descriptions>
                            <Descriptions.Item label="患者姓名">{data.patientName}</Descriptions.Item>
                            <Descriptions.Item label="患者就诊ID">{data.patientID}</Descriptions.Item>
                        </Descriptions>
                    </Panel>
                    <Panel header="医保信息" key="1">
                        <Descriptions>
                            <Descriptions.Item label="医保统筹支付（单位：元）">{data.medicalAmount}</Descriptions.Item>
                            <Descriptions.Item label="医保个账支付（单位：元）">{data.medicalPrivateAmount}</Descriptions.Item>
                            <Descriptions.Item label="现金支付（单位：元）">{data.privateAmount}</Descriptions.Item>
                        </Descriptions>
                    </Panel>
                    {
                        data.payType === 3 &&
                        <Panel header="投币信息" key="6">
                            <Table size="small"
                                columns={columns}
                                dataSource={chargeList}
                                pagination={false}
                                rowKey="patientID" rowClassName={(record, idx) => {
                                    if (idx % 2 === 1)
                                    return 'bg-row';
                                    return ''
                            }} />
                        </Panel>
                    }
                    <Panel header="其他" key="4">
                        <Descriptions>
                            <Descriptions.Item label="应用类型">{typeArrayFormat('PAY_BUSINESS_TYPE')[data.payBusinessType]}</Descriptions.Item>
                            <Descriptions.Item label="系统类型">{data.sysTypeName}</Descriptions.Item>
                            <Descriptions.Item label="支付终端ip">{data.spbillCreateIp}</Descriptions.Item>
                            <Descriptions.Item label="设备类型">{data.sysTypeName}</Descriptions.Item>
                            <Descriptions.Item label="设备编码">{data.deviceCode}</Descriptions.Item>
                            <Descriptions.Item label="HIS设备编码">{data.hisDeviceCode}</Descriptions.Item>
                            <Descriptions.Item label="HIS流水号">{data.invoiceNO}</Descriptions.Item>
                            <Descriptions.Item label="HIS结算状态">{data.hisState}</Descriptions.Item>
                            <Descriptions.Item label="HIS结算备注">{data.hisRemark}</Descriptions.Item>
                            <Descriptions.Item label="备注">{JSON.stringify(data.remark)}</Descriptions.Item>
                            <Descriptions.Item label="患者签名"><Image src={data.creditsStr} width={250}/></Descriptions.Item>
                        </Descriptions>
                    </Panel>
                    {
                        data.state === "2" &&
                        <Panel header="退款信息" key="5">
                            <Descriptions>
                                {/* <Descriptions.Item label="退款交易单号">{data.OutRefundNo}</Descriptions.Item> */}
                                <Descriptions.Item label="退款时间">{data.refundTime}</Descriptions.Item>
                            </Descriptions>
                        </Panel>
                    }
                </Collapse>
            </Card>
        </Modal>
        { refundmentVisible && <Refundement {...modalProps} />}
        </>
    )
}

export default Details;