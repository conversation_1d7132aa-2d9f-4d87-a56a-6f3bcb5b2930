import React, { useState, useEffect } from 'react';
import { Modal, Table, Checkbox, Collapse, Select } from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Panel } = Collapse;
const { Option } = Select;

interface TopicOption {
  id: number;
  type: string;
  text: string;
  conditions?: string;
}

interface QuestionItem {
  id?: number;
  title: string;
  type: number;
  options?: Array<{ text: string; value?: any }>;
}

interface TopicLogicModalProps {
  visible: boolean;
  onCancel: () => void;
  data?: QuestionItem[];
  topicItemData: QuestionItem;
  onSave?: (logicData: any) => void;
}

const TopicLogicModal: React.FC<TopicLogicModalProps> = ({
  visible,
  onCancel,
  data = [],
  topicItemData,
  onSave,
}) => {
  const [conditions, setConditions] = useState<any[]>([]);
  const [lists, setLists] = useState<TopicOption[]>([]);
  const [unconditionalJump, setUnconditionalJump] = useState<string>();

  useEffect(() => {
    if (topicItemData.type === 3 && topicItemData.options) {
      const tempArr = [...topicItemData.options];
      const newArr: TopicOption[] = [];
      
      tempArr.forEach((item, index) => {
        newArr.push({
          id: index,
          type: "选择",
          text: item.text,
        });
      });

      setLists(newArr);
    }
  }, [topicItemData]);

  /**
   * 处理选项跳转变化
   */
  const handleOptionJumpChange = (value: string, record: TopicOption, index: number) => {
    const newLists = [...lists];
    newLists[index] = { ...record, conditions: value };
    setLists(newLists);
  };

  /**
   * 处理无条件跳转变化
   */
  const handleUnconditionalJumpChange = (value: string) => {
    setUnconditionalJump(value);
  };

  /**
   * 保存跳题逻辑
   */
  const handleSave = () => {
    const logicData = {
      questionId: topicItemData.id,
      optionJumps: lists.filter(item => item.conditions).map(item => ({
        optionText: item.text,
        jumpTo: item.conditions,
      })),
      unconditionalJump,
    };
    
    onSave?.(logicData);
    onCancel();
  };

  const columns: ColumnsType<TopicOption> = [
    {
      title: "",
      dataIndex: "type",
      key: "type",
      width: "15%",
      render: () => <span>选择</span>
    },
    {
      title: '选项',
      dataIndex: 'text',
      key: 'text',
      width: "30%",
    },
    {
      title: '跳转到',
      dataIndex: 'conditions',
      key: 'conditions',
      render: (text: string, record: TopicOption, index: number) => (
        <Select 
          style={{ width: "100%" }} 
          placeholder="请选择要跳转到的题目" 
          allowClear
          value={record.conditions}
          onChange={(value) => handleOptionJumpChange(value, record, index)}
        >
          <Option value="不跳转，按顺序填写下一道题">
            不跳转，按顺序填写下一道题
          </Option>
          <Option value="跳到问卷末尾结束作答">
            跳到问卷末尾结束作答
          </Option>
          <Option value="直接提交为无效答卷">
            直接提交为无效答卷
          </Option>
          {data.map((item, idx) => (
            <Option key={idx} value={item.title}>
              {item.title}
            </Option>
          ))}
        </Select>
      )
    },
  ];

  return (
    <Modal
      open={visible}
      title="跳题逻辑"
      width={600}
      onCancel={onCancel}
      onOk={handleSave}
      bodyStyle={{ height: 400, overflow: "auto" }}
      className="topic-logic-modal"
      okText="保存"
      cancelText="取消"
    >
      <Collapse
        bordered={false}
        defaultActiveKey={['1']}
        expandIcon={({ isActive }) => (
          <Checkbox checked={isActive} />
        )}
        className="topic-logic-collapse"
        accordion 
        ghost
      >   
        {topicItemData.type === 3 && (
          <Panel 
            header="按选项跳题" 
            key="1" 
            className="site-collapse-custom-panel"
          >
            <Table<TopicOption>
              columns={columns}
              dataSource={lists}
              bordered={true}
              size="small"
              rowKey="id"
              pagination={false}
            />
          </Panel>
        )}
        
        <Panel 
          header="无条件跳题" 
          key="2" 
          className="site-collapse-custom-panel"
        >
          <div className="no-condition-box">
            <span className="title">填写此题后跳转到</span>
            <Select 
              style={{ width: 250 }}
              placeholder="请选择要跳转到的题目" 
              allowClear
              value={unconditionalJump}
              onChange={handleUnconditionalJumpChange}
            >
              <Option value="跳到问卷末尾结束作答">
                跳到问卷末尾结束作答
              </Option>
              {data.map((item, index) => (
                <Option key={index} value={item.title}>
                  {item.title}
                </Option>
              ))}
            </Select>
          </div>
        </Panel>
      </Collapse>
    </Modal>
  );
};

export default TopicLogicModal;
