import React, { useState, useEffect } from 'react';
import { Input, Button, Radio, Checkbox, Modal, Card, message, Space } from 'antd';
import { CheckCircleOutlined, FileTextOutlined } from '@ant-design/icons';

const { TextArea } = Input;

interface QuestionItem {
  id: string;
  type: 'radio' | 'checkbox' | 'text';
  title: string;
  options?: string[];
  value?: any;
  text?: string;
  required?: boolean;
}

interface QuestionnaireData {
  title: string;
  description: string;
  questions: QuestionItem[];
}

const FillLegacy: React.FC = () => {
  // 从localStorage获取数据
  const getStoredData = (): QuestionnaireData => {
    try {
      const editing = localStorage.getItem('editing');
      return editing ? JSON.parse(editing) : {
        title: '问卷调查',
        description: '请认真填写以下问题',
        questions: []
      };
    } catch {
      return {
        title: '问卷调查',
        description: '请认真填写以下问题',
        questions: []
      };
    }
  };

  const [questionnaireData, setQuestionnaireData] = useState<QuestionnaireData>(getStoredData());
  const [submitting, setSubmitting] = useState(false);

  /**
   * 处理单选题变化
   */
  const handleRadioChange = (e: any, questionIndex: number) => {
    const newData = { ...questionnaireData };
    newData.questions[questionIndex].value = e.target.value;
    setQuestionnaireData(newData);
  };

  /**
   * 处理多选题变化
   */
  const handleCheckboxChange = (checkedValues: any[], questionIndex: number) => {
    const newData = { ...questionnaireData };
    newData.questions[questionIndex].value = checkedValues;
    setQuestionnaireData(newData);
  };

  /**
   * 处理文本题变化
   */
  const handleTextChange = (e: any, questionIndex: number) => {
    const newData = { ...questionnaireData };
    newData.questions[questionIndex].text = e.target.value;
    setQuestionnaireData(newData);
  };

  /**
   * 提交问卷
   */
  const handleSubmitQuestionnaire = () => {
    const { questions } = questionnaireData;
    let answered = 0;
    let unnecessary = 0;

    // 检查答题情况
    questions.forEach((item) => {
      switch (item.type) {
        case 'radio':
          if (item.value && item.value !== '') {
            answered++;
          }
          break;
        case 'checkbox':
          if (item.value && item.value.length > 0) {
            answered++;
          }
          break;
        case 'text':
          if (item.text && item.text.trim() !== '') {
            answered++;
          }
          break;
        default:
          unnecessary++;
      }
    });

    const totalQuestions = questions.length - unnecessary;
    
    if (answered < totalQuestions) {
      message.warning(`还有 ${totalQuestions - answered} 道题未完成，请完成后再提交`);
      return;
    }

    setSubmitting(true);
    
    // 模拟提交
    setTimeout(() => {
      message.success('提交成功，感谢您的参与！');
      setSubmitting(false);
      
      // 清除localStorage数据
      localStorage.removeItem('editing');
      
      // 重置表单
      setQuestionnaireData({
        title: '问卷调查',
        description: '请认真填写以下问题',
        questions: []
      });
    }, 1000);
  };

  /**
   * 渲染问题
   */
  const renderQuestion = (question: QuestionItem, questionIndex: number) => {
    const optionStyle: React.CSSProperties = {
      display: 'block',
      height: '32px',
      lineHeight: '32px',
      marginBottom: 8,
    };

    let questionComponent;

    switch (question.type) {
      case 'radio':
        questionComponent = (
          <Radio.Group 
            onChange={(e) => handleRadioChange(e, questionIndex)}
            value={question.value}
            style={{ width: '100%' }}
          >
            {question.options?.map((option, optionIndex) => (
              <Radio style={optionStyle} value={option} key={optionIndex}>
                {option}
              </Radio>
            ))}
          </Radio.Group>
        );
        break;

      case 'checkbox':
        questionComponent = (
          <Checkbox.Group 
            onChange={(checkedValues) => handleCheckboxChange(checkedValues, questionIndex)}
            value={question.value}
            style={{ width: '100%' }}
          >
            {question.options?.map((option, optionIndex) => (
              <Checkbox style={optionStyle} value={option} key={optionIndex}>
                {option}
              </Checkbox>
            ))}
          </Checkbox.Group>
        );
        break;

      case 'text':
        questionComponent = (
          <TextArea
            placeholder="请输入您的答案..."
            value={question.text}
            onChange={(e) => handleTextChange(e, questionIndex)}
            rows={4}
            maxLength={500}
            showCount
          />
        );
        break;

      default:
        questionComponent = <div>未知问题类型</div>;
    }

    return (
      <Card 
        key={question.id || questionIndex}
        style={{ 
          marginBottom: 24,
          border: '1px solid #f0f0f0',
          borderRadius: 8,
        }}
      >
        <div style={{ marginBottom: 16 }}>
          <span style={{ 
            background: '#1890ff', 
            color: 'white', 
            padding: '4px 8px', 
            borderRadius: 4, 
            marginRight: 12,
            fontSize: 12,
          }}>
            Q{questionIndex + 1}
          </span>
          <span style={{ fontSize: 16, fontWeight: 500 }}>
            {question.title}
          </span>
          {question.required && (
            <span style={{ color: '#ff4d4f', marginLeft: 4 }}>*</span>
          )}
        </div>
        {questionComponent}
      </Card>
    );
  };

  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      {/* 问卷标题和描述 */}
      <Card style={{ marginBottom: 24, textAlign: 'center' }}>
        <h1 style={{ fontSize: 24, marginBottom: 16 }}>
          {questionnaireData.title}
        </h1>
        <p style={{ fontSize: 16, color: '#666', margin: 0 }}>
          {questionnaireData.description}
        </p>
      </Card>

      {/* 问题列表 */}
      {questionnaireData.questions.length > 0 ? (
        <div>
          {questionnaireData.questions.map((question, questionIndex) => 
            renderQuestion(question, questionIndex)
          )}
          
          {/* 提交按钮 */}
          <div style={{ 
            textAlign: 'center', 
            marginTop: 40,
            paddingTop: 24,
            borderTop: '1px solid #f0f0f0',
          }}>
            <Button 
              type="primary" 
              size="large"
              icon={<CheckCircleOutlined />}
              loading={submitting}
              onClick={handleSubmitQuestionnaire}
            >
              提交问卷
            </Button>
          </div>
        </div>
      ) : (
        <Card>
          <div style={{ 
            textAlign: 'center', 
            padding: '60px 20px',
            color: '#999',
          }}>
            <FileTextOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div style={{ fontSize: 16, marginBottom: 8 }}>暂无问题</div>
            <div style={{ fontSize: 12 }}>
              请先在问卷配置中添加问题
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default FillLegacy;
