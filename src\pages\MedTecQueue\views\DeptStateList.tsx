import { StatisticCard } from '@ant-design/pro-components';
import { getDeptDivisionalInfo } from '@/services/api/queue';
import { useRequest } from 'ahooks';
import styles from '../index.less';

// 请求诊区运营总览
const queryDeptDivisionalInfo = async (deptCode: string) => {
  try {
    const response = await getDeptDivisionalInfo({
      deptCode,
    });
    if (response.code === '0') {
      return response.data;
    }
    return false;
  } catch (error) {
    return false;
  }
};

const DigAreaList: React.FC<{ code: string }> = ({ code }) => {
  const { data } = useRequest(() => queryDeptDivisionalInfo(code), {
    refreshDeps: [code],
    pollingInterval: 5000,
    pollingWhenHidden: true
  });

  return (
    <>
      <StatisticCard.Group gutter={16}>
        <StatisticCard
          bordered
          statistic={{
            title: <span className={styles.deptBlockTitle}>总就诊人数</span>,
            value: data?.totalNum,
            icon: (
              <div className={styles.icon} style={{ backgroundColor: '#5B8FF9' }}>
                总
              </div>
            ),
          }}
        />
        <StatisticCard
          bordered
          statistic={{
            title: <span className={styles.deptBlockTitle}>候诊中人数</span>,
            value: data?.waitingNum,
            icon: (
              <div className={styles.icon} style={{ backgroundColor: '#78D3F8' }}>
                候
              </div>
            ),
          }}
        />
        <StatisticCard
          bordered
          statistic={{
            title: <span className={styles.deptBlockTitle}>已就诊人数</span>,
            value: data?.overNum,
            icon: (
              <div className={styles.icon} style={{ backgroundColor: '#24d8ae' }}>
                已
              </div>
            ),
          }}
        />
        <StatisticCard
          bordered
          statistic={{
            title: <span className={styles.deptBlockTitle}>过号人数</span>,
            value: data?.haveNoNum,
            icon: (
              <div className={styles.icon} style={{ backgroundColor: '#F6903D' }}>
                过
              </div>
            ),
          }}
        />
        {/* <StatisticCard
          bordered
          statistic={{
            title: <span className={styles.deptBlockTitle}>回诊人数</span>,
            value: data?.returnNum,
            icon: (
              <div className={styles.icon} style={{ backgroundColor: '#ffa39e' }}>
                回
              </div>
            ),
          }}
        /> */}
      </StatisticCard.Group>
    </>
  );
};

export default DigAreaList;
