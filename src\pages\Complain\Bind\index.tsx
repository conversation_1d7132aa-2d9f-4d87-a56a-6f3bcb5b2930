import {
  PlusOutlined,
  InfoCircleOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { Button, Modal, Drawer, Select, message, Popconfirm } from 'antd';
import { useState, useRef, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryComplaintPushList, removeComplaintPush, addComplaintPush, updateComplaintPush } from '@/services/api/complain';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { handleOperateMethod } from '@/utils/index';
import { renderTableCellWithLineBreaks } from '@/utils/textUtils';

const { Option } = Select;

export type ListType = {
  value?: any;
  label?: string;
};

const ComplaintBind = () => {
  const { initialState } = useModel('@@initialState');
  /** 新增推送绑定的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改推送绑定的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示推送绑定详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 当前行数据 */
  const [currentRow, setCurrentRow] = useState<COMPLAIN.ComplaintPushListItem>();

  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);

  /**
   * 添加推送绑定
   * @param fields
   */
  const handleAdd = async (fields: COMPLAIN.ComplaintPushListItem) => {
    const hide = message.loading('正在添加');
    try {
      const response = await addComplaintPush({ ...fields });
      hide();
      if (response.code === '0') {
        message.success('添加成功');
        return true;
      } else {
        message.error(response.msg || '添加失败');
        return false;
      }
    } catch (error) {
      hide();
      message.error('添加失败请重试！');
      return false;
    }
  };

  /**
   * 更新推送绑定
   * @param fields
   */
  const handleUpdate = async (fields: COMPLAIN.ComplaintPushListItem) => {
    const hide = message.loading('正在更新');
    try {
      const response = await updateComplaintPush({ ...fields });
      hide();
      if (response.code === '0') {
        message.success('更新成功');
        return true;
      } else {
        message.error(response.msg || '更新失败');
        return false;
      }
    } catch (error) {
      hide();
      message.error('更新失败请重试！');
      return false;
    }
  };

  /**
   * 删除推送绑定
   * @param record
   */
  const handleRemove = async (record: COMPLAIN.ComplaintPushListItem) => {
    const hide = message.loading('正在删除');
    try {
      const response = await removeComplaintPush({ id: record.id });
      hide();
      if (response.code === '0') {
        message.success('删除成功');
        actionRef.current?.reload?.();
        return true;
      } else {
        message.error(response.msg || '删除失败');
        return false;
      }
    } catch (error) {
      hide();
      message.error('删除失败请重试！');
      return false;
    }
  };

  const columns: ProColumns<COMPLAIN.ComplaintPushListItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
    },
    {
      title: '接收人姓名',
      dataIndex: 'receiverName',
    },
    {
      title: '接收人邮箱',
      dataIndex: 'receiverEmail',
    },
    {
      title: '接收人电话',
      dataIndex: 'receiverPhone',
      hideInSearch: true,
    },
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.map((c: ListType) => {
          return {
            value: c.value,
            label: c.label,
          };
        });
        return (
          <Select placeholder="请选择" showSearch optionFilterProp="label" options={options} allowClear />
        );
      },
      colSize: 2,
    },
    {
      title: '推送类型',
      dataIndex: 'pushType',
      hideInSearch: true,
      valueEnum: {
        1: { text: '邮件推送' },
        2: { text: '短信推送' },
        3: { text: '微信推送' },
      },
    },
    {
      title: '推送类型',
      dataIndex: 'pushType',
      hideInTable: true,
      hideInDescriptions: true,
      valueEnum: {
        1: { text: '邮件推送' },
        2: { text: '短信推送' },
        3: { text: '微信推送' },
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      valueEnum: {
        1: { text: '启用', status: 'Success' },
        0: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: true,
      ellipsis: true,
      render: (text: string) => renderTableCellWithLineBreaks(text, 100, 'bind-remark'),
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="edit"
          onClick={() => {
            setCurrentRow(record);
            handleUpdateModalVisible(true);
          }}
        >
          编辑
        </a>,
        <Popconfirm
          key="delete"
          title="确定要删除这条记录吗？"
          onConfirm={() => handleRemove(record)}
          okText="确定"
          cancelText="取消"
        >
          <a style={{ color: 'red' }}>删除</a>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer header={{ breadcrumb: {} }}>
      <ProTable
        headerTitle={'投诉推送绑定管理'}
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        request={queryComplaintPushList}
        columns={columns}
      />
      
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            const success = await handleAdd({ ...value });
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      
      {updateModalVisible && (
        <UpdateForm
          updateModalVisible={updateModalVisible}
          values={currentRow || {}}
          onCancel={() => {
            handleUpdateModalVisible(false);
            setCurrentRow(undefined);
          }}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...currentRow, ...value });
            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<COMPLAIN.ComplaintPushListItem>
            column={1}
            title={`推送绑定详情 - ${currentRow.receiverName}`}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<COMPLAIN.ComplaintPushListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default ComplaintBind;
