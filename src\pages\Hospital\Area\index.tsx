import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { But<PERSON>, Modal, Drawer, Select, Tag} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryDiagAreaList, updateDiagArea, addDiagArea } from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import SettingsForm from './components/SettingsForm';
import { handleOperateMethod } from '@/utils/index';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加诊区
 * @param fields
 */
const handleAdd = (fields: AREA.AreaListItem) => handleOperateMethod(addDiagArea, fields, 'add');
/**
 * @zh-CN 修改诊区
 * @param fields
 */
const handleUpdate = (fields: AREA.AreaListItem) => handleOperateMethod(updateDiagArea, fields, 'update');
/**
 * @zh-CN 删除诊区
 * @param deviceID
 */
const handleRemove = (digID?: number) =>
  handleOperateMethod(updateDiagArea, { digID, state: 9 }, 'delete');

const TableList = () => {
  /** 新增诊区的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改诊区的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示诊区详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  /** 诊区设置的弹窗 */
  const [settingsModalVisible, handleSettingsModalVisible] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<AREA.AreaListItem>();
  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);

  const columns: ProColumns<AREA.AreaListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select {...rest} placeholder="请选择">
            {hosList &&
              hosList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`hos${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '诊区名称',
      dataIndex: 'digAreaName',
      key: 'digAreaName',
    },
    {
      title: '诊区代码',
      dataIndex: 'digAreaCode',
      key: 'digAreaCode',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
    },
    {
      title: '关联科室',
      dataIndex: 'diagDeptInfo',
      render: (tags, record) => {
        if (!record) return '';
        return (
          <span>
            { record?.diagDeptInfo?.map((item) => {
              return (
                <Tag color="green" key={item.deptID} style={{marginBottom: 5}}>
                  {item.deptName}
                </Tag>
              );
            })}
          </span>
        );
      },
      width: 180,
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 300,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="settings"
          onClick={() => {
            handleSettingsModalVisible(true);
            setCurrentRow(record);
          }}
        >
          设置
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.digAreaName}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.digID);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        request={queryDiagAreaList}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            const success = await handleAdd(value);
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {settingsModalVisible && (
        <SettingsForm
          values={currentRow || {}}
          modalVisible={settingsModalVisible}
          onCancel={() => handleSettingsModalVisible(false)}
          onSubmit={async (value) => {
            const success = await handleUpdate({...value, digID: currentRow?.digID});
            if (success) {
              handleSettingsModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, digID: currentRow?.digID });

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.digAreaName && (
          <ProDescriptions
            column={1}
            title={currentRow?.digAreaName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.digID,
            }}
            columns={columns as ProDescriptionsItemProps<AREA.AreaListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
