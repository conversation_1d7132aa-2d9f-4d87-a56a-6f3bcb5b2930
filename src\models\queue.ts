// 排队叫号相关查询接口 处理SELECT中的查询数据
import {
    useState,
    useCallback
  } from 'react';
  import {
    getDeptDoctorList,
  } from '@/services/api/queue';
  import {
    message
  } from 'antd'
  
  const MAX_LENGTH = 999;
  export type ListType = {
    value?: any;
    label?: string;
  };
  
  export default () => {
    const [deptDoctorList, setDeptDoctorList] = useState<ListType[]>([]);
    // 查询排队叫号医生患者列表
    const fetchDeptDoctorList = useCallback(async (params) => {
      console.log(params)
      const result = await getDeptDoctorList({
        current: 1,
        pageSize: MAX_LENGTH,
        ...params
      });
      if (result.code === '0' && result.data) {
        const uniqueData = result.data.filter((item, index) =>
          result.data.findIndex(i => i.doctorCode === item.doctorCode) === index);
        const data = uniqueData.map((el:{[key: string]: any }) => {
          return { label: el.doctorName, value: el.doctorCode}
        });
        setDeptDoctorList(data)
        return data
      } else {
        message.error('请求科室医生数据失败,请刷新重试');
        return false
      }
    }, []);
    
    return {
      deptDoctorList,
      fetchDeptDoctorList,
    };
  };