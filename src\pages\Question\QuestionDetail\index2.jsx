/*
	问卷详情-应用于编辑，新建功能
*/

import React from 'react';
import { CheckCircleOutlined, CheckSquareOutlined, FileTextOutlined, PlusOutlined, CloseOutlined, EditOutlined, PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { DatePicker, Button, Input, Checkbox, Icon, Modal, Card, Space, message, Row, Col, Radio } from 'antd';
import { connect, history } from 'umi';
import { addQuestionnaire, editQuestionnaire } from '@/services/question'
import { RollbackOutlined, InfoCircleOutlined } from '@ant-design/icons';
import TipModal from '@/components/TipModal';
import Catalogue from './components/Catalogue';
import TopicLogicModal from './components/TopicLogic';

import './style.less';
/**
 * 添加问卷
 * @param fields
 */
const handleAdd = async fields => {
	const hide = message.loading('正在添加问卷...');
	try {
		const response = await addQuestionnaire({ ...fields });
		if (response.code === '0') {
			hide();
			message.success('添加成功');
			return true;
		}
		message.error(response.msg);
		return false;
	} catch (error) {
		hide();
		message.error('添加失败请重试！');
		return false;
	}
};

/**
 * 修改问卷
 * @param fields
 */
const handleEdit = async fields => {
	const hide = message.loading('正在修改问卷...');
	try {
		const response = await editQuestionnaire({ ...fields });
		if (response.code === '0') {
			hide();
			message.success('修改成功');
			return true;
		}
		message.error(response.msg);
		return false;
	} catch (error) {
		hide();
		message.error('修改失败请重试！');
		return false;
	}
};


const { TextArea } = Input;

// const defaultQuestionnaire = {
// 	title: '此处添加问卷标题',
// 	date: '',
// 	stage: "未发布",
// 	questions: [],
// 	remark: '此处添加问卷描述',
// 	titleEditable: false,
// 	remarkEditable: false,
// 	addAreaVisible: false
// };
const list = localStorage.list ? JSON.parse(localStorage.list) : [];
const editing = localStorage.editing && JSON.parse(localStorage.editing);
const requiredType = {0: false, 1: true, 2: false};   //required checked

class Edit extends React.Component {
	constructor(props) {
		super(props);
		// this.handleTitleClick = this.handleTitleClick.bind(this);
		// this.handleTitleChange = this.handleTitleChange.bind(this);
		// this.handleTitleBlur = this.handleTitleBlur.bind(this);
		// this.handleRemarkClick = this.handleRemarkClick.bind(this);
		// this.handleRemarkChange = this.handleRemarkChange.bind(this);
		// this.handleRemarkBlur = this.handleRemarkBlur.bind(this);
		// this.handleTitleChange = this.handleTitleChange.bind(this);
		// this.handleTitleBlur = this.handleTitleBlur.bind(this);
		// this.handleAddQuestion = this.handleAddQuestion.bind(this);
		// this.handleAddInput = this.handleAddInput.bind(this);
		// this.handleAddRadio = this.handleAddRadio.bind(this);
		// this.handleAddCheckbox = this.handleAddCheckbox.bind(this);
		// this.handleAddTextArea = this.handleAddTextArea.bind(this);
		// this.handleQuestionChange = this.handleQuestionChange.bind(this);
		// this.handleShiftQuestion = this.handleShiftQuestion.bind(this);
		// this.handleCopyQuestion = this.handleCopyQuestion.bind(this);
		// this.handleRemoveQuestion = this.handleRemoveQuestion.bind(this);
		// this.handleOptionChange = this.handleOptionChange.bind(this);
		// this.handleAddOption = this.handleAddOption.bind(this);
		// this.handleRemoveOption = this.handleRemoveOption.bind(this);
		// this.handleTextChange = this.handleTextChange.bind(this);
		// this.handleTextRequire = this.handleTextRequire.bind(this);
		// this.handleDatePick = this.handleDatePick.bind(this);
		// this.handleSaveQuestionnaire = this.handleSaveQuestionnaire.bind(this);
		// this.handleReleaseQuestionnaire = this.handleReleaseQuestionnaire.bind(this);
		this.state = JSON.stringify(this.props.question.editing) !== "{}" ? { ...this.props.question.editing, isCache: false, topicModalVisible: false } :
			{ ...props.question.defaultQuestionnaire, isCache: false, topicModalVisible: false };
	}

	componentWillMount() {
		// 拦截判断是否离开当前页面
		// window.addEventListener('beforeunload', this.beforeunloadHandler, false);
		// const editing = localStorage.editing ? JSON.parse(localStorage.editing) : {}
		// console.log(editing)
		// if (JSON.stringify(editing) !== "{}") {
		// 	this.setState({
		// 		isCache: true
		// 	})
		// }
	}

	componentDidMount() {
		// this.timer = setInterval(()=>{
		//   this.cacheData();
		// },2000)
		document.addEventListener("keydown", this.ctrlS)

	}

	componentWillUnmount() {
		// 销毁拦截判断是否离开当前页面
		this.timer && clearInterval(this.timer)
		document.removeEventListener("keydown", this.ctrlS)
		window.removeEventListener('beforeunload', this.beforeunloadHandler, false);

	}

	beforeunloadHandler = (e) => {
		console.log(e)
		let confirmationMessage = '您输入的内容尚未保存，确定离开此页面吗？';
		(e || window.event).returnValue = confirmationMessage;
		return confirmationMessage;
		// localStorage.removeItem("editing")
	}

	// 缓存数据--localStroage
	cacheData = () => {
		const setEditing = Object.assign({}, { ...this.state });
		localStorage.editing = JSON.stringify(setEditing);
	}

	// ctrl+s缓存数据--localStroage
	ctrlS = (e) => {
		if (window.event) {
			e = window.event
		}
		let code = e.charCode || e.keyCode;
		if (code == 83 && (navigator.platform.match("Mac") ? e.metaKey : e.ctrlKey)) {
			e.preventDefault();
			this.cacheData();
			Modal.success({
				title: '保存成功'
			});
		}
	}

	handleTitleClick=()=>{
		this.setState({
			titleEditable: true
		})
	}

	handleTitleChange=(e)=>{
		this.setState({
			title: e.target.value
		})
	}

	handleTitleBlur=()=>{
		this.setState({
			titleEditable: false
		})
	}

	handleRemarkClick=()=>{
		this.setState({
			remarkEditable: true
		})
	}

	handleRemarkChange=(e)=>{
		this.setState({
			remark: e.target.value
		})
	}

	handleRemarkBlur=()=>{
		this.setState({
			remarkEditable: false
		})
	}

	handleAddQuestion=()=>{
		const { addAreaVisible } = this.state
		this.setState({
			addAreaVisible: !addAreaVisible
		})
	}

	handleAddInput=()=>{
		const newQuestion = {
			// type: 'input',
			type: 1,
			title: '填空题',
			// text: '',
			required: false,
		};
		this.setState((prevState) => ({
			questions: prevState.questions.concat(newQuestion),
			addAreaVisible: false,
		}));
	}

	handleAddRadio=()=>{
		const newQuestion = {
			// type: 'radio',
			type: 3,
			title: '单选题',
			options: [{ text: '选项一' }, { text: '选项二' }, { text: '选项三' }, { text: '选项四' }],
			// data: []
		};
		this.setState((prevState) => ({
			questions: prevState.questions.concat(newQuestion),
			addAreaVisible: false
		}));
	}

	handleAddCheckbox=()=>{
		const newQuestion = {
			// type: 'checkbox',
			type: 2,
			title: '多选题',
			options: [{ text: '选项一' }, { text: '选项二' }, { text: '选项三' }, { text: '选项四' }],
			// data: []
		};
		this.setState((prevState) => ({
			questions: prevState.questions.concat(newQuestion),
			addAreaVisible: false
		}));
	}

	handleAddTextArea=()=>{
		const newQuestion = {
			// type: 'textarea',
			type: 4,
			title: '文本题',
			// text: '',
			required: false
		};
		this.setState((prevState) => ({
			questions: prevState.questions.concat(newQuestion),
			addAreaVisible: false
		}));
	}

	handleQuestionChange=(e, questionIndex)=>{
		const { questions } = this.state;
		questions[questionIndex].title = e.target.value;
		this.setState({
			questions
		});
	}

	handleShiftQuestion=(questionIndex, num)=>{
		const { questions } = this.state;
		const shiftQuestion = questions.splice(questionIndex, 1)[0];
		questions.splice(questionIndex + num, 0, shiftQuestion);
		this.setState({
			questions
		})
	}

	handleCopyQuestion=(questionIndex)=>{
		const { questions } = this.state;
		const copy = Object.assign({}, questions[questionIndex]);
		if (questions[questionIndex].type !== 4) {
			copy.options = copy.options.slice(0);
		}
		questions.splice(questionIndex + 1, 0, copy);
		this.setState({
			questions
		});
	}

	handleRemoveQuestion=(questionIndex)=>{
		const { questions } = this.state;
		questions.splice(questionIndex, 1);
		this.setState({
			questions
		});
	}

	handleOptionChange=(e, questionIndex, optionIndex)=>{
		const { questions } = this.state;
		questions[questionIndex].options[optionIndex].text = e.target.value;
		this.setState({
			questions
		});
		console.log(this.state)
	}

	handleAddOption=(questionIndex)=>{
		const { questions } = this.state;
		const newOption = { text: '新选项' };
		questions[questionIndex].options.push(newOption);
		this.setState({
			questions
		});
	}

	handleRemoveOption=(questionIndex, optionIndex)=>{
		const { questions } = this.state;
		questions[questionIndex].options.splice(optionIndex, 1);
		this.setState({
			questions
		});
	}

	handleTextChange=(e, questionIndex)=>{
		const { questions } = this.state;
		questions[questionIndex].text = e.target.value;
		this.setState({
			questions
		});
	}

	handleTextRequire=(e, questionIndex)=>{
		const { questions } = this.state;
		console.log(e)
		questions[questionIndex].required = e.target.checked;
		this.setState({
			questions
		});
	}

	handleDatePick=(date, dateString)=>{
		this.setState({
			date: dateString
		})
	}

	handleSaveQuestionnaire=()=>{
		const { index } = this.state;
		// const { question } = this.props;
		// const { list } = question || {};
		// list[index] = Object.assign({}, this.state);
		// localStorage.list = JSON.stringify(list);
		this.cacheData();
		Modal.success({
			title: '保存成功'
		});
	}

	// 提交
	handleSubmitQuestionnaire = async () => {
		// {
		//   "current": 0,
		//   "describe": 0,
		//   "hospitalname": "",
		//   "id": 0,
		//   "pageCurrent": 0,
		//   "pageSize": 0,
		//   "publishtime": "",
		//   "questionVOList": [
		//     {
		//       "id": 0,
		//       "options": "",
		//       "required": 0,
		//       "said": 0,
		//       "title": "",
		//       "type": 0
		//     }
		//   ],
		//   "said": 0,
		//   "state": 0,
		//   "title": ""
		// }

		//     addAreaVisible: false
		//     date: ""
		//     index: 10
		//     questions: Array(1)
		//     0:
		//     required: false
		//     text: ""
		//     title: "文本题"
		//     type: "textarea"
		//     __proto__: Array(0)
		//     remark: "此处添加问卷描述"
		//     remarkEditable: false
		//     stage: "未发布"
		//     title: "此处添加问卷标题"
		//     titleEditable: false

		const { title, remark, questions } = this.state;
		const questionVOList = JSON.parse(JSON.stringify(questions));
		questionVOList.forEach((item) => {
			item['required'] = item.required ? 1 : 2
			// item['options'] = JSON.stringify(item.options)
			if(item.hasOwnProperty('options')){
				item['options'] = JSON.stringify(item.options)
			}
			item.hasOwnProperty('index') && (delete item.index)
			item.hasOwnProperty('text') && (delete item.text)
			item.hasOwnProperty('value') && (delete item.value)
		})
		const fields = {
			title,
			describe: remark,
			questionVOList,
		}

		console.log(fields)
		const res = await handleAdd({ ...fields })
		console.log(res)
		if (res) {
			history.push('/question/list')
			localStorage.removeItem("editing")
		}
	}

	// 更新
	handleSave = async () => {
		const { title, id, remark, questions } = this.state;
		console.log(this.state)
		const questionVOList = JSON.parse(JSON.stringify(questions));
		questionVOList.forEach((item) => {
			item['required'] = item.required ? 1 : 2;
			if(item.hasOwnProperty('options')){
				item['options'] = JSON.stringify(item.options)
			}
			item['id'] = item.id;
			item.hasOwnProperty('index') && (delete item.index)
			item.hasOwnProperty('value') && (delete item.value)
			item.hasOwnProperty('text') && (delete item.text)
		})
		const fields = {
			title,
			id,
			describe: remark,
			questionVOList,
		}

		console.log(fields)
		const res = await handleEdit({ ...fields })
		// console.log(res)

		if (res) {
			// history.push('/question/list')
			localStorage.removeItem("editing");
		}


	}

	handleReleaseQuestionnaire() {
		const me = this
		const { index, questions, date } = this.state;

		if (questions.length === 0) {
			Modal.warning({
				title: '请添加至少一个问题'
			});
		} else if (date === '') {
			Modal.warning({
				title: '请选择截止日期'
			});
		} else {
			Modal.confirm({
				title: '确定发布问卷吗？',
				content: `截止日期为${date}`,
				onOk() {
					list[index] = Object.assign({}, { ...me.state, stage: '发布中' });
					localStorage.list = JSON.stringify(list);
					window.location.reload();
					me.props.history.push('/');
				}
			});
		}
	}

	// 锚点定位，聚焦
	scrollToAnchor = (anchorName, ind) => {
		if (anchorName) {
			let scrollElement = document.getElementById("content-card-id");    // 对应id的滚动容器
			let anchorElement = document.getElementById(anchorName);
			// if (anchorElement) { anchorElement.scrollIntoView({ behavior: 'smooth', block: 'start' }); }
			if (scrollElement) {
				let input = document.querySelector(`#${anchorName} .refinput`);
				input.focus();
				scrollElement.scrollTo({ top: anchorElement.offsetTop, left: anchorElement.offsetLeft, behavior: "smooth" });
			}
		}
	}

	handleOpenTopicLogic = (ind,item) => {

		const arr = [...this.state.questions];
		let newarr = arr.slice(ind+1);

		this.setState({
			topicModalVisible: true,
			topicItemData: item,
			topicArr: newarr
		})
	}

	handleCancelTopicLogic = () => {
		this.setState({
			topicModalVisible: false
		})
	}

	getTitle() {
		return (
			this.state.titleEditable ? (
				<div className="editTitle" style={{ padding: 3, textAlign: 'center' }} onClick={this.handleTitleClick}>
					<Input style={{ fontSize: 18, fontWeight: 'bold', padding: 30, textAlign: 'center' }} value={this.state.title} onChange={this.handleTitleChange} onBlur={this.handleTitleBlur} />
				</div>
			) : (
				<div className="editTitle" style={{ padding: 20, textAlign: 'center' }} onClick={this.handleTitleClick}>
					<h2><strong>{this.state.title}</strong></h2>
				</div>
			)
		);
	}

	getRemark() {
		return (
			this.state.remarkEditable ? (
				<div className="editRemark" style={{ margin: '0 20px 20px 20px', padding: 3 }} onClick={this.handleRemarkClick}>
					<TextArea value={this.state.remark} onChange={this.handleRemarkChange} onBlur={this.handleRemarkBlur} />
				</div>
			) : (
				<div className="editRemark" style={{ margin: '0 20px 20px 20px', padding: 20 }} onClick={this.handleRemarkClick}>
					<h4><strong>{this.state.remark}</strong></h4>
				</div>
			)
		)
	}

	getAddArea() {
		return (
			this.state.addAreaVisible ? (
				<div style={{ padding: 30, textAlign: 'center', border: '1px solid #eee' }}>
					<Button icon={<EditOutlined />} size="large" onClick={this.handleAddInput}>填空</Button>
					<Button icon={<CheckCircleOutlined />} size="large" style={{ marginLeft: 16 }} onClick={this.handleAddRadio}>单选</Button>
					<Button icon={<CheckSquareOutlined />} size="large" style={{ marginLeft: 16 }} onClick={this.handleAddCheckbox}>多选</Button>
					<Button icon={<FileTextOutlined />} size="large" style={{ marginLeft: 16 }} onClick={this.handleAddTextArea}>文本</Button>
				</div>
			) : ''
		);
	}

	getQuestions() {
		const { questions } = this.state;
		console.log(questions)

		return questions && questions.map((question, questionIndex, array) => {
			if (question.type === 3) {
				//radio
				return (
					<div className="questionsWrap" style={{ padding: 30 }} key={questionIndex} id={"anchorID" + questionIndex}>
						<span>Q{questionIndex + 1}</span>
						<Input className="refinput" value={question.title} style={{ borderStyle: 'none', width: '97%', marginLeft: 3 }} onChange={(e) => this.handleQuestionChange(e, questionIndex)} />
						<div style={{ margin: "16px 20px" }}>
							{question.options && question.options.map((option, optionIndex) => {
								return (
									<div style={{ margin: '8px 0', display: 'flex', alignItems: 'center' }} key={optionIndex}>
										{/* <Icon type="close" className="deleteOption" style={{ display: 'inline-block', marginRight: 8 }} onClick={() => this.handleRemoveOption(questionIndex, optionIndex)} /> */}
										<Input 
											value={option.text} 
											style={{ width: '30%', borderStyle: 'none', marginRight: 10 }} 
											prefix={<Radio disabled></Radio>}
											onChange={(e) => this.handleOptionChange(e, questionIndex, optionIndex)} 
										/>
										{/* <span className="icon-btns"><PlusCircleOutlined /></span> */}
										<span className="icon-btns" onClick={()=>this.handleRemoveOption(questionIndex, optionIndex)}><MinusCircleOutlined /></span>	
									</div>
								);
							})}
						</div>
						<div className="addOption" style={{ width: '20%', height: 28, margin: '8px 20px' }} onClick={() => this.handleAddOption(questionIndex)} />
						<Checkbox checked={question.required} style={{ margin: '0 20px' }} onChange={(e) => this.handleTextRequire(e, questionIndex)}>此题是否必填</Checkbox>
						{this.getQuestionOperator(questionIndex, array, question)}
					</div>
				);
			}
			if (question.type === 2) {
				//checkbox
				return (
					<div className="questionsWrap" style={{ padding: 30 }} key={questionIndex} id={"anchorID" + questionIndex}>
						<span>Q{questionIndex + 1}</span>
						<Input className="refinput" value={question.title} style={{ borderStyle: 'none', width: '97%', marginLeft: 3 }} onChange={(e) => this.handleQuestionChange(e, questionIndex)} />
						<div style={{ margin: "16px 20px" }}>
							{question.options && question.options.map((option, optionIndex) => {
								return (
									<div style={{ margin: '8px 0', display: 'flex', alignItems: 'center' }} key={optionIndex}>
										<Input 
											value={option.text} 
											style={{ borderStyle: 'none', width: '30%', marginRight: 10 }} 
											prefix={<Checkbox disabled></Checkbox>}
											onChange={(e) => this.handleOptionChange(e, questionIndex, optionIndex)} 
										/>
										<span className="icon-btns" onClick={()=>this.handleRemoveOption(questionIndex, optionIndex)}><MinusCircleOutlined /></span>
									</div>
								);
							})}
						</div>
						<div className="addOption" style={{ width: '20%', height: 28, margin: '8px 20px' }} onClick={() => this.handleAddOption(questionIndex)} />
						<Checkbox checked={question.required} style={{ margin: '0 20px' }} onChange={(e) => this.handleTextRequire(e, questionIndex)}>此题是否必填</Checkbox>
						{this.getQuestionOperator(questionIndex, array, question)}
					</div>
				);
			}
			if (question.type === 4) {
				// textarea
				return (
					<div className="questionsWrap" style={{ padding: 30 }} key={questionIndex} id={"anchorID" + questionIndex}>
						<span>Q{questionIndex + 1}</span>
						<Input className="refinput" value={question.title} style={{ borderStyle: 'none', width: '97%', marginLeft: 3 }} onChange={(e) => this.handleQuestionChange(e, questionIndex)} />
						<div style={{ margin: '16px 20px' }}>
							<TextArea rows={5} value={question.text} onChange={(e) => this.handleTextChange(e, questionIndex)} disabled/>
						</div>
						<Checkbox checked={question.required} style={{ margin: '0 20px' }} onChange={(e) => this.handleTextRequire(e, questionIndex)}>此题是否必填</Checkbox>
						{this.getQuestionOperator(questionIndex, array, question)}
					</div>
				);
			}
			if (question.type === 1) {
				// input
				return (
					<div className="questionsWrap" style={{ padding: 30 }} key={questionIndex} id={"anchorID" + questionIndex}>
						<span>Q{questionIndex + 1}</span>
						<Input className="refinput" value={question.title} style={{ borderStyle: 'none', width: '97%', marginLeft: 3 }} onChange={(e) => this.handleQuestionChange(e, questionIndex)} />
						<div style={{ margin: '16px 20px' }}>
							<Input value={question.text} onChange={(e) => this.handleTextChange(e, questionIndex)} disabled/>
						</div>
						<Checkbox checked={question.required} style={{ margin: '0 20px' }} onChange={(e) => this.handleTextRequire(e, questionIndex)}>此题是否必填</Checkbox>
						{this.getQuestionOperator(questionIndex, array, question)}
					</div>
				);
			}
			return null;
		})
	}

	getQuestionOperator(questionIndex, array, question) {
		return (
			<div>
				<p style={{ float: 'right' }}>
					{questionIndex === 0 ? (
						null
					) : (
						<Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => this.handleShiftQuestion(questionIndex, -1)}>上移</Button>
					)}
					{questionIndex === array.length - 1 ? (
						null
					) : (
						<Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => this.handleShiftQuestion(questionIndex, 1)}>下移</Button>
					)}
					<Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => this.handleCopyQuestion(questionIndex)}>复用</Button>
					<Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => this.handleRemoveQuestion(questionIndex)}>删除</Button>
					<Button size="small" className="questionOperate" style={{ marginLeft: 8 }} onClick={() => this.handleOpenTopicLogic(questionIndex, question)} >跳题逻辑</Button>
				</p>
			</div>
		);
	}

	getFooter() {
		const disabledDate = (current) => current && current.valueOf() < Date.now();
		return (
			<div style={{ padding: 20 }}>
				<div style={{ float: 'left' }}>
					<span>问卷截止日期：</span>
					<DatePicker onChange={this.handleDatePick} disabledDate={disabledDate} />
					<span style={{ marginLeft: 16 }}>你选择的日期为: {this.state.date}</span>
				</div>
				<Space style={{ float: 'right' }}>
					{/* <Button type="primary" style={{ marginLeft: 16 }} onClick={this.handleReleaseQuestionnaire}>发布问卷</Button> */}
					{this.props.question.detailType === "add" ?
						<>
							{/* <Button onClick={this.handleSaveQuestionnaire}>保存问卷</Button> */}
							<Button type="primary" onClick={this.handleSubmitQuestionnaire}>提交问卷</Button>
						</> :
						<>
							<Button type="primary" onClick={this.handleSave} >保存问卷</Button>
						</>
					}
				</Space>
			</div>
		);
	}

	render() {
		
		const tipModalProps = {
			visible: this.state.isCache,
			title: '提示',
			content: '之前有未提交的内容，需要填充吗',
			width: 500,
			onCancel: () => {
				this.setState({ isCache: false })
			},
			onOk: () => {
				const setEditing = Object.assign({}, JSON.parse(localStorage.editing))
				this.setState({
					...setEditing,
					isCache: false,
				})
			}
		}

		let arr = [...this.state.questions];
		const catalogueData = [];
		arr.forEach((item, index) => {
			catalogueData.push({
				name: item.title,
				anchorID: "anchorID" + index
			})
		})



		return (
			<>
				<Row gutter={10} >
					<Col span={4}>
						<Card title="问卷大纲" className="catalogue-card" >
							<Catalogue data={catalogueData} scrollToAnchor={this.scrollToAnchor} />
						</Card>
					</Col>
					<Col span={20}>
						<Card
							className="content-card"
							title={this.getTitle()}
							extra={<Button type="primary" onClick={() => history.replace('/question/list')}><RollbackOutlined />返回</Button>}
							id="content-card-id"
						>
							{/* {this.getTitle()} */}
							{this.getRemark()}
							<div style={{ padding: 20, borderTop: '2px solid #ccc', borderBottom: '2px solid #ccc' }}>
								<div style={{ marginBottom: 20 }} id="questionsBox">
									{this.getQuestions()}
								</div>
								{this.getAddArea()}
								<div className="addQuestion" style={{ wdith: '100%', height: '100%', padding: 30, background: '#eee', textAlign: 'center' }} onClick={this.handleAddQuestion}>
									{this.state.addAreaVisible ? <><CloseOutlined /> 取消添加</> : <><PlusOutlined /> 添加问题</>}
								</div>
							</div>
							{this.getFooter()}
						</Card>
					</Col>
				</Row>
				{this.state.isCache && <TipModal {...tipModalProps} />}
				{this.state.topicModalVisible &&
					<TopicLogicModal
						visible={this.state.topicModalVisible}
						onCancel={this.handleCancelTopicLogic}
						data={this.state.topicArr}
						topicItemData={this.state.topicItemData}
					/>
				}
			</>
		);
	}
}

export default connect(({ question }) => ({
	question
}))(Edit); 