/*
    流程图
*/
import moment from 'moment';
import './index.less';

const OrderSteps = ({
    data
}) => {
    return (
        <ul className="pay-order-flowPath-box">
            { data && data.map((item, index)=>{
                    return (
                        <li className="item-flowPath" key={`${item.title}`}>
                            <span className="title">{item.title}</span>
                            <div className="circle-box">
                                <span className="line"/>
                                <div className="box">
                                    <span className="circle">{index+1}</span>
                                </div>
                            </div>
                            <div className="create-time">
                                <span>{item.time ? moment(item.time).format("YYYY-MM-DD"):""}</span>
                                <br />
                                <span>{item.time ? moment(item.time).format("HH:mm:ss"):""}</span>
                            </div>
                        </li>

                    )
                })
            }
        </ul>
    )

}

export default OrderSteps;