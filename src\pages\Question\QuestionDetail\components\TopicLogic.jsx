// 挑剔逻辑弹窗
import React, { useRef, useState, useEffect } from 'react';
import { Modal, Table, Checkbox, Collapse, Select} from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Option } = Select;
const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;


const TopicLogicModal = (props) => {

    const { visible, onCancel, data, topicItemData } = props;
    const [conditions,setConditions] = useState([])
    const [lists, setLists] = useState([])

    useEffect(()=>{
        if(topicItemData.type===3){
            let temparr = [...topicItemData.options]
            let newarr = [];
            temparr.forEach((item,index)=>{
                newarr.push({
                    id: index,
                    type: "选择",
                    text: item.text,
                })
            })
    
            setLists(newarr)
        }
    },[topicItemData])

   

    // console.log(topicItemData)
    // console.log(data)

    const columns = [
        {
            title:"",
            dataIndex: "type",
            key: "type",
            width: "15%",
            render: (text,record)=>{
                return <span>选择</span>
            }
        },
        {
            title: '选项',
            dataIndex: 'text',
            key: 'text',
            width: "30%",
        },
        {
            title: '跳转到',
            dataIndex: 'conditions',
            key: 'conditions',
            render: (text, record, index)=>{
                return (
                    <Select style={{width: "100%"}} placeholder="请选择要跳转到的题目" allowClear>
                        <Option value={"不跳转，按顺序填写下一道题"}>不跳转，按顺序填写下一道题</Option>
                        <Option value={"跳到问卷末尾结束作答"}>跳到问卷末尾结束作答</Option>
                        <Option value={"直接提交为无效答卷"}>直接提交为无效答卷</Option>
                        {
                            data && data.map((item)=>{
                                return (
                                    <Option value={item.title}>{item.title}</Option>
                                )
                            })
                        }
                    </Select>
                )
            }
        },
    ]



    return (
        <Modal
            visible={visible}
            title="跳题逻辑"
            width={600}
            onCancel={onCancel}
            bodyStyle={{height: 400,overflow: "auto"}}
            className="topic-logic-modal"
        >
            <Collapse
                bordered={false}
                defaultActiveKey={['1']}
                // expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
                expandIcon={({ isActive }) =><Checkbox checked={isActive}></Checkbox> }
                className="topic-logic-collapse"
                accordion 
                ghost
            >   
                { topicItemData.type===3 && 
                    <Panel header="按选项跳题" key="1" className="site-collapse-custom-panel" >
                        <Table
                            columns={columns}
                            dataSource={lists}
                            bordered={true}
                            size="small"
                            rowKey="id"
                            pagination={false}
                        />
                    </Panel>
                }
                <Panel header="无条件跳题" key="2" className="site-collapse-custom-panel">
                    <div className="no-condition-box">
                        <span className="title">填写此题后跳转到</span>
                        <Select style={{width:250}}placeholder="请选择要跳转到的题目" allowClear>
                            <Option value={"跳到问卷末尾结束作答"}>跳到问卷末尾结束作答</Option>
                            {
                                data && data.map((item)=>{
                                    return (
                                        <Option value={item.title}>{item.title}</Option>
                                    )
                                })
                            }
                        </Select>
                    </div>
                </Panel>
            </Collapse>
        </Modal>
    )

}


export default TopicLogicModal