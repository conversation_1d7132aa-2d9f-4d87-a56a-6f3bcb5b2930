declare namespace PATIENT {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 患者详情
    type PatientListItem = {
        patientName?: string,  // 患者姓名
        clinicID?:string,  // 门诊号
        patientID?:string,       // 患者ID
        idCard?:string, // 身份证号
        patientGender?: number,     // 性别
        genderName?: string,  // 性别
        birthDate?:string,  // 生日
        patientPhone?:string,  // 手机号
        medicalCard?:string,  // 社保卡号
        createTime?: string,  // 创建时间
        healthCard?: string,  // 健康卡号
        patientCard?:string,  // 患者卡号
        ID?: number,     // ID       
        state?: string   // 状态
        hospitalName?: string  // 机构
    };
    // 患者列表
    type PatientList = {
        /** 列表的内容 **/
        data?: PatientListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}