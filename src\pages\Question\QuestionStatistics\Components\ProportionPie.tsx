import React from 'react';
import { Card, Radio, Statistic } from 'antd';
import { Pie } from './Charts';
import '../index.less';

export interface PieDataItem {
  x: string;
  y: number;
}

export interface ProportionPieProps {
  title?: string;
  dropdownGroup?: React.ReactNode;
  salesType?: string;
  loading?: boolean;
  salesPieData?: PieDataItem[];
  handleChangeSalesType?: (e: any) => void;
  total?: number;
  subTitle?: string;
  height?: number;
}

const defaultSalesPieData: PieDataItem[] = [
  {
    x: '公众号',
    y: Math.floor(Math.random() * 100) + 20,
  },
  {
    x: '小程序',
    y: Math.floor(Math.random() * 100) + 20,
  },
  {
    x: '自助机',
    y: Math.floor(Math.random() * 100) + 20,
  },
  {
    x: '其他',
    y: Math.floor(Math.random() * 100) + 20,
  },
];

const ProportionPie: React.FC<ProportionPieProps> = ({
  title = "1.您预约挂号的方式为",
  dropdownGroup,
  salesType,
  loading = false,
  salesPieData = defaultSalesPieData,
  handleChangeSalesType,
  total,
  subTitle = "总计",
  height = 248,
}) => {
  // 计算总数
  const calculatedTotal = total || salesPieData.reduce((sum, item) => sum + item.y, 0);

  // 格式化数值
  const formatNumber = (value: number): string => {
    return value.toLocaleString();
  };

  return (
    <Card
      loading={loading}
      bordered={false}
      title={title}
      style={{
        height: '100%',
      }}
      className="question-look-piecard"
      extra={dropdownGroup}
    >
      <div>
        <Pie
          hasLegend
          subTitle={subTitle}
          total={formatNumber(calculatedTotal)}
          data={salesPieData}
          valueFormat={(value: number) => formatNumber(value)}
          height={height}
          lineWidth={4}
          style={{ padding: '8px 0' }}
        />
        
        {/* 额外的统计信息 */}
        <div style={{ 
          marginTop: 16, 
          padding: '16px 0',
          borderTop: '1px solid #f0f0f0',
        }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-around',
            flexWrap: 'wrap',
          }}>
            {salesPieData.map((item, index) => (
              <div key={index} style={{ textAlign: 'center', minWidth: 80 }}>
                <Statistic
                  title={item.x}
                  value={item.y}
                  valueStyle={{ fontSize: 16 }}
                  suffix="人"
                />
                <div style={{ 
                  fontSize: 12, 
                  color: '#666',
                  marginTop: 4,
                }}>
                  {((item.y / calculatedTotal) * 100).toFixed(1)}%
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 单选按钮组 */}
        {handleChangeSalesType && (
          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Radio.Group 
              value={salesType} 
              onChange={handleChangeSalesType}
              size="small"
            >
              <Radio.Button value="all">全部</Radio.Button>
              <Radio.Button value="online">线上</Radio.Button>
              <Radio.Button value="offline">线下</Radio.Button>
            </Radio.Group>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ProportionPie;
