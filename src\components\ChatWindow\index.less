.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
  overflow: hidden;
  
  .chat-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #f5f5f5;
    
    .chat-title {
      font-weight: 500;
      font-size: 16px;
    }
  }
  
  .chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #f5f5f7;
    scroll-behavior: smooth;
    max-height: calc(100vh - 200px); // 确保有明确的高度限制
    
    .chat-loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    .message-time-group {
      text-align: center;
      margin: 16px 0 8px;
      color: #999;
      font-size: 12px;
    }


    
    .message-item {
      display: flex;
      margin-bottom: 16px;
      
      &.message-self {
        flex-direction: row-reverse;
        
        .message-content {
          margin-right: 8px;
          margin-left: 48px;
          
          .message-text {
            background-color: #1890ff;
            color: #fff;
            border-radius: 8px 0 8px 8px;
          }
        }
      }
      
      &.message-other {
        .message-content {
          margin-left: 8px;
          margin-right: 48px;
          
          .message-text {
            background-color: #fff;
            border-radius: 0 8px 8px 8px;
          }
        }
      }
      
      .message-avatar {
        flex-shrink: 0;
      }
      
      .message-content {
        max-width: 70%;
        
        .message-text {
          padding: 8px 12px;
          word-break: break-word;
        }
        
        .message-image {
          img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;

            &:hover {
              transform: scale(1.02);
            }
          }

          // 图片加载状态
          .image-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100px;
            background-color: #f5f5f5;
            border-radius: 8px;
            color: #999;
          }
        }
      }
    }
  }
  
  .chat-input {
    padding: 12px;
    border-top: 1px solid #e8e8e8;
    background-color: #fff;
    
    .ant-input {
      border-radius: 4px;
      resize: none;
    }
    
    .chat-actions {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      
      .ant-upload {
        margin-right: 8px;
      }
    }
  }
}