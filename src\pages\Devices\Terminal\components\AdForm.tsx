import { useState } from 'react';
import { ModalForm, ProFormUploadButton} from '@ant-design/pro-form';
import {message} from 'antd';
import defaultSettings from '../../../../../config/defaultSettings';
import type { UploadFile } from 'antd/es/upload/interface';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const UPLOAD_URL = `/${defaultSettings.apiName}/system/uploadFiles.np`

type FormValueType = Partial<DEVICES.CmdTypeItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
};

const AdForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, onSubmit }) => {
  const [ fileList, setFileList ] = useState<UploadFile[]>([])
  const [ filePath, setFilePath ] = useState<string>('')

  const uploadProps = {
    name: 'multipartFile',
    data: {
        moduleSrc: "advertising",
    },
    action: UPLOAD_URL,
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info: any) {
      let proUploadList = [...info.fileList];
      proUploadList = proUploadList.slice(-1);
      setFileList(proUploadList)
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 文件上传成功。`);
        setFilePath(info.file.response.data)
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件上传失败。`);
      }
    },
  };

  return (
    <ModalForm
      title="下发广告/宣教视频"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={() => {
        const data = {
          cmdContent: '广告宣教视频',  // 命令类型
          cmdSrc: filePath,  // 命令src
          optType: 3  // 操作类型 3 下发
        }
        return onSubmit(data)
      }}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      <ProFormUploadButton 
        name="file" 
        label="广告/宣教视频" 
        fieldProps={uploadProps} 
        fileList={fileList} 
        max={1} 
        rules={[{required: true, message: '广告/宣教视频为必填项' }]}
      />
    </ModalForm>
  );
};

export default AdForm;