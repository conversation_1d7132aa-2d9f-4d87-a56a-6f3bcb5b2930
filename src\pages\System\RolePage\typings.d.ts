declare namespace ROLE {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 角色详情
    type RoleListItem = {        
        sysType?: string,   // 系统类型
        sysTypeName?: string,  // 系统类型名称
        roleID?: number,    // 角色ID
        roleName?: string,  // 角色名称
        remark?: string,    // 备注
        key?: number,       // key
        isInit?: number     // 是否初始化角色
        perCode?: string    // 菜单ID
    };

    // 功能详情
    type FunctionListItem = {
        perCode?: string,
        sysType?: string,
        permiEnable?: number,
        sysPerID?: number,
        controllerName?: string,
        icon?: string,
        remark?: string,
        permiName?: string,
        classType?: string,
        urlLink?: string
    }

    // 角色列表
    type RoleList = {
        /** 列表的内容 **/
        data?: RoleListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}