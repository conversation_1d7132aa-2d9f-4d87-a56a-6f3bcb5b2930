import {
  PlusOutlined,
  InfoCircleOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  ArrowDownOutlined,
  CloudUploadOutlined,
  RedoOutlined,
  StopOutlined,
  SoundOutlined
} from '@ant-design/icons';
import { Button, message, Modal, Radio, Popover, Tag, Drawer, Select, Space, Switch, Dropdown, Menu} from 'antd';
import { useState, useRef, useEffect } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import { ProFormRadio, ProFormSwitch, ProList, ProFormSelect} from '@ant-design/pro-components'
import type { ActionType, ProColumns, ProDescriptionsItemProps} from '@ant-design/pro-components';
import type { MenuProps } from 'antd';
import { queryDevicesList, updateDevice, addDevice, cmdDevice, queryDeviceFaultInfo, queryDevicesDetail} from '@/services/api/devices';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import AdForm from './components/AdForm';
import VersionForm from './components/VersionForm';
import BoardcastForm  from './components/BoardcastForm';
import ClinicForm from './components/ClinicForm';
import PicForm from './components/PicForm';
import DigForm from './components/DigForm';
import HardwareForm from './components/HardwareForm';
import { handleOperateMethod } from '@/utils/index';
import defaultSettings from '../../../../config/defaultSettings'

const { Option } = Select;
export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加设备
 * @param fields
 */
const handleAdd = (fields: DEVICES.DeviceListItem) => handleOperateMethod(addDevice, fields, 'add');
/**
 * @zh-CN 更新设备
 * @param fields
 */
const handleUpdate = (fields: DEVICES.DeviceListItem) => handleOperateMethod(updateDevice, fields, 'update');
/**
 * @zh-CN 删除设备
 * @param deviceID
 */
const handleRemove = (deviceID?: number) =>
  handleOperateMethod(updateDevice, { deviceID, state: 9 }, 'delete');
/**
 * @zh-CN 启用设备
 * @param deviceID
 */
const handleStart = (deviceID?: number) =>
  handleOperateMethod(updateDevice, { deviceID, state: 1 }, 'start');
/**
 * @zh-CN 停用设备
 * @param deviceID
 */
const handleStop = (deviceID?: number) => handleOperateMethod(updateDevice, { deviceID, state: 0 }, 'stop');
/**
 * @zh-CN 设备指令
 * @param fields
 */
const handleSendCmd = (fields: DEVICES.CmdTypeItem) => handleOperateMethod(cmdDevice, fields, 'send');

/**
 * @zh-CN 请求硬件异常信息
 * @param fields
 */
const handleDeviceFaultInfo = async (fields: DEVICES.DeviceListItem) => {
  try {
    const response = await queryDeviceFaultInfo({ ...fields });
    if (response.code === '0') {
      return response.data;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    message.error('获取硬件信息失败！');
    return false;
  }
};

const TableList = () => {
  /** 新增设备的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改设备的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 设备下发广告的弹窗 */
  const [adModalVisible, handleAdModalVisible] = useState(false);  // 广告弹出框
  /** 版本更新下发的弹窗 */
  const [versionModalVisible, handleVersionModalVisible] = useState(false); // 版本更新弹出框
  /** 广播下发的弹窗 */
  const [boardcastModalVisible, handleBoardcastModalVisible] = useState(false);  // 广播下发弹出框
  /** 设备绑定诊室的弹窗 */
  const [clinicModalVisible, handleClinicModalVisible] = useState(false); // 设备绑定诊室
  /** 设备绑定诊区的弹窗 */
  const [digAreaModalVisible, handleDigAreaModalVisible] = useState(false); // 设备绑定诊区
  /** 设备硬件状态的弹窗 */
  const [hardwareModalVisible, handleHardwareModalVisible] = useState(false); // 设备硬件状态
  /** 设备实时截屏状态的弹窗 */
  const [picModalVisible, handlePicModalVisible] = useState(false); // 设备解聘状态
  /** 展示设备详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<DEVICES.DeviceListItem>();
  const [hardwareData, setHardwareData] = useState<DEVICES.HardwareItem[]>([]);
  const [selectedRowsState, setSelectedRows] = useState<DEVICES.DeviceListItem[]>([]);
  const [formStyle, handleFormStyleChange] = useState('table'); // 列表或图片模式展示切换
  const { dicList, fetchDicList } = useModel('dictionary');
  const { roleList, fetchRoleList } = useModel('system');
  const { hosList, fetchHosList } = useModel('hospital');


  useEffect(() => {
    fetchHosList();
    fetchDicList('deviceType', 'number');
    fetchRoleList();
  }, []);

  const extraContent = (
    <Radio.Group
      defaultValue={formStyle}
      value={formStyle}
      buttonStyle="solid"
      onChange={(e) => {
        handleFormStyleChange(e.target.value);
      }}
    >
      <Radio.Button value="list">
        <Space>
          <AppstoreOutlined />
          <span>大图模式</span>
        </Space>
      </Radio.Button>
      <Radio.Button value="table">
        <Space>
          <UnorderedListOutlined />
          <span>列表模式</span>
        </Space>
      </Radio.Button>
    </Radio.Group>
  );

  const items: MenuProps['items'] = [
    {
      label: '关联诊室',
      key: '0',
    },
    {
      type: 'divider',
    },
    {
      label: '硬件状态',
      key: '1',
    },
    {
      type: 'divider',
    },
    {
      label: '参数选取',
      key: '2',
    },
    {
      type: 'divider',
    },
    {
      label: '参数修改',
      key: '3',
    },
    {
      type: 'divider',
    },
    {
      label: '日志文件',
      key: '4',
    },
  ];
  
  const columns: ProColumns<DEVICES.DeviceListItem>[]  = [
    {
      title: '关联机构',
      dataIndex: 'saID',
      // hideInTable: true,
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.map((c: ListType) => {
          return {
            value: c.value,
            label: c.label,
          };
        })
        return (
          <Select {...rest} placeholder="请选择" showSearch optionFilterProp="label" options={options} />
        );
      },
    },
    {
      title: '终端编号',
      dataIndex: 'deviceCode',
      key: 'deviceCode',
      fixed: 'left',
    },
    {
      title: 'HIS终端编号',
      dataIndex: 'hisDeviceCode',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '终端名称',
      dataIndex: 'deviceName',
      key: 'deviceName',
      fixed: 'left',
    },
    {
      title: '设备位置',
      dataIndex: 'deviceAddress',
      hideInSearch: true,
    },
    {
      title: '关联诊室',
      dataIndex: 'roomID',
      key: 'roomID',
      valueType: 'select',
      render: (tags, record) => {
        if (!record) return '';
        const roomName = record.roomName ? record.roomName.split(',') : [];
        return (
          <span>
            {roomName.map((tag) => {
              return (
                <Tag color="geekblue" key={tag}>
                  {tag}
                </Tag>
              );
            })}
          </span>
        );
      },
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '关联科室',
      dataIndex: 'deptCode',
      render: (tags, record) => {
        if (!record) return '';
        const deptName = record.deptName ? record.deptName.split(',') : [];
        return (
          <span>
            {deptName.map((tag) => {
              return (
                <Tag color="green" key={tag}>
                  {tag}
                </Tag>
              );
            })}
          </span>
        );
      },
      width: 180,
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '关联诊区',
      dataIndex: 'digAreaName',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: 'IP地址',
      dataIndex: 'deviceIP',
      hideInSearch: true,
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      renderText: (_, record) => {
        return record.deviceTypeName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        return (
          <Select {...rest} placeholder="请选择">
            {dicList &&
              dicList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`deviceType${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '设备角色',
      dataIndex: 'roleID',
      hideInSearch: true,
      renderText: (_, record) => {
        return record.roleName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        return (
          <Select {...rest} placeholder="请选择">
            {roleList &&
              roleList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`role${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '软件版本',
      dataIndex: 'deviceVersion',
      hideInSearch: true,
      hideInTable: defaultSettings.platfromType === 'PlatformForDrugstore'
    },
    {
      title: "启/停用",
      dataIndex: "check",
      render: (dom, entity, index, action) => {
        return (
          <Switch checkedChildren="启用" unCheckedChildren="停用" checked={entity.state === 0 ? false: true} size="small" onChange={async(checked) => {
            let success;
            if(checked){
              success = await handleStart(entity.deviceID);
            }else{
              success = await handleStop(entity.deviceID);
            }
            if (success) {
              action?.reload();
            }
          }} />
        );
      },
      hideInDescriptions: true,
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueEnum: {
        0: {
          text: '未启用',
          status: 'Default',
        },
        1: {
          text: '正常',
          status: 'Processing',
        },
        2: {
          text: '异常',
          status: 'Error',
        },
        3: {
          text: '离线',
          status: 'Warning',
        },
      },
    },
    {
      title: '异常状态',
      dataIndex: 'errorRemark',
      hideInForm: true,
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: true,
      ellipsis: true,
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除设备${record.deviceCode}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.deviceID);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
        ( defaultSettings.platfromType === 'SelfService' || defaultSettings.platfromType === 'Queue' ) &&  <div key="more">
          <Dropdown
            // menu={{ items }}
            overlay={
              <Menu>
                <Menu.Item
                  onClick={() => {
                    handleClinicModalVisible(true);
                    setCurrentRow(record);
                  }}
                >
                  关联诊室
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    handleDigAreaModalVisible(true);
                    setCurrentRow(record);
                  }}
                >
                  关联诊区
                </Menu.Item>
                {/* <Menu.Item
                  onClick={async () => {
                    // handleClinicModalVisible(true);
                    setCurrentRow(record);
                    const success = await handleSendCmd({
                      deviceID: record.deviceID,
                      optType: 10
                    });
                    if(success){
                      handlePicModalVisible(true)
                    }else{
                      message.error('请联系管理员配置设备参数')
                    }
                  }}
                >
                  实时截屏
                </Menu.Item> */}
                <Menu.Item
                  onClick={async () => {
                    // await handleDeviceFaultInfo({ deviceID: record.deviceID});
                    handleHardwareModalVisible(true);
                    const data: DEVICES.HardwareItem[]= [
                      {
                        content: 'CPU',
                        state:  record.cpu,
                      },
                      {
                        content: '内存',
                        state:  record.memory + '%',
                      },
                      {
                        content: '硬盘',
                        state:  record.diskRemark,
                      },
                    ]
                    setHardwareData(data);
                  }}
                >
                  硬件状态
                </Menu.Item>
                {/* <Menu.Item
                  onClick={() => {
                    // dispatch({
                    //   type: 'module/fetchBaseParam',
                    //   payload: {},
                    // });
                    // dispatch({
                    //   type: 'module/fetchDeviceParam',
                    //   payload: {
                    //     deviceCode: record.deviceCode,
                    //   },
                    // });
                    // handleParamsModalVisible(true);
                    // setStepFormValues(record);
                  }}
                >
                  参数选取
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    // dispatch({
                    //   type: 'module/fetchDeviceParam',
                    //   payload: {
                    //     deviceCode: record.deviceCode,
                    //   },
                    //   callback: (res) => {
                    //     setStepFormValues(record);
                    //     if (res.length > 0) {
                    //       return handleModuleModalVisible(true);
                    //     }
                    //     handleParamsModalVisible(true);
                    //     return message.info('请先选择参数项');
                    //   },
                    // });
                  }}
                >
                  参数修改
                </Menu.Item> */}
                <Menu.Item
                  onClick={() => {
                    const aLink = document.createElement('a');
                    document.body.appendChild(aLink);
                    aLink.style.display = 'none';
                    aLink.href = `http://${record.deviceIP}:9002`;
                    aLink.setAttribute('target', '_blank');
                    aLink.click();
                    document.body.removeChild(aLink);
                  }}
                >
                  日志文件
                </Menu.Item>
              </Menu>
            }
          >
            <a>更多操作</a>
          </Dropdown>
        </div>
      ],
    },
  ];
  
  return (
    <PageContainer 
      // extra={extraContent}
      header={{breadcrumb:{}}}>
      { 
      formStyle === 'list' ?
      <ProList<any>
        pagination={{
          defaultPageSize: 8,
          showSizeChanger: false,
        }}
        showActions="hover"
        rowSelection={{}}
        grid={{ gutter: 16, column: 4 }}
        onItem={(record: any) => {
          return {
            onMouseEnter: () => {
              console.log(record);
            },
            onClick: () => {
              console.log(record);
            },
          };
        }}
        metas={{
          title: {
            dataIndex: "deviceName"
          },
          subTitle: {
            dataIndex: "deviceCode"
          },
          type: {},
          avatar: {},
          content: {
            dataIndex: "deviceIP"
          },
          actions: {},
        }}
        headerTitle="设备列表"
        request={queryDevicesList}
      />:
      <ProTable
        headerTitle={'设备列表'}
        actionRef={actionRef}
        rowKey="deviceID"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          ( defaultSettings.platfromType === 'SelfService' || defaultSettings.platfromType === 'Queue' ) && selectedRowsState?.length > 0 && (
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                Modal.confirm({
                  title: '确认重启',
                  icon: <InfoCircleOutlined />,
                  content: `是否确认重启这${selectedRowsState.length}台设备?`,
                  okText: '确认',
                  cancelText: '取消',
                  onOk: async () => {
                    const success = await handleSendCmd({
                      cmdContent: '',
                      cmdSrc: '',
                      deviceIDs: selectedRowsState.map((item)=> item.deviceID).join(','),
                      optType: 1
                    });
                    if (success) {
                      if (actionRef.current) {
                        actionRef.current.reload();
                      }
                    }
                  }
                });
              }}
            >
              <RedoOutlined /> 批量重启
            </Button>
          ),
          ( defaultSettings.platfromType === 'SelfService' || defaultSettings.platfromType === 'Queue' ) && selectedRowsState?.length > 0 && (
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                Modal.confirm({
                  title: '确认关机',
                  icon: <InfoCircleOutlined />,
                  content: `是否确认关机这${selectedRowsState.length}台设备?`,
                  okText: '确认',
                  cancelText: '取消',
                  onOk: async () => {
                    const success = await handleSendCmd({
                      cmdContent: '',
                      cmdSrc: '',
                      deviceIDs: selectedRowsState.map((item)=> item.deviceID).join(','),
                      optType: 2
                    });
                    if (success) {
                      if (actionRef.current) {
                        actionRef.current.reload();
                      }
                    }
                  }
                });
              }}
            >
              <StopOutlined /> 批量关机
            </Button>
          ),
          ( defaultSettings.platfromType === 'SelfService' || defaultSettings.platfromType === 'Queue' ) && selectedRowsState?.length > 0 && (
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                handleVersionModalVisible(true);
              }}
            >
              <CloudUploadOutlined /> 版本更新
            </Button>
          ),
          ( defaultSettings.platfromType === 'SelfService' || defaultSettings.platfromType === 'Queue' ) && selectedRowsState?.length > 0 && (
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                handleAdModalVisible(true);
              }}
            >
              <ArrowDownOutlined /> 宣教下发
            </Button>
          ),
          ( defaultSettings.platfromType === 'SelfService' || defaultSettings.platfromType === 'Queue' ) && selectedRowsState?.length > 0 && (
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                handleBoardcastModalVisible(true);
              }}
            >
              <SoundOutlined /> 设备广播
            </Button>
          ),
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        request={queryDevicesList}
        columns={columns}
        rowSelection={( defaultSettings.platfromType === 'SelfService' || defaultSettings.platfromType === 'Queue' ) && {
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      }
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              项
            </div>
          }
        >
          <Button
            type="primary"
            onClick={() => {
              handleAdModalVisible(true);
            }}
          >
            宣教下发
          </Button>
          <Button type="primary" onClick={() => {
            handleBoardcastModalVisible(true);
          }}>设备广播</Button>
          <Button type="primary" onClick={() => {
            handleVersionModalVisible(true);
          }}>版本更新</Button>
        </FooterToolbar>
      )}
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value);
            const success = await handleAdd(value);
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, deviceID: currentRow?.deviceID });

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      {adModalVisible && <AdForm
        onSubmit={async values => {
          const success = await handleSendCmd({...values, deviceIDs: selectedRowsState.map((item)=> item.deviceID).join(',')});

          if (success) {
            handleAdModalVisible(false);
            if (actionRef.current) {
              actionRef.current?.reload?.();
            }
          }
        }}
        onCancel={() => { handleAdModalVisible(false);}}
        modalVisible={ adModalVisible }
      />
      }
      {versionModalVisible && <VersionForm
        onSubmit={async values => {
          const success = await handleSendCmd({...values, deviceIDs: selectedRowsState.map((item)=> item.deviceID).join(',')});

          if (success) {
            handleVersionModalVisible(false);
            if (actionRef.current) {
              actionRef.current?.reload?.();
            }
          }
        }}
        onCancel={() => { handleVersionModalVisible(false);}}
        modalVisible={ versionModalVisible }
      />
      }
      {
        picModalVisible && <PicForm
          onCancel={() => { handlePicModalVisible(false);}}
          modalVisible={ picModalVisible }
          code={currentRow.deviceCode || ''}
        />
      }
      {
        digAreaModalVisible && <DigForm 
          onSubmit={async values => {
            const success = await handleUpdate({ digID: values.digID, deviceID: currentRow?.deviceID });

            if (success) {
              handleDigAreaModalVisible(false);
              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => { handleDigAreaModalVisible(false);}}
          modalVisible={ digAreaModalVisible }
          values={currentRow || {}}
        />
      }
      {
        hardwareModalVisible && <HardwareForm
          onCancel={() => { handleHardwareModalVisible(false);}}
          modalVisible={ hardwareModalVisible }
          data={hardwareData}
        />
      }
      {
        boardcastModalVisible && <BoardcastForm
          onSubmit={async values => {
            const success = await handleSendCmd({...values, deviceIDs: selectedRowsState.map((item)=> item.deviceID).join(',')});

            if (success) {
              handleBoardcastModalVisible(false);
              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => { handleBoardcastModalVisible(false);}}
          modalVisible={ boardcastModalVisible }
        />
      }
      {
        clinicModalVisible && <ClinicForm
        onSubmit={async values => {
          console.log(values)
          let roomIDs
          if (typeof values.roomID === "string") {
            roomIDs = values.roomID;
          }
          if (typeof values.roomID === "object") {
            roomIDs = values.roomID.join(',');
          }
          const success = await handleUpdate({ roomID: roomIDs, deviceID: currentRow?.deviceID });

          if (success) {
            handleClinicModalVisible(false);
            if (actionRef.current) {
              actionRef.current?.reload?.();
            }
          }
        }}
        onCancel={() => { handleClinicModalVisible(false);}}
        modalVisible={ clinicModalVisible }
        values={currentRow || {}}
      />
      }
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.deviceID && (
          <ProDescriptions
            column={1}
            title={currentRow?.deviceName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.deviceID,
            }}
            columns={columns as ProDescriptionsItemProps<DEVICES.DeviceListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
