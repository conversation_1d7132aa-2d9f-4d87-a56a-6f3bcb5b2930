import { getPatientAgeGroupVisits } from '@/services/api/analysis';
import { useRequest } from 'ahooks';
import * as echarts from 'echarts';
import { memo, useEffect, useRef } from 'react';
import styles from '../index.less';

// 请求年龄
const queryPatientAgeGroupVisits = async () => {
  try {
    const response = await getPatientAgeGroupVisits({});

    if (response.code === '0') {
      return response.data;
    }
    return false;
  } catch (error) {
    return false;
  }
};

const EchartsTest = memo(() => {
  const { data } = useRequest(queryPatientAgeGroupVisits, {
    refreshDeps: [],
    pollingInterval: 30000,
    pollingWhenHidden: true,
  });
  const ref = useRef<any>();
  const chartRef = useRef<any>();
  const handleResize = () => {
    chartRef.current.resize();
  };
  useEffect(() => {
    if (data) {
      chartRef.current = echarts.init(ref.current);
      const values = [
        data?.age_0_to_10,
        data?.age_10_to_20,
        data?.age_20_to_40,
        data?.age_40_to_60,
        data?.age_over_60
      ];
      values.sort(function (a, b) {
        return a-b;
      });
      const max = values[values.length - 1] + 10
      const ageData = [
        {
          name: '0-10岁',
          num: data?.age_0_to_10,
          max
        },
        {
          name: '10-20岁',
          num: data?.age_10_to_20,
          max
        },
        {
          name: '20-40岁',
          num: data?.age_20_to_40,
          max
        },
        {
          name: '40-60岁',
          num: data?.age_40_to_60,
          max
        },
        {
          name: '60岁以上',
          num: data?.age_over_60,
          max
        },
      ];
      const option = {
        radar: {
          // shape: 'circle',
          indicator: ageData,
          center: ['55%', '60%'],
          axisName: {
            color: '#fff',
          },
          splitArea: {
            areaStyle: {
              color: 'rgba(0, 0, 0, 0)',
              shadowColor: 'rgba(0, 0, 0, 0)',
              shadowBlur: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: '#5087EC',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#5087EC',
            },
          },
        },

        series: [
          {
            name: '年龄',
            type: 'radar',
            areaStyle: {},
            label: {
              show: true //显示数值
            },
            data: [
              {
                value: values,
                name: '人数',
                itemStyle: {
                  normal: {
                    color: '#4083E2',
                  },
                },
              },
            ],
          },
        ],
      };
      window.addEventListener('resize', handleResize);
      chartRef.current.setOption(option);
    }
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data]);
  return <div className={styles.main5} id="main5" ref={ref}></div>;
});

export default EchartsTest;
