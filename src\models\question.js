const sessionEditing = sessionStorage.getItem('editing');
const editing = sessionEditing ? JSON.parse(sessionEditing) : {};

const Model = {
  namespace: 'question',
  state: {
    editing,
    list: [],
    detailType: '',
    defaultQuestionnaire: { // 默认的问卷字段设置
      title: '',
      date: '',
      stage: "未发布",
      questions: [],
      remark: '',
      titleEditable: false,
      remarkEditable: false,
      addAreaVisible: false
    },
    updateQuesValues: {}, // 编辑问卷时,问卷数据
    questionLists: [], // 问卷问题列表数据用于目录
  },
  effects: {

  },
  reducers: {
    saveQustionLists(state, {
      payload
    }) {
      return {
        ...state,
        list: payload
      };
    },
    setEditingDetails(state, {
      payload
    }) {
      sessionStorage.setItem('editing', JSON.stringify(payload))
      return {
        ...state,
        editing: payload
      };
    },
    setDetailTypeType(state, {
      payload
    }) {
      return {
        ...state,
        detailType: payload
      };
    },
    setQuestionnaireFields(state, {
      payload
    }) {
      return {
        ...state,
        defaultQuestionnaire: payload
      }
    },
    setQuestionnaireValues(state, {
      payload
    }) {
      return {
        ...state,
        updateQuesValues: payload
      }
    },
    setQuestionLists(state, {
      payload
    }) {
      return {
        ...state,
        questionLists: payload
      }
    }

  }
};

export default Model;
