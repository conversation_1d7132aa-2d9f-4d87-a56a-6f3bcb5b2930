# 文字内容换行符适配功能

## 功能概述

为了更好地显示包含换行符 `\n` 的文字内容，我们创建了一套完整的文本处理工具函数，支持在不同场景下正确显示换行符。

## 主要功能

### 1. 聊天消息换行显示
- 在聊天窗口中正确显示包含换行符的文字消息
- 保持消息的原始格式和换行结构
- 支持多行文本的自然显示

### 2. 表格内容换行显示
- 在数据表格中正确显示包含换行符的文本
- 支持文本长度限制和省略号显示
- 提供完整文本的tooltip提示

### 3. 通用文本处理
- 提供多种文本换行处理方式
- 支持不同的显示需求和场景
- 统一的API接口，便于维护

## 工具函数说明

### 1. `renderTextWithLineBreaks(text, keyPrefix)`
**用途**：将文本中的 `\n` 转换为 React 中的 `<br />` 标签

**参数**：
- `text: string` - 包含换行符的文本
- `keyPrefix: string` - 用于生成唯一key的前缀（默认：'line'）

**返回**：React 元素数组

**使用场景**：聊天消息、富文本显示

**示例**：
```typescript
// 输入文本
const text = "第一行\n第二行\n第三行";

// 使用函数
const result = renderTextWithLineBreaks(text, 'msg-123');

// 渲染结果
<span key="msg-123-0">第一行<br /></span>
<span key="msg-123-1">第二行<br /></span>
<span key="msg-123-2">第三行</span>
```

### 2. `renderTextWithLineDivs(text, keyPrefix)`
**用途**：将文本中的 `\n` 转换为多个 `<div>` 元素

**参数**：
- `text: string` - 包含换行符的文本
- `keyPrefix: string` - key前缀（默认：'line'）

**返回**：React 元素

**使用场景**：表格显示、列表显示

**示例**：
```typescript
// 输入文本
const text = "第一行\n第二行\n第三行";

// 渲染结果
<div>
  <div key="line-0">第一行</div>
  <div key="line-1">第二行</div>
  <div key="line-2">第三行</div>
</div>
```

### 3. `renderTableCellWithLineBreaks(text, maxLength, keyPrefix)`
**用途**：为表格单元格渲染带换行的文本，支持长度限制

**参数**：
- `text: string` - 文本内容
- `maxLength: number` - 最大显示长度（默认：100）
- `keyPrefix: string` - key前缀（默认：'cell'）

**返回**：React 元素

**特性**：
- 自动截断过长文本并添加省略号
- 完整文本作为tooltip显示
- 保持换行格式

**示例**：
```typescript
// 长文本
const longText = "这是一段很长的文本\n包含换行符\n需要在表格中显示";

// 使用函数（限制50字符）
const result = renderTableCellWithLineBreaks(longText, 50, 'table-cell');

// 渲染结果（带tooltip）
<div title="完整文本...">
  <div key="table-cell-0">这是一段很长的文本</div>
  <div key="table-cell-1">包含换行符</div>
  <div key="table-cell-2">需要在表格中...</div>
</div>
```

### 4. 其他辅助函数

#### `convertLineBreaksToHtml(text)`
将 `\n` 转换为HTML的 `<br>` 标签
```typescript
const html = convertLineBreaksToHtml("第一行\n第二行");
// 结果: "第一行<br />第二行"
```

#### `removeLineBreaks(text)`
移除换行符，用空格替代
```typescript
const result = removeLineBreaks("第一行\n第二行");
// 结果: "第一行 第二行"
```

#### `truncateTextWithLineBreaks(text, maxLength)`
截断文本但保留换行符
```typescript
const result = truncateTextWithLineBreaks("很长的文本\n第二行", 10);
// 结果: "很长的文本\n第..."
```

#### `countTextLines(text)`
计算文本行数
```typescript
const lines = countTextLines("第一行\n第二行\n第三行");
// 结果: 3
```

## 应用场景

### 1. 聊天系统 (`ChatWindow`)
```typescript
// 在聊天消息中显示换行
<div className="message-text">
  {utilRenderTextWithLineBreaks(msg.content, String(msg.messageId))}
</div>
```

**效果**：
- 用户发送的多行消息正确显示
- 保持原始的换行格式
- 提升聊天体验

### 2. 投诉管理系统
```typescript
// 投诉内容显示
{
  title: '投诉内容',
  dataIndex: 'content',
  render: (text: string) => renderTableCellWithLineBreaks(text, 100, 'content'),
}

// 处理备注显示
{
  title: '处理备注',
  dataIndex: 'handleRemark', 
  render: (text: string) => renderTableCellWithLineBreaks(text, 150, 'remark'),
}
```

**效果**：
- 表格中正确显示多行投诉内容
- 长文本自动截断并显示省略号
- 鼠标悬停显示完整内容

### 3. 其他数据展示场景
- 用户评论和反馈
- 系统日志和错误信息
- 配置说明和帮助文档
- 任何需要保持换行格式的文本内容

## 技术实现

### 1. React Key 管理
```typescript
// 使用唯一的key避免渲染问题
return lines.map((line, index) => (
  <span key={`${keyPrefix}-${index}`}>
    {line}
    {index < lines.length - 1 && <br />}
  </span>
));
```

### 2. 空行处理
```typescript
// 使用不间断空格保持空行显示
<div key={`${keyPrefix}-${index}`}>{line || '\u00A0'}</div>
```

### 3. 性能优化
- 避免重复的字符串分割操作
- 使用合适的key值提升渲染性能
- 按需加载和渲染

### 4. 类型安全
```typescript
// 完整的TypeScript类型定义
export const renderTextWithLineBreaks = (
  text: string, 
  keyPrefix: string = 'line'
): React.ReactNode => {
  // 实现...
};
```

## 使用指南

### 1. 导入工具函数
```typescript
import { 
  renderTextWithLineBreaks,
  renderTableCellWithLineBreaks,
  convertLineBreaksToHtml 
} from '@/utils/textUtils';
```

### 2. 在组件中使用
```typescript
// 聊天消息
const messageContent = renderTextWithLineBreaks(message.content, message.id);

// 表格单元格
const cellContent = renderTableCellWithLineBreaks(data.description, 100);

// HTML内容
const htmlContent = convertLineBreaksToHtml(data.content);
```

### 3. 自定义配置
```typescript
// 自定义长度限制
const shortText = renderTableCellWithLineBreaks(text, 50, 'short');

// 自定义key前缀
const uniqueContent = renderTextWithLineBreaks(text, `msg-${userId}-${timestamp}`);
```

## 注意事项

### 1. 性能考虑
- 对于大量数据的表格，考虑虚拟滚动
- 避免在render函数中进行复杂的文本处理
- 使用React.memo优化组件渲染

### 2. 安全性
- 所有文本内容都经过React的XSS防护
- 不使用dangerouslySetInnerHTML
- 输入验证和清理

### 3. 兼容性
- 支持所有现代浏览器
- 移动端友好的显示效果
- 响应式设计适配

### 4. 可访问性
- 保持文本的语义结构
- 支持屏幕阅读器
- 合适的对比度和字体大小

## 扩展建议

1. **富文本支持**：扩展支持更多格式（粗体、斜体等）
2. **国际化**：支持不同语言的换行规则
3. **主题定制**：支持不同的显示主题和样式
4. **性能监控**：添加性能监控和优化建议
5. **单元测试**：完善的测试覆盖率

## 相关文件

- `src/utils/textUtils.tsx` - 工具函数实现
- `src/components/ChatWindow/index.tsx` - 聊天窗口应用
- `src/pages/Complain/Content/index.tsx` - 投诉管理应用
- `src/pages/Complain/Bind/index.tsx` - 绑定推送应用
- `src/utils/textUtils-README.md` - 本说明文档
