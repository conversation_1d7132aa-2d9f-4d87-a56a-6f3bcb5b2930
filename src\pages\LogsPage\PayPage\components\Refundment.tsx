import {useState} from 'react';
import { Modal, Form, Input } from 'antd';
import TextArea from 'antd/lib/input/TextArea';

const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 14 },
    },
};

const Refundement = ({
    visible,
    formValues,
    onRefund,
    onCancel,
})=>{
    const [form] = Form.useForm();
    const [formVals, setFormVals] = useState({ ...formValues});
    const onSubmit = ()=>{
        onRefund(formVals);
    }

    return (
        <Modal 
            destroyOnClose
            width={450}
            open={visible}
            title={'退款'}
            centered={true}
            onCancel={() => onCancel()}
            bodyStyle={{
                maxHeight: '60vh',
                overflow: 'auto',
                // padding: '10px 0'
            }}
            okText="确定"
            onOk={onSubmit}
        >
            <Form 
                form={form}
                {...formItemLayout}
                initialValues={{
                    refundAmount: formVals.refundAmount,
                    refundReason: formVals.refundReason,
                }}
                onValuesChange={(changedValues)=> { setFormVals({...formVals, ...changedValues}) }}
            >
                <Form.Item name="refundAmount" label={"退款金额(元)"} labelAlign="right" rules={[{ required: true }]} >
                    <Input readOnly={true}/>
                </Form.Item>
                {/* <Form.Item name="isTest" label="退款秘钥" labelAlign="right" rules={[{ required: true }]} >
                    <Input  />
                </Form.Item> */}
                <Form.Item name="refundReason" label="退款描述" labelAlign="right">
                    <TextArea  autoSize={{minRows:4}}/>
                </Form.Item>
            </Form>
        </Modal>
    )
}

export default Refundement;