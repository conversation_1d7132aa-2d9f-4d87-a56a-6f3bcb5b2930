import { ModalForm , ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const MoveToTop = ({ modalVisible, onCancel, onSubmit, values, mutiValues, mode}) => {
    const { fetchDeptDoctorList } = useModel('queue');
    const { fetchDicList } = useModel('dictionary');
    return (
        <ModalForm
            title={mode === 'mutil'? '批量置顶': '患者置顶'}
            layout="horizontal"
            width={640}
            open={modalVisible}
            modalProps={{
                destroyOnClose: true,
                onCancel: () => onCancel(),
                maskClosable: false,
            }}
            onFinish={onSubmit}
            {...formItemLayout}
            initialValues={{ ...values, patientIDs: mutiValues.map(item => item.patientID), patientMoveType: null}}
            className="_modal-wrapper"
        >
           { mode === 'single' ? 
           <>
                <ProFormText name="patientID" label="患者ID" hidden/>
                <ProFormText name="deptCode" label="科室代码" readonly/>
                <ProFormText name="patientName" label="患者姓名" readonly/>
                <ProFormSelect
                    name="doctorCode"
                    label="就诊医生"
                    placeholder="请选择就诊医生"
                    fieldProps={{
                        showSearch: true
                    }}
                    request={() => fetchDeptDoctorList({deptCode: values.deptCode})}
                />
            </> : <ProFormText name="patientIDs" label="患者ID" hidden/>  }
            <ProFormSelect
                rules={[
                    {
                        required: true,
                        message: '置顶原因为必填项',
                    }
                ]}
                name="patientMoveType"
                label="置顶原因"
                placeholder="请选择患者置顶原因"
                request={() => fetchDicList('PatientMoveType', 'number')}
            />
        </ModalForm>
    );
};

export default MoveToTop;