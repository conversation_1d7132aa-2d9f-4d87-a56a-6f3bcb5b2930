import {
  UploadOutlined
} from '@ant-design/icons';
import { But<PERSON>, Select} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { queryVersionCheckList, addVersionFile } from '@/services/api/system';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import UploadForm from './components/UploadForm';
import { handleOperateMethod } from '@/utils/index';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加上传
 * @param fields
 */
const handleUpload = (fields: VERSION.VersionListItem) => handleOperateMethod(addVersionFile, fields, 'upload');

const TableList = () => {
  /** 上传更新信息的弹窗 */
  const [modalVisible, handleModalVisible] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<VERSION.VersionListItem>();
  const { dicList, fetchDicList } = useModel('dictionary');

  useEffect(() => {
    fetchDicList('deviceType', 'number');
  }, []);

  const columns: ProColumns<VERSION.VersionListItem>[]  = [
    {
      title: '版本序号',
      dataIndex: 'versionCode',
      key: 'versionCode',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '版本号',
      dataIndex: 'versionNo',
    },
    {
      title: '版本说明',
      dataIndex: 'versionDesc',
      hideInSearch: true,
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      width: 150,
      renderText: (_, record) => {
        return record.deviceTypeName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select {...rest} placeholder="请选择">
            {dicList &&
              dicList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`deviceType${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
    },
  ];
  return (
    <PageContainer>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <UploadOutlined /> 上传
          </Button>,
        ]}
        request={queryVersionCheckList}
        columns={columns}
      />
      {modalVisible && (
        <UploadForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpload(value);

            if (success) {
              handleModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleModalVisible(false);
          }}
          modalVisible={modalVisible}
        />
      )}
    </PageContainer>
  );
};

export default TableList;
