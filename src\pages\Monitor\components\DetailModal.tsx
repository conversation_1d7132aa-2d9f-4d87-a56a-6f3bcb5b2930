import ProDescriptions from '@ant-design/pro-descriptions';
import { Modal, Button} from 'antd';

type DateValueType = Partial<MONITOR.DataListItem>;

type DetailModalProps = {
	onCancel: () => void;
	modalVisible: boolean;
  data: DateValueType
};

const DetailModal: React.FC<DetailModalProps> = ({ data, modalVisible, onCancel }) => {
  return (
    <Modal
      width={950}
      open={modalVisible}
      onCancel={onCancel}
      maskClosable
      footer={
        [<Button type="default" key="colse" onClick={onCancel}>关闭</Button>]
      }
    >
      <ProDescriptions column={2} title="订单信息" tooltip={`${data.insuredName}${data.deptName??''}购药记录`} bordered size='small'>
        <ProDescriptions.Item label="药店名称">
          {data.deptName}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="医保机构编码">
          {data.deptCode}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="交易设备SN号">
          {data.deviceSN}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="业务单据号">
          {data.businessID}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="交易时间" valueType="dateTime">
          {data.createTime}
        </ProDescriptions.Item>
        <ProDescriptions.Item
          label="状态"
          valueEnum={{
            '0': { text: '正常', status: 'Success' },
            '1': {
              text: '异常',
              status: 'Error',
            },
          }}
        >
          {data.state}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="核验时间" valueType="dateTime">
          {data.verifTime}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="备注">
          {data.remark}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="购药人姓名">
          {data.insuredName}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="购药人身份证">
          {data.insuredIDCard}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="参保区域">
          {data.insuredOrg}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="购药人年龄">
          {data.insuredAge}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="交易金额" valueType="money">
          {data.totalAmount}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="自费金额" valueType="money">
          {data.privateAmount}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="医保统筹金额" valueType="money">
          {data.medicalAmount}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="医保个账金额" valueType="money">
          {data.medicalPrivateAmount}
        </ProDescriptions.Item>
      </ProDescriptions>
    </Modal>
  );
};

export default DetailModal;
