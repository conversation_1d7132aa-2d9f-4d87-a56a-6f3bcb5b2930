declare namespace BILLING {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 开单项目
    type BillingListItem = {        
        isTest?: boolean; // 是否测试数据
        saID?: string;      // 机构ID
        apiVersion?: string;// 版本号
        patientID?: string;    // 患者ID
        deviceCode?: string;     // 设备号
        name?: string; // 项目名称
        code?: string;  // 项目代码
        pyCode?: string; // 拼音搜索
        subCode?: string; // 项目子代码
        sort?: string;  // 项目排序
        type?: string;     // 类型
        id?: string; // ID
        createTime?: string; // 创建时间
        state?: number;    // 状态
        hospitalName?: string;
    };
    // 项目列表
    type BillingList = {
        /** 列表的内容 **/
        data?: BillingListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}