// 系统管理相关查询接口 处理SELECT中的查询数据
import {
  useState,
  useCallback
} from 'react';
import {
  queryRoleList,
  queryMenuList,
  getRolePer,
} from '@/services/api/system';
import {
  message
} from 'antd'

export type ListType = {
  value?: string;
  label?: string;
  key?: string;
  title?: string
};

export default () => {
  const [roleList, setRoleList] = useState<ListType[]>([]);
  const [menuList, setMenuList] = useState<ListType[]>([]);
  const [roleMenuList, setRoleMenuList] = useState([])
  // 查询角色列表方法
  const fetchRoleList = useCallback(async () => {
    const result = await queryRoleList({
      current: 1,
      pageSize: 999,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el:{[key: string]: any }) => {
        return{
          label: el.roleName,
          value: el.roleID,
        };
      });
      setRoleList(data)
      return data
    } else {
      message.error('请求角色信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询系统功能菜单
  const fetchMenuList = useCallback(async (sysType) => {
    const result = await queryMenuList({
      sysType
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          key: el.perCode,
          title: el.permiName
        }
      })
      setMenuList(data)
      return data
    } else {
      return false
    }
  }, []);

  // 查询角色菜单
  const fetchRoleMenuList = useCallback(async (roleID) => {
    const result = await getRolePer({
      roleID
    });
    if (result.code === '0' && result.data) {
      setRoleMenuList(result.data)
      return result.data
    } else {
      return false
    }
  }, []);
  
  return {
    roleList,
    fetchRoleList,
    menuList,
    fetchMenuList,
    roleMenuList,
    fetchRoleMenuList
  };
};
