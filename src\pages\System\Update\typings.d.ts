declare namespace VERSION {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 更新详情
    type VersionListItem = {
        ID?: number,  // ID
        deviceType?: number,  // 设备类型
        deviceTypeName?: string, // 设备类型名称
        fileSrc?: string,     // 文件地址
        versionDesc?: string,  // 更新地址
        src?: string,   // 相对路径
        createTime?: string,
        lastModifyTime?: string,
        versionNo?: string,   // 版本号
        isUpdate?: number,    // 是否更新
        versionCode?: number,  // 版本code
        moduleSrc?: string
    };
    // 更新列表
    type VersionList = {
        /** 列表的内容 **/
        data?: VersionListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}