import { request } from '@umijs/max';

/***************** 1对1聊天功能接口 *****************/

// 聊天会话对象类型
export interface ChatConversation {
  id?: number;
  conversationId: string;
  user1Id: string;
  user2Id: string;
  lastMessageContent?: string;
  lastMessageType?: number; // 1-文字，2-图片
  lastMessageTime?: string;
  otherUserId?: string;
  otherUserName?: string;
  otherUserAvatar?: string;
  unreadCount?: number;
  user1UnreadCount?: number;
  user2UnreadCount?: number;
  status?: number;
  createTime?: string;
  updateTime?: string;
}

// 聊天消息对象类型
export interface ChatMessage {
  id?: number;
  messageId: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  messageType: number; // 1-文字，2-图片
  content: string;
  imageUrl?: string;
  fileSize?: number;
  sendStatus?: number; // 1-发送中，2-发送成功，3-发送失败
  readStatus?: number; // 0-未读，1-已读
  status?: number;
  createTime: string;
  updateTime?: string;
  senderName?: string;
  senderAvatar?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  code: string;
  msg: string;
  data: T;
}

/**
 * 创建或获取会话
 * @param params 
 */
export async function createOrGetConversation(params: {
  user1Id: string;
  user2Id: string;
}): Promise<ApiResponse<ChatConversation>> {
  return request('/wyyx/chat/conversation/create', {
    method: 'POST',
    data: params,
  });
}

/**
 * 发送文字消息
 * @param params 
 */
export async function sendTextMessage(params: {
  senderId: string;
  receiverId: string;
  content: string;
}): Promise<ApiResponse<ChatMessage>> {
  return request('/wyyx/chat/message/text', {
    method: 'POST',
    data: params,
  });
}

/**
 * 将File对象转换为base64字符串
 * @param file
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        // 移除data:image/jpeg;base64,前缀，只保留base64数据
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = error => reject(error);
  });
}

/**
 * 发送图片消息（使用base64）
 * @param params
 */
export async function sendImageMessage(params: {
  senderId: string;
  receiverId: string;
  imageFile: File;
}): Promise<ApiResponse<ChatMessage>> {
  try {
    // 将图片文件转换为base64
    const base64Data = await fileToBase64(params.imageFile);

    return request('/wyyx/chat/message/image', {
      method: 'POST',
      data: {
        senderId: params.senderId,
        receiverId: params.receiverId,
        imageBase64: base64Data,
        fileName: params.imageFile.name,
        fileSize: params.imageFile.size,
        fileType: params.imageFile.type,
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    throw new Error('图片转换失败: ' + (error as Error).message);
  }
}

/**
 * 获取聊天记录
 * @param params 
 */
export async function getChatHistory(params: {
  conversationId: string;
  page?: number;
  size?: number;
}): Promise<ApiResponse<ChatMessage[]>> {
  return request('/wyyx/chat/message/history', {
    method: 'GET',
    params: {
      page: 1,
      size: 20,
      ...params,
    },
  });
}

/**
 * 获取会话列表
 * @param params 
 */
export async function getConversationList(params: {
  userId: string;
  page?: number;
  size?: number;
}): Promise<ApiResponse<ChatConversation[]>> {
  return request('/wyyx/chat/conversation/list', {
    method: 'GET',
    params: {
      page: 1,
      size: 20,
      ...params,
    },
  });
}

/**
 * 标记消息为已读
 * @param params 
 */
export async function markMessagesAsRead(params: {
  conversationId: string;
  userId: string;
}): Promise<ApiResponse<string>> {
  return request('/wyyx/chat/message/read', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取未读消息数
 * @param params 
 */
export async function getUnreadCount(params: {
  conversationId: string;
  userId: string;
}): Promise<ApiResponse<number>> {
  return request('/wyyx/chat/message/unread-count', {
    method: 'GET',
    params,
  });
}

/**
 * 获取消息详情
 * @param params 
 */
export async function getMessageDetail(params: {
  messageId: string;
}): Promise<ApiResponse<ChatMessage>> {
  return request('/wyyx/chat/message/detail', {
    method: 'GET',
    params,
  });
}

/**
 * 获取会话详情
 * @param params 
 */
export async function getConversationDetail(params: {
  conversationId: string;
}): Promise<ApiResponse<ChatConversation>> {
  return request('/wyyx/chat/conversation/detail', {
    method: 'GET',
    params,
  });
}

// 工具函数：格式化时间显示
export function formatMessageTime(timestamp: string | number): string {
  const date = new Date(typeof timestamp === 'string' ? timestamp : timestamp);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  // 一分钟内
  if (diff < 60 * 1000) {
    return '刚刚';
  }
  
  // 一小时内
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`;
  }
  
  // 今天
  if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }
  
  // 昨天
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  if (date.toDateString() === yesterday.toDateString()) {
    return `昨天 ${date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })}`;
  }
  
  // 一周内
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return `${weekdays[date.getDay()]} ${date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })}`;
  }
  
  // 超过一周
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 工具函数：判断是否需要显示时间分隔符
export function shouldShowTimeGroup(currentTime: string, previousTime?: string): boolean {
  if (!previousTime) return true;
  
  const current = new Date(currentTime);
  const previous = new Date(previousTime);
  
  // 超过5分钟显示时间分隔符
  return current.getTime() - previous.getTime() > 5 * 60 * 1000;
}
