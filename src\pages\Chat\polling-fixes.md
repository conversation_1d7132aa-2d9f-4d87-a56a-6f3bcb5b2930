# 轮询功能问题修复

## 修复的问题

### 1. 切换会话时轮询问题

#### 问题描述
- 切换会话时，上一个会话的消息轮询没有正确停止
- 可能导致多个会话同时轮询，造成资源浪费和数据混乱

#### 解决方案
```typescript
// 选择会话时的处理逻辑
const handleSelectConversation = useCallback(async (conversation: ChatConversation) => {
  // 如果选择的是同一个会话，直接返回
  if (selectedConversation?.conversationId === conversation.conversationId) {
    return;
  }
  
  // 停止当前消息轮询
  stopMessagePolling();
  
  // 设置新的选中会话
  setSelectedConversation(conversation);
  
  // 加载聊天记录
  await loadChatHistory(conversation.conversationId, true);
  
  // 处理已读状态...
}, [selectedConversation, stopMessagePolling, loadChatHistory, currentUserId]);

// 监听选中会话变化，自动开始/停止消息轮询
useEffect(() => {
  if (selectedConversation && isPollingEnabled) {
    const timer = setTimeout(() => {
      startMessagePolling();
    }, 100);
    
    return () => {
      clearTimeout(timer);
      stopMessagePolling();
    };
  } else {
    stopMessagePolling();
    return () => {};
  }
}, [selectedConversation, isPollingEnabled, startMessagePolling, stopMessagePolling]);
```

#### 修复效果
- ✅ 切换会话时自动停止上一个会话的轮询
- ✅ 延迟启动新会话的轮询，确保状态更新完成
- ✅ 避免重复选择同一会话时的无效操作

### 2. 页面滚动冲突问题

#### 问题描述
- 轮询获取新消息后自动滚动到底部
- 用户手动滚动查看历史消息时，轮询会强制滚动回底部
- 影响用户查看历史消息的体验

#### 解决方案

##### 2.1 滚动状态检测
```typescript
// 检查是否在底部
const isScrolledToBottom = () => {
  if (!messagesContainerRef.current) return true;
  const container = messagesContainerRef.current;
  const threshold = 50; // 50px的容差
  return container.scrollHeight - container.scrollTop - container.clientHeight <= threshold;
};

// 处理用户滚动
const handleScroll = useCallback(() => {
  if (!messagesContainerRef.current) return;
  
  // 检查是否在底部
  const atBottom = isScrolledToBottom();
  setShouldAutoScroll(atBottom);
  
  // 设置用户正在滚动的状态
  setIsUserScrolling(true);
  
  // 1秒后重置用户滚动状态
  scrollTimeoutRef.current = setTimeout(() => {
    setIsUserScrolling(false);
  }, 1000);
}, []);
```

##### 2.2 智能滚动控制
```typescript
// 滚动到最新消息
const scrollToBottom = (force = false) => {
  // 如果用户正在手动滚动且不是强制滚动，则不自动滚动
  if (!force && !shouldAutoScroll) {
    return;
  }

  // 执行滚动逻辑...
};
```

##### 2.3 滚动指示器
```typescript
{/* 滚动状态指示器 */}
{!shouldAutoScroll && (
  <div className="scroll-indicator" onClick={() => scrollToBottom(true)}>
    <Button type="primary" size="small" shape="round">
      回到底部
    </Button>
  </div>
)}
```

#### 修复效果
- ✅ 用户滚动到顶部查看历史消息时，不会被强制滚动到底部
- ✅ 用户在底部时，新消息会自动滚动显示
- ✅ 提供"回到底部"按钮，方便用户快速回到最新消息
- ✅ 平滑的滚动动画和视觉反馈

## 技术实现细节

### 1. 状态管理
```typescript
// 轮询控制状态
const [isPollingEnabled, setIsPollingEnabled] = useState(true);
const [pollingInterval, setPollingInterval] = useState(1000);

// 滚动控制状态
const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
const [isUserScrolling, setIsUserScrolling] = useState(false);

// 定时器引用
const conversationPollingRef = useRef<NodeJS.Timeout | null>(null);
const messagePollingRef = useRef<NodeJS.Timeout | null>(null);
const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
```

### 2. 事件监听
```typescript
// 滚动事件监听
useEffect(() => {
  const container = messagesContainerRef.current;
  if (container) {
    container.addEventListener('scroll', handleScroll);
    return () => {
      container.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }
  return () => {};
}, [handleScroll]);
```

### 3. 生命周期管理
```typescript
// 组件卸载时清理所有资源
useEffect(() => {
  return () => {
    stopConversationPolling();
    stopMessagePolling();
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
  };
}, []);
```

## 用户体验改进

### 1. 智能滚动行为
- **在底部时**：新消息自动滚动显示
- **查看历史时**：不会被强制滚动，保持当前位置
- **手动滚动后**：1秒内不自动滚动，避免干扰

### 2. 视觉反馈
- **滚动指示器**：当不在底部时显示"回到底部"按钮
- **平滑动画**：滚动指示器的淡入淡出效果
- **阴影效果**：按钮有适当的阴影，提升视觉层次

### 3. 交互优化
- **点击回到底部**：一键快速滚动到最新消息
- **容差设置**：50px的容差，避免过于敏感的滚动检测
- **防抖处理**：滚动状态的防抖，避免频繁状态更新

## 性能优化

### 1. 函数优化
- 使用 `useCallback` 包装事件处理函数
- 避免不必要的重新渲染
- 合理的依赖数组设置

### 2. 定时器管理
- 及时清理所有定时器
- 避免内存泄漏
- 组件卸载时的完整清理

### 3. 事件监听优化
- 正确添加和移除事件监听器
- 避免重复绑定事件
- 使用防抖减少频繁调用

## 测试建议

### 1. 切换会话测试
1. 选择会话A，等待轮询开始
2. 快速切换到会话B
3. 检查会话A的轮询是否停止
4. 检查会话B的轮询是否正常开始

### 2. 滚动行为测试
1. 在聊天底部时发送消息，检查是否自动滚动
2. 滚动到历史消息区域，等待轮询更新
3. 检查是否保持在当前位置，不被强制滚动
4. 点击"回到底部"按钮，检查是否正确滚动

### 3. 性能测试
1. 长时间使用聊天功能
2. 检查内存使用情况
3. 验证定时器是否正确清理
4. 测试页面切换时的资源释放

## 相关文件

- `src/pages/Chat/index.tsx` - 会话轮询逻辑修复
- `src/components/ChatWindow/index.tsx` - 滚动冲突修复
- `src/components/ChatWindow/index.less` - 滚动指示器样式
