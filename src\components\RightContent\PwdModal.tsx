import { ModalForm, ProFormText} from '@ant-design/pro-form';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
}

type FormValueType = Partial<USER.UserListItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
};

const PwdForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, onSubmit }) => {

  return (
    <ModalForm
      title="密码修改"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={onSubmit}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      <ProFormText name="pwd" rules={[{ required: true, message: '旧密码为必填项' }]} placeholder="请输入旧密码" label="输入旧密码" /> 
      <ProFormText name="newPwd" rules={[{ required: true, message: '新密码为必填项' }, 
        ({ getFieldValue }) => ({
          validator(_, value) {
            if (!value || getFieldValue('pwd') !== value) {
              return Promise.resolve();
            }
            return Promise.reject(new Error('新密码不能与旧密码相同'));
          },
        })]} placeholder="请输入新密码" label="输入新密码" /> 
      <ProFormText name="reNewPwd" rules={[{ required: true, message: '请再输入一次新密码' }, 
        ({ getFieldValue }) => ({
          validator(_, value) {
            if (!value || getFieldValue('newPwd') === value) {
              return Promise.resolve();
            }
            return Promise.reject(new Error('新密码输入的不一致！'));
          },
        })]} placeholder="请再输入一次新密码" label="重复新密码" /> 
    </ModalForm>
  );
};

export default PwdForm;