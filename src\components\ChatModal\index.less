.chat-modal {
  .ant-modal-content {
    height: 600px;
    display: flex;
    flex-direction: column;
  }

  .ant-modal-header {
    flex-shrink: 0;
    border-bottom: 1px solid #f0f0f0;
    
    .chat-modal-title {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .patient-info {
        font-size: 12px;
        color: #666;
        font-weight: normal;
      }
    }
  }

  .ant-modal-body {
    flex: 1;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .chat-modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .chat-window {
      height: 100%;
      border: none;
      border-radius: 0;
      box-shadow: none;
      
      .chat-header {
        display: none; // 隐藏聊天窗口的标题，使用Modal的标题
      }
      
      .chat-messages {
        flex: 1;
        max-height: calc(100vh - 200px);
      }
      
      .chat-input {
        border-top: 1px solid #f0f0f0;
        background: #fafafa;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chat-modal {
    .ant-modal {
      width: 95% !important;
      margin: 10px auto;
    }
    
    .ant-modal-content {
      height: calc(100vh - 40px);
    }
    
    .chat-modal-content {
      .chat-window {
        .chat-messages {
          max-height: calc(100vh - 160px);
        }
      }
    }
  }
}
