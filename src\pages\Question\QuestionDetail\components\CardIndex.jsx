import React, { useState } from 'react';
import { connect, history } from 'umi';
import { RollbackOutlined } from '@ant-design/icons';
import { Button, Card, message  } from 'antd';
import { QnTitle, QnRemark, QnActions, QnSubmitButton } from './CardItem';
import QnQuestions from './QnQuestions';
import "../style.less";

export const QuestionFuncContext = React.createContext();

const CardIndex =({
    detailType,
    history,
    question: { editing, updateQuesValues, questionLists },
    dispatch,
	handleSubmitQuestionnaire,
	handleSaveQuestionnaire
})=>{
	
    const [questions, setQuestions] = useState(editing.questions || [])
    const [addAreaVisible, setAddAreaVisible] = useState(false);
	const [questionsOrigin, setQuestionsOrigin] = useState(questionLists || [])

    const setQuestionsnanire=(key, value)=>{
		dispatch({
            type: "question/setEditingDetails",
            payload: {
                ...editing,
                [key]: value
            }
        })
	}

    const handleUpdateFields=(data)=>{
        setQuestions(data);
        setQuestionsnanire("questions",data);
		setQuestionsOrigin(data);
		dispatch({
			type: "question/setQuestionLists",
			payload: data
		})
    }

    const handleUpdateQustionsLists=(data)=>{
        let tempArr = [...questions];
        let newArr = tempArr.concat(data);
        setAddAreaVisible(false);
        handleUpdateFields(newArr);
    }
    const handleAddInput=()=>{
		const newQuestion = {
			// type: 'input',
			type: 1,
			title: '填空题',
			// text: '',
			required: false,
		};
        handleUpdateQustionsLists(newQuestion)

	}

	const handleAddRadio=()=>{
		const newQuestion = {
			// type: 'radio',
			type: 3,
			title: '单选题',
			options: [{ text: '选项一' }, { text: '选项二' }, { text: '选项三' }, { text: '选项四' }],
			// data: []
		};
        handleUpdateQustionsLists(newQuestion)
	}

	const handleAddCheckbox=()=>{
		const newQuestion = {
			// type: 'checkbox',
			type: 2,  
			title: '多选题',
			options: [{ text: '选项一' }, { text: '选项二' }, { text: '选项三' }, { text: '选项四' }],
			// data: []
		};
        handleUpdateQustionsLists(newQuestion)
	}

	const handleAddTextArea=()=>{
		const newQuestion = {
			// type: 'textarea',
			type: 4,
			title: '文本题',
			// text: '',
			required: false
		};
        handleUpdateQustionsLists(newQuestion)
	}

    const handleOptionChange=(e, questionIndex, optionIndex)=>{
        let tempArr = [...questions];
		tempArr[questionIndex].options[optionIndex].text = e.target.value;
        handleUpdateFields(tempArr);
	}

	const handleQuestionChange=(e, questionIndex)=>{
        let tempArr = [...questions];
		tempArr[questionIndex].title = e.target.value;
        handleUpdateFields(tempArr);

	}

    const handleRemoveOption=(questionIndex, optionIndex, id)=>{
        let tempArr = [...questions];
		// tempArr[questionIndex].options.splice(optionIndex, 1);
		// handleUpdateFields(tempArr);
		if(detailType==="update" && id){
			let options = tempArr[questionIndex].options || [];
			options.forEach((ele)=>{
				if(ele.id===id){
					ele.state = 9;
				}
			})
			tempArr[questionIndex].options = options;
		}else{
			tempArr[questionIndex].options.splice(optionIndex, 1);
		}
		handleUpdateFields(tempArr);
	}

    const handleAddOption=(questionIndex)=>{
        let tempArr = [...questions];
		const newOption = { text: '新选项' };
		tempArr[questionIndex].options.push(newOption);
		handleUpdateFields(tempArr);
	}

    const handleTextRequire=(e, questionIndex)=>{
		console.log(e)
        let tempArr = [...questions];
		tempArr[questionIndex].required = e.target.checked;
		handleUpdateFields(tempArr);
	}

    const handleTextChange=(e, questionIndex)=>{
        let tempArr = [...questions];
		tempArr[questionIndex].text = e.target.value;
        handleUpdateFields(tempArr);
	}


    // 上移/下移
	const handleShiftQuestion=(questionIndex, num)=>{
        let tempArr = [...questions];	
        const shiftQuestion = tempArr.splice(questionIndex, 1)[0];
		tempArr.splice(questionIndex + num, 0, shiftQuestion);
        handleUpdateFields(tempArr);
	}

    // 复制
	const handleCopyQuestion=(questionIndex, id)=>{
        // console.log(questions)
		let tempArr = [...questions];
		let tempObj = tempArr[questionIndex];
		const copy = Object.assign({}, {
			title: tempObj.title,
			required: tempObj.required,
			sort: tempObj.sort,
			type: tempObj.type,
			state: tempObj.state,
			options: tempObj.options,
		});
		if (tempObj.type === 3 || tempObj.type === 2 ) {
			copy.options = copy.options.slice(0);
			let newarr = [];
			copy.options.forEach((el,elind)=>{
				newarr.push({
					text: el.text,
					sort: el.sort || elind+1,
					state: el.state || 1,

				})
			})
			copy.options = newarr;
		}
		tempArr.splice(questionIndex + 1, 0, copy);
		handleUpdateFields(tempArr);
	}

    // 删除
    const handleRemoveQuestion=(questionIndex,id)=>{
		let tempArr = [...questions];
		// tempArr.splice(questionIndex, 1);
		// handleUpdateFields(tempArr);
		// 假如是编辑界面,更改删除字段的state
		if(detailType === "update" && id){
			// let arr = [...questionsOrigin];
			let newarr = tempArr[questionIndex].options || [];
			newarr.forEach((ele) => {
				ele.state = 9;
			})
			tempArr.options = newarr;
			tempArr[questionIndex].state = 9;
		}else{
			tempArr.splice(questionIndex, 1);

		}
		handleUpdateFields(tempArr);

	}

    // 添加问题
    const handleAddQuestion=()=>{
        setAddAreaVisible(!addAreaVisible)
	}
	
    return (
        <Card
            className="content-card"
            title={
                <QnTitle
                    questionnaireValues={editing}
                    setQuestionsnanire={setQuestionsnanire}
                />
            }
            extra={<Button type="primary" onClick={() => history.replace('/question/list')}><RollbackOutlined />返回</Button>}
            id="content-card-id"
        >
            <QnRemark
                questionnaireValues={editing}
                setQuestionsnanire={setQuestionsnanire}
            />
            <div style={{ padding: 20, borderTop: '2px solid #ccc', borderBottom: '2px solid #ccc' }}>
                <div style={{ marginBottom: 20 }} id="questionsBox">
					<QuestionFuncContext.Provider 
						value={{
							handleQuestionChange,
							handleOptionChange,
							handleRemoveOption,
							handleAddOption,
							handleTextRequire,
							handleShiftQuestion,
							handleCopyQuestion,
							handleRemoveQuestion,
							handleTextChange,
						}}
					>
						<QnQuestions questions={questions} />
					</QuestionFuncContext.Provider>
                </div>
                <QnActions
                    addAreaVisible={QnActions}
                    handleAddInput={handleAddInput}
                    handleAddRadio={handleAddRadio}
                    handleAddCheckbox={handleAddCheckbox}
                    handleAddTextArea={handleAddTextArea}                
                />

                {/* <div className="addQuestion" style={{ width: '100%', height: '100%', padding: 30, background: '#eee', textAlign: 'center' }} onClick={handleAddQuestion}>
                    { addAreaVisible ? <> <CloseOutlined /> 取消添加</> : <><PlusOutlined /> 添加问题 </> }
                </div> */}
            </div>
            <QnSubmitButton
                detailType={detailType}
                setQuestionsnanire={setQuestionsnanire}
                handleSubmit={handleSubmitQuestionnaire}
                handleSave={handleSaveQuestionnaire}
            />
        </Card>
    )


}

export default connect(({ question }) => ({
	question
}))(CardIndex);

