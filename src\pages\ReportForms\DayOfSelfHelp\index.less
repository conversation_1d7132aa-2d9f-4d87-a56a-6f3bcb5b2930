.reconciliation-box{
    .ant-card-body{
        padding-top: 10px !important;
    }
    .center{
        width: 100%;
        margin: 0 auto;
        // font-size: 18px;
        .recon-table{
            width: 100%;
            font-size: 14px;
            font-family: 宋体;
            border-collapse: collapse;
            border: 1px solid ;
            position: relative;
            th,td{
                border: 1px solid #000;
                text-align: center;
                height: 28px;
                width: 80px;
                line-height: 20px;
            }
            th{
                color: #000;
            }
            // td{
            //     width: 80px;
            // }
        }
        .content{
            width: 100%;
            display: flex;
            justify-content: flex-end;
        }
        .ant-descriptions-item{
            padding-top: 10px;
            padding-bottom: 10px;
        }
        .ant-descriptions-item-content,.ant-descriptions-item-
        {
            font-size: 16px;
            line-height: 25px;
        }


        .search-list-row{
            width: 100%;
            // border: 1px solid red;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            font-size: 16px;
            margin-bottom: 10px;
        }
        // .scroll-tbody-wrapper{
        //     // position:absolute;
        //     width: 100%;
        //     height: 50vh;
        //     overflow: auto;
        //     border: 1px solid red;
        // }
        .ant-pagination{
            text-align: center;
        }
    }



}


