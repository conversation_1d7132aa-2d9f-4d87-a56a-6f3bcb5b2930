import {
  InfoCircleOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { But<PERSON>, Modal, Drawer, Select, Tag, message } from 'antd';
import { useState, useRef, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryComplaintList, updateComplaint } from '@/services/api/complain';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';
import { handleOperateMethod } from '@/utils/index';
import { renderTableCellWithLineBreaks } from '@/utils/textUtils';

const { Option } = Select;

export type ListType = {
  value?: any;
  label?: string;
};

const ComplaintContent = () => {
  const { initialState } = useModel('@@initialState');
  /** 修改投诉处理的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示投诉详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 当前行数据 */
  const [currentRow, setCurrentRow] = useState<COMPLAIN.ComplaintListItem>();

  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);

  /**
   * 处理投诉
   * @param fields
   */
  const handleUpdate = async (fields: COMPLAIN.ComplaintListItem) => {
    const hide = message.loading('正在处理');
    try {
      const response = await updateComplaint({ ...fields });
      hide();
      if (response.code === '0') {
        message.success('处理成功');
        return true;
      } else {
        message.error(response.msg || '处理失败');
        return false;
      }
    } catch (error) {
      hide();
      message.error('处理失败请重试！');
      return false;
    }
  };

  const columns: ProColumns<COMPLAIN.ComplaintListItem>[] = [
    {
      title: 'ID',
      dataIndex: 'ID',
      hideInSearch: true,
    },
    {
      title: '患者姓名',
      dataIndex: 'name',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
    },
    {
      title: '投诉内容',
      dataIndex: 'complaint',
      hideInSearch: true,
      ellipsis: true,
      width: 200,
      render: (text: string) => renderTableCellWithLineBreaks(text, 100, 'content'),
    },
    {
      title: '投诉时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: '投诉时间',
      dataIndex: 'createTime',
      hideInTable: true,
      hideInDescriptions: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            beginTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.map((c: ListType) => {
          return {
            value: c.value,
            label: c.label,
          };
        });
        return (
          <Select placeholder="请选择" showSearch optionFilterProp="label" options={options} allowClear />
        );
      },
      colSize: 2,
    },
    {
      title: '处理状态',
      dataIndex: 'status',
      hideInSearch: true,
      render: (_, record) => {
        let color = 'default';
        let text = '未处理';
        if (record.status === 1) {
          color = 'success';
          text = '已处理';
        } else if (record.status === 2) {
          color = 'error';
          text = '已忽略';
        }
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '处理状态',
      dataIndex: 'status',
      hideInTable: true,
      hideInDescriptions: true,
      valueEnum: {
        0: { text: '未处理' },
        1: { text: '已处理' },
        2: { text: '已忽略' },
      },
    },
    {
      title: '处理时间',
      dataIndex: 'handleTime',
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: '处理人',
      dataIndex: 'handleUser',
      hideInSearch: true,
    },
    {
      title: '处理备注',
      dataIndex: 'handleRemark',
      hideInSearch: true,
      ellipsis: true,
      render: (text: string) => renderTableCellWithLineBreaks(text, 150, 'remark'),
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        record.status === 0 && (
          <a
            key="handle"
            onClick={() => {
              setCurrentRow(record);
              handleUpdateModalVisible(true);
            }}
          >
            处理
          </a>
        ),
        record.status === 0 && (
          <a
            key="ignore"
            onClick={() => {
              Modal.confirm({
                title: '确认忽略',
                content: '确定要忽略这条投诉吗？',
                onOk: async () => {
                  const success = await handleUpdate({
                    ...record,
                    status: 2,
                    handleUser: initialState?.currentUser?.realName || '',
                    handleRemark: '已忽略',
                  });
                  if (success) {
                    actionRef.current?.reload?.();
                  }
                },
              });
            }}
          >
            忽略
          </a>
        ),
      ].filter(Boolean),
    },
  ];

  return (
    <PageContainer header={{ breadcrumb: {} }}>
      <ProTable
        headerTitle={'患者投诉管理'}
        actionRef={actionRef}
        rowKey="id"
        scroll={{ x: 1500 }}
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        request={queryComplaintList}
        columns={columns}
      />
      
      {updateModalVisible && (
        <UpdateForm
          updateModalVisible={updateModalVisible}
          values={currentRow || {}}
          onCancel={() => {
            handleUpdateModalVisible(false);
            setCurrentRow(undefined);
          }}
          onSubmit={async (value) => {
            const success = await handleUpdate({
              ...currentRow,
              ...value,
              status: 1,
              handleUser: initialState?.currentUser?.realName || '',
            });
            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<COMPLAIN.ComplaintListItem>
            column={1}
            title={`投诉详情 - ${currentRow.patientName}`}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<COMPLAIN.ComplaintListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default ComplaintContent;
