@import '~antd/es/style/themes/default.less';

.editTitle:hover {
  background: #ecf6fd;
}

.addQuestion:hover {
  cursor: pointer;
}

.questionsWrap:hover {
  background: #ecf6fd;
  .icon-btns{
    display: block;
  }
}

.deleteOption:hover {
  cursor: pointer;
}

.addOption:hover {
  border: 1px dotted #7ec2f3;
  cursor: pointer;
}

.questionOperate:hover {
  cursor: pointer;
}

.catalogue-card{
  height: 90vh;
  overflow: hidden;
  .ant-card-body{
    padding: 0 !important;
    .item-title{
      // white-space: nowrap;
      // overflow: hidden;
      // text-overflow: ellipsis;
      word-break: break-all;
      text-align: justify;
      &.actived{
        color: #1890ff;
      }
    }
  }
}


.content-card::-webkit-scrollbar{
  width: 0;
  height: 0;
}
.content-card{
  height: 90vh;
  scrollbar-width: none;
  overflow: auto;
  .ant-radio-disabled .ant-radio-inner {
    background-color: #ffffff;
    border-color: #d9d9d9 !important;
    cursor: text;
  }
  .ant-radio-disabled .ant-radio-input{
    cursor: text;
  }
  .ant-checkbox-disabled .ant-checkbox-inner {
    background-color: #ffffff;
    cursor: text;
  }
  .ant-checkbox-disabled .ant-checkbox-input{
    cursor: text;
  }
  .ant-input[disabled]{
    background-color: #ffffff;
  }
  .icon-btns{
    display: none;
    font-size: 20px;
    margin-right: 10px;
    color: rgba(0, 0, 0, 0.65);
    cursor: pointer;
  }
  .icon-btns:hover{
    color: #1890ff;
  }
}

#content-card-id{
  position: relative;
  // .questionsWrap{
  //   border: 1px solid red;
  // }
}

.topic-logic-modal{
  .ant-modal-body{
    padding-top: 0;
    .topic-logic-collapse{
      background-color: transparent;
    }
    .no-condition-box{
      display: flex;
      align-items: center;
      .title{
        margin-right: 10px;
      }
    }
  }
}


.scorll-box{
  max-height: 80vh;
  overflow: auto;
  scrollbar-width: none;
}
.scorll-box::-webkit-scrollbar, #questionsBox::-webkit-scrollbar{
  width: 0;
  height: 0;
}

// #questionsBox{
//   max-height: 37vh;
//   overflow: auto;
//   scrollbar-width: none;
// }