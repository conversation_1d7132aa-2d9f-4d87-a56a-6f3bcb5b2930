import { useState } from 'react';
import { ModalForm, ProFormUploadButton, ProFormTextArea, ProFormText, ProFormSelect} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import {message} from 'antd';
import defaultSettings from '../../../../../config/defaultSettings';
import type { UploadFile } from 'antd/es/upload/interface';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const UPLOAD_URL = `/${defaultSettings.apiName}/system/uploadFiles.np`

type FormValueType = Partial<VERSION.VersionListItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
  values: FormValueType;
};

const UploadForm: React.FC<UploadFormProps> = ({ modalVisible, values, onCancel, onSubmit }) => {
  const { fetchDicList } = useModel('dictionary');
  const [ fileList, setFileList ] = useState<UploadFile[]>([])
  const [ filePath, setFilePath ] = useState<string>('')

  const uploadProps = {
    name: 'multipartFile',
    data: {
        moduleSrc: "system/version",
        uploadType: "2",
    },
    action: UPLOAD_URL,
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info: any) {
      let proUploadList = [...info.fileList];
      proUploadList = proUploadList.slice(-1);
      setFileList(proUploadList)
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 文件上传成功。`);
        setFilePath(info.file.response.data)
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件上传失败。`);
      }
    },
  };

  return (
    <ModalForm
      title="上传文件"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={(values) => {
        const data = {
          versionNo: values.versionNo,
          versionDesc: values.versionDesc,
          deviceType: values.deviceType,
          moduleSrc: filePath
        }
        return onSubmit(data)
      }}
      {...formItemLayout}
      initialValues={{ ...values }}
      className="_modal-wrapper"
    >
      <ProFormUploadButton name="file" label="版本更新文件" fieldProps={uploadProps} fileList={fileList} max={1} rules={[
        {
          required: true,
          message: '更新文件为必填项',
        },
      ]}/>
      <ProFormSelect
        name="deviceType"
        label="设备类型"
        rules={[
          {
            required: true,
            message: '设备类型为必填项',
          },
        ]}
        placeholder="请选择设备类型"
        request={() => fetchDicList('deviceType', 'string')}
      />
      <ProFormText name="versionNo" label="版本号" rules={[
          {
            required: true,
            message: '版本号为必填项',
          },
        ]}/>
      <ProFormTextArea name="versionDesc" label="版本说明" fieldProps={{rows:4}}/>
    </ModalForm>
  );
};

export default UploadForm;
