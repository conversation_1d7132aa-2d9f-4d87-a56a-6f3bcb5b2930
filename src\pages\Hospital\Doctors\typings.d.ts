declare namespace DOCTOR {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 医生详情
    type DoctorListItem = {        
        doctorCode?: string, // 医生代码 同HIS系统
        doctorName?: string,   // 医生姓名
        saID?: string,      // 机构ID
        hospitalName?: string, // 机构名称
        gender?: number,    // 医生性别
        docID?: number,     // 医生ID
        doctorNameSpelling?: string,  // 医生简拼
        icon?: any,      // 医生头像
        bindDeptName?: string, // 绑定科室
        title?: string,     // 医生职称
        createTime?: string, // 创建时间
        lastModifyTime?: string, // 上一次修改时间
        createUser?: number, // 创建人
        bindDept?: string[],  // 医生关联科室列表
        state?: number,     // 状态
        key?: number,        // key  同 医生ID
        deptName?: string,  // 科室名称
        deptCode?: string   // 科室代码
        expertNo?: number   // 类型
    };
    // 医生列表
    type DoctorList = {
        /** 列表的内容 **/
        data?: DoctorListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}