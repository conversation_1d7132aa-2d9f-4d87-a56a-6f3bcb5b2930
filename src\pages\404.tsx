import { history, useModel} from '@umijs/max';
import { Button, Result } from 'antd';
import React from 'react';


const goPage = (permissionsList: any) => {
  console.log(permissionsList)
  let startPage = '';
  if(permissionsList[0]?.routes.length > 0){
    startPage = permissionsList[0].routes[0].path
  }else{
    startPage = permissionsList[0].path
  }
  history.push( startPage );
}



const NoFoundPage: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  return <Result
    status="404"
    title="404"
    subTitle="Sorry, the page you visited does not exist."
    extra={
      <Button type="primary" onClick={() => goPage(initialState.menuData)}>
        Back Home
      </Button>
    }
  />
  };

export default NoFoundPage;
