declare namespace AREA {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 诊区详情
    type AreaListItem = {        
        overNum?: number,   // 过号规则
        backNum?: number,   // 回诊规则
        doctorNum?: number, // 候诊患者数量
        state?: number,   // 状态
        stateName?: string, // 状态名称
        digID?: number,     // 诊区ID
        key?: number,   // key 同诊区ID
        digAreaName?: number,   // 诊区名称
        digAreaCode?: string,  // 诊区代码
        remark?: string,    // 备注
        roomScreenNotice?: string,  // 诊间屏文字配置
        largeScreenNotice?: string,  // 诊区大屏文字提示
        hospitalName?: string, // 机构名称
        deptIDs?: string,  // 绑定科室
        diagDeptInfo?: Array<diagDeptInfo>
    };

    type diagDeptInfo = {
        deptName?: string,
        deptID?: number,
    }
    // 诊区列表
    type AreaList = {
        /** 列表的内容 **/
        data?: AreaListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}