import { ProFormText, ProFormSelect } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchDicList } = useModel('dictionary')
  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '项目名称为必填项',
          },
        ]}
        name="name"
        label="项目名称"
        placeholder="请输入项目名称"
      />
      <ProFormSelect 
        rules={[
          {
            required: true,
            message: '项目类型为必填项',
          },
        ]} 
        name="type" 
        label="项目类型"
        placeholder="请选择项目类型"
        request={() => fetchDicList('CreatePaymentType')}
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: '项目代码为必填项',
          },
        ]}
        name="code"
        label="项目代码"
        placeholder="请输入项目代码"
      />
      <ProFormText
        name="sort"
        label="项目序号"
        placeholder="请输入项目序号"
      />
      <ProFormText
        name="state"
        label="状态"
        hidden
      />
    </>
  );
};

export default FormList;
