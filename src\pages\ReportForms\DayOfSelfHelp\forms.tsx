//报表
import './index.less';

const ReportForms = ({
    data: {
        singleDevice,
        total,
    },
    date,
}) => {
   
    // const onShowSizeChange = (current, pageSize) => {
    //     console.log(current, pageSize);
    //     changeSize(current,pageSize)
    // }
    // const onChange = page => {
    //     console.log(page);
    //     changeSize(page,pageSize)

    // };
    return (
        <div className="center">
            <h1 style={{ textAlign: 'center', fontSize: '22px', lineHeight: '35px',marginBottom:0 }}><b>自助机日结报表</b></h1>
            {/* <Descriptions>
                    <Descriptions.Item label="机构应用名称" span={2}>台州市中西医结合医院-本部-京威盛自助机</Descriptions.Item>
                    <Descriptions.Item label="日报日期" style={{ textAlign: 'right' }}>2021-04-06</Descriptions.Item>
                </Descriptions> */}
            <div className="search-list-row">
                <span>汇总日期：</span>
                <span>{date}</span>
            </div>
            <table className="recon-table">
                <thead>
                    <tr>
                        <th rowSpan={2} style={{ width: 100 }}>操作员</th>
                        <th colSpan={2}>总计</th>
                        <th colSpan={4}>微信</th>
                        <th colSpan={4}>支付宝</th>
                        <th colSpan={4}>银联</th>
                    </tr>
                    <tr>
                        <th>总笔数</th>
                        <th>总金额</th>

                        {/* <th>成功<br />笔数</th>
                        <th>成功<br />金额</th>
                        <th>退款<br />笔数</th>
                        <th>退款<br />金额</th> */}
                        {/* <th>笔数</th> */}
                        {/* <th>金额</th> */}

                        {/* <th>总笔数</th> */}
                        <th>总金额</th>
                        <th>成功<br />笔数</th>
                        {/* <th>实际收款金额</th> */}
                        <th>退款金额</th>
                        <th>退款<br />笔数</th>
                        {/* <th>非当日<br/>金额</th> */}

                        {/* <th>总笔数</th> */}
                        <th>总金额</th>
                        <th>成功<br />笔数</th>
                        {/* <th>实际收款金额</th> */}
                        <th>退款金额</th>
                        <th>退款<br />笔数</th>
                        {/* <th>非当日<br/>金额</th> */}

                        {/* <th>总笔数</th> */}
                        <th>总金额</th>
                        <th>成功<br />笔数</th>
                        {/* <th>实际收款金额</th> */}
                        <th>退款金额</th>
                        <th>退款<br />笔数</th>
                        {/* <th>非当日<br/>金额</th> */}
                    </tr>

                </thead>
                <tbody className="scroll-tbody-wrapper">
                    {singleDevice && Object.keys(singleDevice).filter(item => item !== 'total').map((item, index) => {
                        return (
                            <tr key={index}>
                                <td style={{ color: '#000' }}>{item}</td>
                                <td>{singleDevice[item]?.countDevice}</td>
                                <td>{singleDevice[item]?.totalDevice}</td>
                                <td>{singleDevice[item]?.totalWeiXin}</td>
                                <td>{singleDevice[item]?.countWeixin}</td>
                                <td>{singleDevice[item]?.totalWeiXinRefund}</td>
                                <td>{singleDevice[item]?.countWeiXinRefund}</td>
                                <td>{singleDevice[item]?.totalZhiFuBao}</td>
                                <td>{singleDevice[item]?.countZhiFuBao}</td>
                                <td>{singleDevice[item]?.totalZhiFuBaoRefund}</td>
                                <td>{singleDevice[item]?.countZhiFuBaoRefund}</td>
                                <td>{singleDevice[item]?.totalYinLian}</td>
                                <td>{singleDevice[item]?.countYinLian}</td>
                                <td>{singleDevice[item]?.totalYinLianRefund}</td>
                                <td>{singleDevice[item]?.countYinLianRefund}</td>
                            </tr>
                        )
                    })}
                
                    {/* 合计 */}
                    { singleDevice && Object.keys(singleDevice).length > 0 && 
                        <tr>
                            <td style={{ color: '#000' }}>合计</td>
                            <td>{singleDevice?.total?.countDevice}</td>
                            <td>{singleDevice?.total?.totalDevice}</td>
                            <td>{singleDevice?.total?.totalWeiXin}</td>
                            <td>{singleDevice?.total?.countWeixin}</td>
                            <td>{singleDevice?.total?.totalWeiXinRefund}</td>
                            <td>{singleDevice?.total?.countWeiXinRefund}</td>
                            <td>{singleDevice?.total?.totalZhiFuBao}</td>
                            <td>{singleDevice?.total?.countZhiFuBao}</td>
                            <td>{singleDevice?.total?.totalZhiFuBaoRefund}</td>
                            <td>{singleDevice?.total?.countZhiFuBaoRefund}</td>
                            <td>{singleDevice?.total?.totalYinLian}</td>
                            <td>{singleDevice?.total?.countYinLian}</td>
                            <td>{singleDevice?.total?.totalYinLianRefund}</td>
                            <td>{singleDevice?.total?.countYinLianRefund}</td>
                        </tr>
                    }
                    {/* <tr>
                        <td colSpan="17" style={{height: 40}}>
                            <Pagination 
                                defaultCurrent={1} 
                                total={singleDevice.length} 
                                showSizeChanger 
                                onShowSizeChange={onShowSizeChange}
                                onChange={onChange}
                            />
                        </td>
                    </tr> */}
                </tbody>

            </table>
            {/* <Descriptions>
                    <Descriptions.Item label="制表时间">2021-04-13 16:18:52</Descriptions.Item>
                    <Descriptions.Item label="制表"></Descriptions.Item>
                    <Descriptions.Item label="审核"></Descriptions.Item>
                </Descriptions> */}
        </div>


    )
}


export default ReportForms;