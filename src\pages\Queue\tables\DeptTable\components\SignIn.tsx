/**
 * 适用于签到要选择医生的弹出框
 */
import { ModalForm , ProFormSelect, ProFormText} from '@ant-design/pro-form';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const SignIn = ({ mode, modalVisible, onCancel, onSubmit, values, doctorList}) => {

    return (
        <ModalForm
            title={mode === 'delay'? '过号签到选择': '回诊签到选择'}
            layout="horizontal"
            width={640}
            open={modalVisible}
            modalProps={{
                destroyOnClose: true,
                onCancel: () => onCancel(),
                maskClosable: false,
            }}
            onFinish={onSubmit}
            {...formItemLayout}
            initialValues={{ ...values}}
            className="_modal-wrapper"
        >
            <ProFormText name="patientID" label="患者ID" hidden/>
            <ProFormText name="doctorCode" label="原医生代码" hidden/>
            <ProFormText name="deptCode" label="原科室代码" hidden/>
            <ProFormText name="checkCode" label="挂号序号" hidden/>
            <ProFormText name="patientName" label="患者姓名" readonly/>
            <ProFormText name="deptName" label="原就诊科室" readonly/>
            { doctorList.length > 0 && <ProFormSelect
                name="newDoctorCode"
                label="签到的医生"
                options={doctorList.map(item => {
                    return {
                        label: `${item.doctorName}-${item.deptName}`,
                        value: `${item.doctorCode}|${item.deptCode}`,
                    }
                })}
                placeholder="原挂号医生未登录，请选择其他医生签到"
            /> }
        </ModalForm>
    );
};

export default SignIn;