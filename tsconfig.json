{"compilerOptions": {"outDir": "build/dist", "module": "esnext", "target": "esnext", "lib": ["esnext", "dom"], "sourceMap": true, "baseUrl": ".", "jsx": "react-jsx", "esModuleInterop": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "importHelpers": true, "noImplicitReturns": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "allowJs": true, "skipLibCheck": true, "experimentalDecorators": true, "strict": false, "noImplicitThis": false, "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"]}}, "include": ["mock/**/*", "src/**/*", "playwright.config.ts", "tests/**/*", "test/**/*", "__test__/**/*", "typings/**/*", "config/**/*", ".eslintrc.js", ".prettierrc.js", "jest.config.js", "mock/*"], "exclude": ["node_modules", "build", "dist", "scripts", "src/.umi/*", "webpack", "jest"]}