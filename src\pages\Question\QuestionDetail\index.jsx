/*
	问卷详情-应用于编辑，新建功能
*/
import React from 'react';
import { Modal, Card, message, Row, Col } from 'antd';
import { connect, history } from 'umi';
import { addQuestionnaire, editQuestionnaire } from '@/services/question'
import TipModal from '@/components/TipModal';
import Catalogue from './components/Catalogue';
import CardIndex from './components/CardIndex';
import './style.less';

/**
 * 添加问卷
 * @param fields
 */
 const handleAdd = async fields => {
	const hide = message.loading('正在添加问卷...');
	try {
		const response = await addQuestionnaire({ ...fields });
		if (response.code === '0') {
			hide();
			message.success('添加成功');
			return true;
		}
		message.error(response.msg);
		return false;
	} catch (error) {
		hide();
		message.error('添加失败请重试！');
		return false;
	}
};

/**
 * 修改问卷
 * @param fields
 */
const handleEdit = async fields => {
	const hide = message.loading('正在保存问卷...');
	try {
		const response = await editQuestionnaire({ ...fields });
		if (response.code === '0') {
			hide();
			message.success('修改成功');
			return true;
		}
		message.error(response.msg);
		return false;
	} catch (error) {
		hide();
		message.error('修改失败请重试！');
		return false;
	}
};

class Edit extends React.Component {
	constructor(props) {
		super(props);
		this.state = {
			isShowCacheModal: false,
			isCache: false,
		}
	}

	componentWillMount() {
		// // 拦截判断是否离开当前页面
		// window.addEventListener('beforeunload', this.beforeunloadHandler, false);
		// sessionStorage.removeItem('editing');
		// if(this.props.question.detailType==="add"){
		// 	let sessionEditing = sessionStorage.getItem('editing');
		// 	console.log(sessionEditing)
		// 	if (sessionEditing && JSON.stringify(sessionEditing) !== "{}") {
		// 		this.setState({
		// 			isShowCacheModal: true
		// 		})
		// 	}
		// }
	}

	componentDidMount() {
		// this.timer = setInterval(()=>{
		//   this.cacheData();
		// },5000)
		document.addEventListener("keydown", this.ctrlS);
		// window.addEventListener("beforeunload", this.beforeunloadHandler);
	}

	componentWillUnmount() {
		// 销毁拦截判断是否离开当前页面
		this.timer && clearInterval(this.timer);
		this.timer = null;
		document.removeEventListener("keydown", this.ctrlS)
		// window.removeEventListener("beforeunload", this.beforeunloadHandler, false);
		// this.props.dispatch({
		// 	type: "question/setEditingDetails",
		// 	payload: {},
		// })
	}



	beforeunloadHandler = (e) => {
		console.log(e)
		console.log("监听浏览器刷新");
		// let confirmationMessage = '您输入的内容尚未保存，确定离开此页面吗？';
		// (e || window.event).returnValue = confirmationMessage;
		// return confirmationMessage;
		// // localStorage.removeItem("editing")
		let sessionEditing = sessionStorage.getItem('editing');
		if (JSON.stringify(sessionEditing) !== "{}") {
			this.setState({
				isShowCacheModal: true
			})
		}
		
	}

	// 缓存数据--
	cacheData = () => {
		console.log("====保存数据=====")
		const { question, dispatch } = this.props;
		const { editing } = question || {};
		const setEditing = Object.assign({}, { ...editing });

		sessionStorage.setItem('editing', JSON.stringify(setEditing));

		dispatch({
			type: "question/setEditingDetails",
			payload: setEditing
		})
	}

	// ctrl+s缓存数据--
	ctrlS = (e) => {
		if (window.event) {
			e = window.event
		}
		let code = e.charCode || e.keyCode;
		if (code == 83 && (navigator.platform.match("Mac") ? e.metaKey : e.ctrlKey)) {
			e.preventDefault();
			this.cacheData();
			Modal.success({
				title: '保存成功'
			});
		}
	}

	handleSaveData=()=>{
		const { index } = this.state;
		// const { question } = this.props;
		// const { list } = question || {};
		// list[index] = Object.assign({}, this.state);
		// localStorage.list = JSON.stringify(list);
		this.cacheData();
		Modal.success({
			title: '保存成功'
		});
	}


	handleReleaseQuestionnaire() {
		const me = this;
		const { index, questions, date } = this.state;
		if (questions.length === 0) {
			Modal.warning({
				title: '请添加至少一个问题'
			});
		} else if (date === '') {
			Modal.warning({
				title: '请选择截止日期'
			});
		} else {
			Modal.confirm({
				title: '确定发布问卷吗？',
				content: `截止日期为${date}`,
				onOk() {
					list[index] = Object.assign({}, { ...me.state, stage: '发布中' });
					localStorage.list = JSON.stringify(list);
					window.location.reload();
					me.props.history.push('/');
				}
			});
		}
	}

	// 添加问卷和修改问卷需要删除字段的公共部分
	funDeleteFields = (data) => {
		data.hasOwnProperty('index') && (delete data.index)
		data.hasOwnProperty('text') && (delete data.text)
		data.hasOwnProperty('value') && (delete data.value)
		data.hasOwnProperty('createtime') && (delete data.createtime)
		data.hasOwnProperty('createuser') && (delete data.createuser)
		// data.hasOwnProperty('questionoptionsList') && (delete data.questionoptionsList);
		// data.hasOwnProperty('options') && (delete data.options);
	}

	// 提交问卷
	handleSubmitQuestionnaire = async () => {
		const { title, remark, questions } = this.props.question.editing || {};
		// console.log(editing)
		const questionVOList = JSON.parse(JSON.stringify(questions));
		questionVOList.forEach((item, itemind) => {
			item['required'] = item.required ? 1 : 2;
			item['questionoptionsVOList'] = []; //答案列表
			item['sort'] = itemind + 1;
			if (item.hasOwnProperty('options')) {
				item.options.forEach((el, eind) => {
					item['questionoptionsVOList'].push({
						title: el.text,
						state: 1,
						sort: eind + 1,
					})
				})
			}
			this.funDeleteFields(item);
		})
		const fields = {
			title,
			questionVOList,
			state: 1,
			describe: remark,
		}
		console.log(fields);
		const res = await handleAdd({ ...fields })
		if (res) {
			history.push('/question/list')
			sessionStorage.removeItem("editing");
			if(this.timer){
				clearInterval(this.timer);
				this.timer = null;
			}
		}

	}

	// 更新问卷
	handleSaveQuestionnaire = async () => {
		// console.log(editing)
		const { title, id, remark, questions, questionType, state } = this.props.question.editing || {};
		let newarr = JSON.parse(JSON.stringify(questions));
		let questionVOList = [...newarr];
		console.log(newarr);
		questionVOList.forEach((item,itemind) => {
			item['required'] = item.required ? 1 : 2;
			item['questionoptionsVOList'] = []; //答案列表
			item['state'] = item.state || 1;
			item['sort'] = itemind + 1;
			if (item.hasOwnProperty('options')) {
				item.options.forEach((el, eind) => {
					let title = el.text;
					el.hasOwnProperty('text') && (delete el.text);
					item['questionoptionsVOList'].push({
						...el,
						title,
						sort: eind + 1,
						state: el.state || 1,
					})
				})
			}
			item.hasOwnProperty('options') && (delete item.options)
			item.hasOwnProperty('questionoptionsList') && (delete item.questionoptionsList)
			this.funDeleteFields(item);
		})

		const fields = {
			id,
			title,
			state,
			questionType,
			questionVOList,
			describe: remark,
		}
		// fields.hasOwnProperty('options') && (delete fields.options)
		// fields.hasOwnProperty('questionoptionsList') && (delete fields.questionoptionsList)
		console.log(fields)
		const res = await handleEdit({ ...fields })
		if (res) {
			history.push('/question/list')
			sessionStorage.removeItem("editing")
			if(this.timer){
				clearInterval(this.timer);
				this.timer = null;
			}
		}
	}
	
	onTipOk = ()=>{
		this.setState({
			isShowCacheModal: false,
			isCache: true,
		})
	}

	// 锚点定位，聚焦
	scrollToAnchor = (anchorName, ind) => {
		if (anchorName) {
			let scrollElement = document.getElementById("content-card-id");    // 对应id的滚动容器
			let anchorElement = document.getElementById(anchorName);
			// if (anchorElement) { anchorElement.scrollIntoView({ behavior: 'smooth', block: 'start' }); }
			if (scrollElement) {
				let input = document.querySelector(`#${anchorName} .refinput`);
				input.focus();
				scrollElement.scrollTo({ top: anchorElement.offsetTop, left: anchorElement.offsetLeft, behavior: "smooth" });
			}
		}
	}

	handleOpenTopicLogic = (ind, item) => {
		const arr = [...this.state.questions];
		let newarr = arr.slice(ind + 1);
		this.setState({
			topicModalVisible: true,
			topicItemData: item,
			topicArr: newarr
		})
	}

	handleCancelTopicLogic = () => {
		this.setState({
			topicModalVisible: false
		})
	}

	render() {
		const { 
			isShowCacheModal, 
			isCache,
		} = this.state;

		const cacheTipModalProps = {
			visible: isShowCacheModal,
			title: '提示',
			content: '之前有未提交的内容，需要填充吗',
			width: 500,
			onCancel: () => {
				this.setState({ isShowCacheModal: false })
			},
			onOk: this.onTipOk
		}

		return (
			<>
				<Row gutter={10} >
					<Col span={4}>
						<Card title="问卷大纲" className="catalogue-card" >
							<Catalogue scrollToAnchor={this.scrollToAnchor} />
						</Card>
					</Col>
					<Col span={20}>
						<CardIndex
							isCache={isCache}
							history={history}
							detailType={this.props.question.detailType}
							questionnaireValues={this.props.question.editing}
							handleSubmitQuestionnaire={this.handleSubmitQuestionnaire}
							handleSaveQuestionnaire={this.handleSaveQuestionnaire}
						/>
					</Col>
				</Row>
				{isShowCacheModal && <TipModal {...cacheTipModalProps} />}
			</>
		);
	}
}

export default connect(({ question }) => ({
	question
}))(Edit); 