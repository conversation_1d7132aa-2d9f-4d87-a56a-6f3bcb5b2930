import { request } from '@umijs/max';
import defaultSettings from '../../../config/defaultSettings';

/** 设备管理 */
/** 获取设备列表 POST /device/getDeviceList.sp */
export async function queryDevicesList(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/device/getDeviceList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/** 设备管理 */
/** 获取设备详情 GET /device/getDeviceDetail.dp */
export async function queryDevicesDetail(params?: {[key: string]: any}) {
  const data = {
    ...params,
    date: new Date().getTime()
  }
  return request(`/${defaultSettings.apiName}/device/getDeviceDetail.dp`, {
    method: 'GET',
    params: data
  });
}
  
/** 添加设备 POST /device/addDevice.sp */
export async function addDevice(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/device/addDevice.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}
  
/** 修改设备 POST /device/updateDevice.sp */
export async function updateDevice(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/device/updateDevice.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}
  
/** 设备指令下发 POST /device/deviceCmd.sp */
export async function cmdDevice(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/device/deviceCmd.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}
  
/** 获取设备异常信息 GET /device/getDeviceFaultInfo.sp */
export async function queryDeviceFaultInfo(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/device/getDeviceFaultInfo.sp`, {
    method: 'GET',
    params,
  });
}
  
/** 获取设备异常信息推送 GET /device/getDeviceFaultInfo.sp */
export async function queryNotices(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/device/getDeviceErrorInfo.dp`, {
    ...params
  });
}
  