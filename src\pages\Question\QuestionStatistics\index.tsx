import React, { useState, useEffect, Suspense } from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import { Card, Button, Row, Col, Spin, Select, DatePicker, Space } from 'antd';
import { useModel, history } from '@umijs/max';
import { RollbackOutlined, Bar<PERSON><PERSON>Outlined, PieChartOutlined } from '@ant-design/icons';
import PageLoading from '@/components/PageLoading';
import { queryQuestionStatistics } from '@/services/api/question';
import './index.less';

const ProportionPie = React.lazy(() => import('./Components/ProportionPie'));

const { Option } = Select;
const { RangePicker } = DatePicker;

export type ListType = {
  value?: any;
  label?: string;
};

interface StatisticsData {
  questionId: number;
  questionTitle: string;
  questionType: number;
  options: string[];
  statistics: {
    option: string;
    count: number;
    percentage: number;
  }[];
}

const QuestionStatistics: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [loading, setLoading] = useState(false);
  const [statisticsData, setStatisticsData] = useState<StatisticsData[]>([]);
  const [selectedQuestionnaire, setSelectedQuestionnaire] = useState<string>();
  const [dateRange, setDateRange] = useState<any[]>([]);
  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);

  /**
   * 获取统计数据
   */
  const loadStatisticsData = async (params: any = {}) => {
    setLoading(true);
    try {
      const response = await queryQuestionStatistics({
        questionnaireId: selectedQuestionnaire,
        startDate: dateRange[0]?.format('YYYY-MM-DD'),
        endDate: dateRange[1]?.format('YYYY-MM-DD'),
        ...params,
      });
      
      if (response.code === '0') {
        setStatisticsData(response.data || []);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 返回问卷列表
   */
  const handleBack = () => {
    history.push('/question/list');
  };

  /**
   * 转换数据为饼图格式
   */
  const convertToPieData = (statistics: StatisticsData['statistics']) => {
    return statistics.map(item => ({
      x: item.option,
      y: item.count,
    }));
  };

  /**
   * 渲染统计图表
   */
  const renderStatisticsChart = (data: StatisticsData, index: number) => {
    const pieData = convertToPieData(data.statistics);
    const total = data.statistics.reduce((sum, item) => sum + item.count, 0);

    return (
      <Col span={12} key={data.questionId || index} style={{ marginBottom: 24 }}>
        <Suspense fallback={<PageLoading />}>
          <ProportionPie
            title={`${index + 1}. ${data.questionTitle}`}
            loading={loading}
            salesPieData={pieData}
            total={total}
            subTitle="回答总数"
            height={300}
          />
        </Suspense>
      </Col>
    );
  };

  return (
    <PageContainer
      header={{
        title: '问卷数据统计',
        breadcrumb: {},
        extra: [
          <Button key="back" icon={<RollbackOutlined />} onClick={handleBack}>
            返回问卷列表
          </Button>,
        ],
      }}
    >
      {/* 筛选条件 */}
      <Card style={{ marginBottom: 24 }}>
        <Space wrap>
          <span>问卷选择:</span>
          <Select
            placeholder="请选择问卷"
            style={{ width: 200 }}
            value={selectedQuestionnaire}
            onChange={setSelectedQuestionnaire}
            allowClear
          >
            <Option value="1">满意度调查问卷</Option>
            <Option value="2">服务质量评价</Option>
            <Option value="3">医疗体验调查</Option>
          </Select>

          <span>时间范围:</span>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            style={{ width: 240 }}
          />

          <span>医院:</span>
          <Select
            placeholder="请选择医院"
            style={{ width: 160 }}
            allowClear
          >
            {hosList.map((hospital: ListType) => (
              <Option key={hospital.value} value={hospital.value}>
                {hospital.label}
              </Option>
            ))}
          </Select>

          <Button 
            type="primary" 
            icon={<BarChartOutlined />}
            onClick={() => loadStatisticsData()}
            loading={loading}
          >
            查询统计
          </Button>
        </Space>
      </Card>

      {/* 统计图表 */}
      <Card
        className="question-card"
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <PieChartOutlined style={{ marginRight: 8 }} />
            <span>问卷数据分析</span>
          </div>
        }
        loading={loading}
      >
        {statisticsData.length > 0 ? (
          <Row gutter={[24, 24]}>
            {statisticsData.map((data, index) => renderStatisticsChart(data, index))}
          </Row>
        ) : (
          <div style={{ 
            textAlign: 'center', 
            padding: '60px 20px',
            color: '#999',
          }}>
            <PieChartOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div style={{ fontSize: 16, marginBottom: 8 }}>暂无统计数据</div>
            <div style={{ fontSize: 12 }}>
              请选择问卷和时间范围后点击"查询统计"
            </div>
          </div>
        )}
      </Card>

      {/* 数据概览 */}
      {statisticsData.length > 0 && (
        <Card 
          title="数据概览" 
          style={{ marginTop: 24 }}
        >
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                  {statisticsData.length}
                </div>
                <div style={{ color: '#666' }}>问题总数</div>
              </div>
            </Col>
            <Col span={6}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                  {statisticsData.reduce((sum, item) => 
                    sum + item.statistics.reduce((s, stat) => s + stat.count, 0), 0
                  )}
                </div>
                <div style={{ color: '#666' }}>回答总数</div>
              </div>
            </Col>
            <Col span={6}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                  {statisticsData.filter(item => item.questionType === 1).length}
                </div>
                <div style={{ color: '#666' }}>单选题</div>
              </div>
            </Col>
            <Col span={6}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                  {statisticsData.filter(item => item.questionType === 2).length}
                </div>
                <div style={{ color: '#666' }}>多选题</div>
              </div>
            </Col>
          </Row>
        </Card>
      )}
    </PageContainer>
  );
};

export default QuestionStatistics;
