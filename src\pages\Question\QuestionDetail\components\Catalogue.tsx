import React, { useState, useEffect } from 'react';
import { List, Typography, Card } from 'antd';
import { FileTextOutlined } from '@ant-design/icons';

export interface CatalogueProps {
  questions: QUESTION.QuestionListItem[];
  onQuestionClick?: (index: number) => void;
  selectedIndex?: number;
}

interface CatalogueItem {
  name: string;
  anchorID: string;
  index: number;
}

const Catalogue: React.FC<CatalogueProps> = ({
  questions = [],
  onQuestionClick,
  selectedIndex = -1,
}) => {
  const [data, setData] = useState<CatalogueItem[]>([]);
  const [selectItem, setSelectItem] = useState<number>(selectedIndex);

  useEffect(() => {
    const catalogueData: CatalogueItem[] = [];
    questions.forEach((item, index) => {
      if (item.title) {
        catalogueData.push({
          name: item.title,
          anchorID: `anchorID${index}`,
          index,
        });
      }
    });
    setData(catalogueData);
  }, [questions]);

  useEffect(() => {
    setSelectItem(selectedIndex);
  }, [selectedIndex]);

  const handleClick = (anchorName: string, index: number) => {
    setSelectItem(index);
    onQuestionClick?.(index);
    
    // 滚动到对应问题
    const element = document.getElementById(anchorName);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FileTextOutlined style={{ marginRight: 8 }} />
          问题目录
        </div>
      }
      size="small"
      className="catalogue-card"
    >
      <div className="scroll-box" style={{ maxHeight: 400, overflowY: 'auto' }}>
        <List
          size="small"
          dataSource={data}
          locale={{ emptyText: '暂无问题' }}
          renderItem={(item, index) => (
            <List.Item
              style={{
                padding: '8px 0',
                borderBottom: index === data.length - 1 ? 'none' : '1px solid #f0f0f0',
              }}
            >
              <span 
                className={selectItem === item.index ? 'item-title actived' : 'item-title'} 
                style={{
                  cursor: 'pointer',
                  display: 'block',
                  padding: '8px 12px',
                  borderRadius: 4,
                  transition: 'all 0.3s',
                  backgroundColor: selectItem === item.index ? '#e6f7ff' : 'transparent',
                  color: selectItem === item.index ? '#1890ff' : '#333',
                  border: selectItem === item.index ? '1px solid #91d5ff' : '1px solid transparent',
                  fontSize: 12,
                  lineHeight: '16px',
                  wordBreak: 'break-all',
                }}
                onClick={() => handleClick(item.anchorID, item.index)}
                onMouseEnter={(e) => {
                  if (selectItem !== item.index) {
                    e.currentTarget.style.backgroundColor = '#f5f5f5';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectItem !== item.index) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <Typography.Text 
                  strong={selectItem === item.index}
                  style={{ 
                    color: selectItem === item.index ? '#1890ff' : '#666',
                    marginRight: 4,
                  }}
                >
                  Q{index + 1}.
                </Typography.Text>
                <span title={item.name}>
                  {item.name.length > 20 ? `${item.name.substring(0, 20)}...` : item.name}
                </span>
              </span>
            </List.Item>
          )}
        />
      </div>
      
      {data.length === 0 && (
        <div style={{ 
          textAlign: 'center', 
          padding: '40px 20px',
          color: '#999',
        }}>
          <FileTextOutlined style={{ fontSize: 24, marginBottom: 8 }} />
          <div>暂无问题</div>
          <div style={{ fontSize: 12, marginTop: 4 }}>
            请先添加问题
          </div>
        </div>
      )}
    </Card>
  );
};

export default Catalogue;
