import React, { useState, useRef, useEffect } from 'react';
import { Select, Progress, Statistic, Card, Row, Col } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import { useModel } from '@umijs/max';
import { LikeOutlined, DislikeOutlined, BarChartOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import ProTable from '@ant-design/pro-table';
import { queryGoodValue } from '@/services/api/question';

const { Option } = Select;

export type ListType = {
  value?: any;
  label?: string;
};

interface FavorableRateData {
  datea?: string;
  zps?: number; // 总评数
  haoPingLv?: string; // 好评率
  hps?: number; // 好评数量
  hospitalName?: string;
}

const FavorableRate: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const actionRef = useRef<ActionType>();
  const [summaryData, setSummaryData] = useState<{
    totalCount: number;
    goodCount: number;
    goodRate: number;
  }>({
    totalCount: 0,
    goodCount: 0,
    goodRate: 0,
  });
  
  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);

  const columns: ProColumns<FavorableRateData>[] = [
    {
      title: '统计时间范围',
      dataIndex: 'datea',
      key: 'datea',
      hideInTable: true,
      renderFormItem: (item, { defaultRender, ...rest }) => {
        return (
          <Select {...rest} defaultValue="30" placeholder="请选择时间范围">
            <Option key="7" value="7">一周内</Option>
            <Option key="21" value="21">三周内</Option>
            <Option key="30" value="30">一个月内</Option>
            <Option key="90" value="90">一个季度内</Option>
          </Select>
        );
      },
    },
    {
      title: '机构名称',
      dataIndex: 'hospitalName',
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.map((c: ListType) => ({
          value: c.label,
          label: c.label,
        }));
        return (
          <Select placeholder="请选择机构" showSearch optionFilterProp="label" options={options} allowClear />
        );
      },
      width: 200,
    },
    {
      title: '统计日期',
      dataIndex: 'datea',
      key: 'statisticsDate',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '总评数',
      dataIndex: 'zps',
      key: 'zps',
      hideInSearch: true,
      width: 100,
      render: (value: number) => (
        <Statistic
          value={value || 0}
          valueStyle={{ fontSize: 16 }}
          prefix={<BarChartOutlined />}
        />
      ),
    },
    {
      title: '好评数量',
      dataIndex: 'hps',
      key: 'hps',
      hideInSearch: true,
      width: 100,
      render: (value: number) => (
        <Statistic
          value={value || 0}
          valueStyle={{ fontSize: 16, color: '#52c41a' }}
          prefix={<LikeOutlined />}
        />
      ),
    },
    {
      title: '差评数量',
      dataIndex: 'badCount',
      key: 'badCount',
      hideInSearch: true,
      width: 100,
      render: (_, record) => {
        const badCount = (record.zps || 0) - (record.hps || 0);
        return (
          <Statistic
            value={badCount}
            valueStyle={{ fontSize: 16, color: '#ff4d4f' }}
            prefix={<DislikeOutlined />}
          />
        );
      },
    },
    {
      title: '好评率',
      dataIndex: 'haoPingLv',
      key: 'haoPingLv',
      hideInSearch: true,
      width: 150,
      render: (value: string, record) => {
        const rate = parseFloat(value || '0');
        let status: 'success' | 'normal' | 'exception' = 'normal';
        if (rate >= 90) status = 'success';
        else if (rate < 70) status = 'exception';
        
        return (
          <div>
            <Progress
              percent={rate}
              size="small"
              status={status}
              format={(percent) => `${percent}%`}
            />
            <div style={{ marginTop: 4, fontSize: 12, color: '#666' }}>
              {record.hps || 0}/{record.zps || 0}
            </div>
          </div>
        );
      },
    },
  ];

  return (
    <PageContainer header={{ breadcrumb: {} }}>
      {/* 汇总统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总评价数"
              value={summaryData.totalCount}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="好评数量"
              value={summaryData.goodCount}
              prefix={<LikeOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="差评数量"
              value={summaryData.totalCount - summaryData.goodCount}
              prefix={<DislikeOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="整体好评率"
              value={summaryData.goodRate}
              precision={2}
              suffix="%"
              valueStyle={{ 
                color: summaryData.goodRate >= 90 ? '#52c41a' : summaryData.goodRate >= 70 ? '#faad14' : '#ff4d4f' 
              }}
            />
          </Card>
        </Col>
      </Row>

      <ProTable<FavorableRateData>
        headerTitle="好评率统计"
        actionRef={actionRef}
        rowKey={(record, index) => `${record.datea}-${record.hospitalName}-${index}`}
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        request={async (params) => {
          const response = await queryGoodValue({
            datea: params.datea || '30',
            hospitalName: params.hospitalName,
            ...params,
          });
          
          if (response.code === '0') {
            const data = response.data || [];
            
            // 计算汇总数据
            const totalCount = data.reduce((sum: number, item: any) => sum + (item.zps || 0), 0);
            const goodCount = data.reduce((sum: number, item: any) => sum + (item.hps || 0), 0);
            const goodRate = totalCount > 0 ? (goodCount / totalCount) * 100 : 0;
            
            setSummaryData({
              totalCount,
              goodCount,
              goodRate,
            });
            
            return {
              data: data,
              total: data.length,
              success: true,
            };
          }
          
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        columns={columns}
      />
    </PageContainer>
  );
};

export default FavorableRate;
