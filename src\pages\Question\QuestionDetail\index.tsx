import React, { useState, useEffect } from 'react';
import { Modal, Card, message, Row, Col, Button, Form, Input, Select, Space } from 'antd';
import { useModel, history, useSearchParams } from '@umijs/max';
import { PageContainer } from '@ant-design/pro-layout';
import { ArrowLeftOutlined, SaveOutlined, EyeOutlined } from '@ant-design/icons';
import { 
  addQuestionnaire, 
  editQuestionnaire, 
  queryListById 
} from '@/services/api/question';
import TipModal from './components/TipModal';
import Catalogue from './components/Catalogue';
import CardIndex from './components/CardIndex';
import './style.less';

const { Option } = Select;
const { TextArea } = Input;

export type ListType = {
  value?: any;
  label?: string;
};

const QuestionDetail: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [searchParams] = useSearchParams();
  const [form] = Form.useForm();
  
  const [loading, setLoading] = useState(false);
  const [questionnaireData, setQuestionnaireData] = useState<QUESTION.QuestionnaireListItem>({});
  const [questions, setQuestions] = useState<QUESTION.QuestionListItem[]>([]);
  const [tipModalVisible, setTipModalVisible] = useState(false);
  
  const { hosList, fetchHosList } = useModel('hospital');
  
  // 获取URL参数
  const id = searchParams.get('id');
  const mode = searchParams.get('mode') || 'view'; // add, edit, view, preview
  
  useEffect(() => {
    fetchHosList();
    if (id && (mode === 'edit' || mode === 'view' || mode === 'preview')) {
      loadQuestionnaireDetail();
    }
  }, [id, mode]);

  /**
   * 加载问卷详情
   */
  const loadQuestionnaireDetail = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const response = await queryListById({ id: parseInt(id) });
      if (response.code === '0') {
        setQuestionnaireData(response.data.questionnaire || {});
        setQuestions(response.data.questions || []);
        
        // 设置表单初始值
        form.setFieldsValue({
          title: response.data.questionnaire?.title,
          description: response.data.questionnaire?.description,
          saID: response.data.questionnaire?.saID,
        });
      } else {
        message.error(response.msg || '加载问卷详情失败');
      }
    } catch (error) {
      message.error('加载问卷详情失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 添加问卷
   */
  const handleAdd = async (fields: any) => {
    const hide = message.loading('正在添加问卷...');
    try {
      const response = await addQuestionnaire({ ...fields });
      if (response.code === '0') {
        hide();
        message.success('添加成功');
        return true;
      }
      message.error(response.msg || '添加失败');
      return false;
    } catch (error) {
      hide();
      message.error('添加失败请重试！');
      return false;
    }
  };

  /**
   * 修改问卷
   */
  const handleEdit = async (fields: any) => {
    const hide = message.loading('正在保存问卷...');
    try {
      const response = await editQuestionnaire({ ...fields });
      if (response.code === '0') {
        hide();
        message.success('修改成功');
        return true;
      }
      message.error(response.msg || '修改失败');
      return false;
    } catch (error) {
      hide();
      message.error('修改失败请重试！');
      return false;
    }
  };

  /**
   * 保存问卷
   */
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const questionnaireData = {
        ...values,
        questions: questions,
        creator: initialState?.currentUser?.realName || '',
      };

      let success = false;
      if (mode === 'add') {
        success = await handleAdd(questionnaireData);
      } else if (mode === 'edit') {
        success = await handleEdit({ ...questionnaireData, id: parseInt(id!) });
      }

      if (success) {
        history.push('/question/list');
      }
    } catch (error) {
      message.error('请完善问卷信息');
    }
  };

  /**
   * 返回列表
   */
  const handleBack = () => {
    history.push('/question/list');
  };

  /**
   * 预览问卷
   */
  const handlePreview = () => {
    setTipModalVisible(true);
  };

  /**
   * 添加问题
   */
  const handleAddQuestion = (question: QUESTION.QuestionListItem) => {
    setQuestions([...questions, { ...question, id: Date.now() }]);
  };

  /**
   * 更新问题
   */
  const handleUpdateQuestion = (index: number, question: QUESTION.QuestionListItem) => {
    const newQuestions = [...questions];
    newQuestions[index] = question;
    setQuestions(newQuestions);
  };

  /**
   * 删除问题
   */
  const handleDeleteQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    setQuestions(newQuestions);
  };

  const isReadOnly = mode === 'view' || mode === 'preview';
  const pageTitle = mode === 'add' ? '新增问卷' : mode === 'edit' ? '编辑问卷' : mode === 'preview' ? '预览问卷' : '问卷详情';

  return (
    <PageContainer
      header={{
        title: pageTitle,
        breadcrumb: {},
        extra: [
          <Button key="back" icon={<ArrowLeftOutlined />} onClick={handleBack}>
            返回
          </Button>,
          !isReadOnly && (
            <Button key="preview" icon={<EyeOutlined />} onClick={handlePreview}>
              预览
            </Button>
          ),
          !isReadOnly && (
            <Button key="save" type="primary" icon={<SaveOutlined />} onClick={handleSave}>
              保存
            </Button>
          ),
        ].filter(Boolean),
      }}
    >
      <Row gutter={24}>
        <Col span={18}>
          <Card title="问卷信息" loading={loading}>
            <Form
              form={form}
              layout="vertical"
              disabled={isReadOnly}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="title"
                    label="问卷标题"
                    rules={[{ required: true, message: '请输入问卷标题' }]}
                  >
                    <Input placeholder="请输入问卷标题" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="saID"
                    label="所属机构"
                    rules={[{ required: true, message: '请选择所属机构' }]}
                  >
                    <Select placeholder="请选择所属机构" showSearch optionFilterProp="label">
                      {hosList.map((item: ListType) => (
                        <Option key={item.value} value={item.value}>
                          {item.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item
                name="description"
                label="问卷描述"
                rules={[{ required: true, message: '请输入问卷描述' }]}
              >
                <TextArea rows={3} placeholder="请输入问卷描述" />
              </Form.Item>
            </Form>
          </Card>

          <Card title="问题列表" style={{ marginTop: 16 }}>
            <CardIndex
              questions={questions}
              onAddQuestion={handleAddQuestion}
              onUpdateQuestion={handleUpdateQuestion}
              onDeleteQuestion={handleDeleteQuestion}
              readOnly={isReadOnly}
            />
          </Card>
        </Col>
        
        <Col span={6}>
          <Catalogue
            questions={questions}
            onQuestionClick={(index) => {
              // 滚动到对应问题
              const element = document.getElementById(`question-${index}`);
              element?.scrollIntoView({ behavior: 'smooth' });
            }}
          />
        </Col>
      </Row>

      {tipModalVisible && (
        <TipModal
          visible={tipModalVisible}
          onCancel={() => setTipModalVisible(false)}
          questionnaire={questionnaireData}
          questions={questions}
        />
      )}
    </PageContainer>
  );
};

export default QuestionDetail;
