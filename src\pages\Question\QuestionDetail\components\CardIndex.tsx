import React, { useState, createContext } from 'react';
import { useModel, history } from '@umijs/max';
import { RollbackOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { But<PERSON>, Card, message, Space } from 'antd';
import { Qn<PERSON><PERSON>le, QnRemark, QnActions, QnSubmitButton } from './CardItem';
import QnQuestions from './QnQuestions';
import '../style.less';

export const QuestionFuncContext = createContext<any>(null);

export interface CardIndexProps {
  questions: QUESTION.QuestionListItem[];
  onAddQuestion?: (question: QUESTION.QuestionListItem) => void;
  onUpdateQuestion?: (index: number, question: QUESTION.QuestionListItem) => void;
  onDeleteQuestion?: (index: number) => void;
  readOnly?: boolean;
  detailType?: string;
  handleSubmitQuestionnaire?: () => void;
  handleSaveQuestionnaire?: () => void;
}

const CardIndex: React.FC<CardIndexProps> = ({
  questions: propQuestions = [],
  onAddQuestion,
  onUpdateQuestion,
  onDeleteQuestion,
  readOnly = false,
  detailType,
  handleSubmitQuestionnaire,
  handleSaveQuestionnaire,
}) => {
  const { initialState } = useModel('@@initialState');
  const [questions, setQuestions] = useState<QUESTION.QuestionListItem[]>(propQuestions);
  const [addAreaVisible, setAddAreaVisible] = useState(false);

  // 更新问题列表
  const handleUpdateFields = (data: QUESTION.QuestionListItem[]) => {
    setQuestions(data);
    onUpdateQuestion?.(0, data[0]); // 通知父组件更新
  };

  // 添加新问题
  const handleUpdateQuestionsLists = (data: QUESTION.QuestionListItem[]) => {
    const tempArr = [...questions];
    const newArr = tempArr.concat(data);
    setAddAreaVisible(false);
    handleUpdateFields(newArr);
    
    // 通知父组件
    data.forEach(question => {
      onAddQuestion?.(question);
    });
  };

  // 显示添加问题区域
  const handleAddInput = () => {
    setAddAreaVisible(true);
  };

  // 隐藏添加问题区域
  const handleCancelAdd = () => {
    setAddAreaVisible(false);
  };

  // 删除问题
  const handleDeleteQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    setQuestions(newQuestions);
    onDeleteQuestion?.(index);
  };

  // 更新单个问题
  const handleUpdateSingleQuestion = (index: number, question: QUESTION.QuestionListItem) => {
    const newQuestions = [...questions];
    newQuestions[index] = question;
    setQuestions(newQuestions);
    onUpdateQuestion?.(index, question);
  };

  // 返回列表
  const handleBack = () => {
    history.push('/question/list');
  };

  // 保存问卷
  const handleSave = () => {
    if (questions.length === 0) {
      message.error('请至少添加一个问题');
      return;
    }
    handleSaveQuestionnaire?.();
  };

  // 提交问卷
  const handleSubmit = () => {
    if (questions.length === 0) {
      message.error('请至少添加一个问题');
      return;
    }
    handleSubmitQuestionnaire?.();
  };

  const contextValue = {
    questions,
    setQuestions,
    addAreaVisible,
    setAddAreaVisible,
    handleUpdateFields,
    handleUpdateQuestionsLists,
    handleAddInput,
    handleCancelAdd,
    handleDeleteQuestion: handleDeleteQuestion,
    handleUpdateSingleQuestion,
    readOnly,
  };

  return (
    <QuestionFuncContext.Provider value={contextValue}>
      <div className="question-detail-container">
        {/* 问卷标题和描述 */}
        <Card className="question-header-card">
          <QnTitle readOnly={readOnly} />
          <QnRemark readOnly={readOnly} />
        </Card>

        {/* 问题列表 */}
        <Card 
          className="question-content-card"
          title="问题列表"
          extra={
            !readOnly && (
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleAddInput}
                disabled={addAreaVisible}
              >
                添加问题
              </Button>
            )
          }
        >
          <QnQuestions 
            questions={questions}
            onUpdateQuestion={handleUpdateSingleQuestion}
            onDeleteQuestion={handleDeleteQuestion}
            readOnly={readOnly}
          />
        </Card>

        {/* 操作按钮 */}
        {!readOnly && (
          <Card className="question-actions-card">
            <Space>
              <Button 
                icon={<RollbackOutlined />} 
                onClick={handleBack}
              >
                返回
              </Button>
              <Button 
                type="default" 
                icon={<SaveOutlined />}
                onClick={handleSave}
              >
                保存草稿
              </Button>
              <Button 
                type="primary" 
                onClick={handleSubmit}
              >
                发布问卷
              </Button>
            </Space>
          </Card>
        )}

        {/* 提交按钮组件 */}
        <QnSubmitButton 
          onSave={handleSave}
          onSubmit={handleSubmit}
          readOnly={readOnly}
        />

        {/* 操作组件 */}
        <QnActions 
          onBack={handleBack}
          onSave={handleSave}
          onSubmit={handleSubmit}
          readOnly={readOnly}
        />
      </div>
    </QuestionFuncContext.Provider>
  );
};

export default CardIndex;
