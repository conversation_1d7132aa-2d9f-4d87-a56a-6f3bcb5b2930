import React, { useState, useRef, useEffect } from 'react';
import { Button, message, Tag, Select, Drawer } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import { useModel } from '@umijs/max';
import { EyeOutlined, DownloadOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import ProTable from '@ant-design/pro-table';
import { 
  queryAnswerRecord, 
  getAnswerDisplayByRecord, 
  queryAnswerRecordPage 
} from '@/services/api/question';
import Answers from './Components/Answers';
import { parseParam } from '@/utils';
import moment from 'moment';
import 'moment/locale/zh-cn';
import './index.less';

const { Option } = Select;

export type ListType = {
  value?: any;
  label?: string;
};

const QuestionResult: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const actionRef = useRef<ActionType>();
  const [showDetail, setShowDetail] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<QUESTION.AnswerRecordItem>();
  const [answerDetail, setAnswerDetail] = useState<QUESTION.AnswerResultItem[]>([]);
  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);

  /**
   * 根据问卷记录查答题结果展示接口
   */
  const selectById = async (fields: { recordId: number }) => {
    const hide = message.loading('正在查询');
    try {
      const response = await getAnswerDisplayByRecord({ ...fields });
      if (response.code === '0') {
        hide();
        message.success('查询成功');
        return response;
      }
      message.error(response.msg || '查询失败');
      return false;
    } catch (error) {
      hide();
      message.error('查询失败请重试！');
      return false;
    }
  };

  /**
   * 导出Excel
   */
  const handleExportToExcel = async (fields: any) => {
    const hide = message.loading('正在导出');
    try {
      const paramsStr = parseParam(fields);
      const aLink = document.createElement('a');
      document.body.appendChild(aLink);
      aLink.style.display = 'none';
      aLink.href = `/${process.env.UMI_APP_API_NAME}/answer/exportAnswerRecord.dp?${paramsStr}`;
      aLink.download = `答卷记录_${moment().format('YYYY-MM-DD')}.xlsx`;
      aLink.click();
      document.body.removeChild(aLink);
      hide();
      message.success('导出成功');
    } catch (error) {
      hide();
      message.error('导出失败请重试！');
    }
  };

  /**
   * 查看答卷详情
   */
  const handleViewDetail = async (record: QUESTION.AnswerRecordItem) => {
    if (!record.id) return;
    
    const result = await selectById({ recordId: record.id });
    if (result && result.data) {
      setCurrentRecord(record);
      setAnswerDetail(result.data || []);
      setShowDetail(true);
    }
  };

  const columns: ProColumns<QUESTION.AnswerRecordItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '问卷名称',
      dataIndex: 'questionnaireName',
      ellipsis: true,
      width: 200,
    },
    {
      title: '答题人姓名',
      dataIndex: 'answererName',
      width: 120,
    },
    {
      title: '答题人电话',
      dataIndex: 'answererPhone',
      hideInSearch: true,
      width: 130,
    },
    {
      title: '设备编号',
      dataIndex: 'deviceCode',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '机构名称',
      dataIndex: 'hospitalName',
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.map((c: ListType) => ({
          value: c.label,
          label: c.label,
        }));
        return (
          <Select placeholder="请选择" showSearch optionFilterProp="label" options={options} allowClear />
        );
      },
      width: 150,
    },
    {
      title: '答题时间',
      dataIndex: 'answerTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 150,
    },
    {
      title: '答题时间',
      dataIndex: 'answerTime',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => ({
          beginTime: value[0],
          endTime: value[1],
        }),
      },
    },
    {
      title: '评分',
      dataIndex: 'score',
      hideInSearch: true,
      width: 80,
      render: (score: number) => {
        let color = 'default';
        if (score >= 90) color = 'success';
        else if (score >= 70) color = 'warning';
        else if (score >= 0) color = 'error';
        
        return <Tag color={color}>{score}分</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      width: 100,
      valueEnum: {
        0: { text: '未完成', status: 'Default' },
        1: { text: '已完成', status: 'Success' },
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 120,
      render: (_, record) => [
        <a
          key="view"
          onClick={() => handleViewDetail(record)}
        >
          <EyeOutlined /> 查看详情
        </a>,
      ],
    },
  ];

  return (
    <PageContainer header={{ breadcrumb: {} }}>
      <ProTable<QUESTION.AnswerRecordItem>
        headerTitle="答卷记录"
        actionRef={actionRef}
        rowKey="id"
        scroll={{ x: 1300 }}
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={(action, { selectedRows }) => [
          <Button
            key="export"
            icon={<DownloadOutlined />}
            onClick={() => {
              // 获取当前搜索条件
              const searchParams = action?.pageInfo || {};
              handleExportToExcel(searchParams);
            }}
          >
            导出Excel
          </Button>,
        ]}
        request={async (params) => {
          // 使用queryAnswerRecordPage接口
          const response = await queryAnswerRecordPage({
            current: params.current,
            pageSize: params.pageSize,
            ...params,
          });
          
          if (response.code === '0') {
            return {
              data: response.data?.records || [],
              total: response.data?.total || 0,
              success: true,
            };
          }
          
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        columns={columns}
      />

      <Drawer
        title={`答卷详情 - ${currentRecord?.answererName}`}
        width={800}
        open={showDetail}
        onClose={() => {
          setShowDetail(false);
          setCurrentRecord(undefined);
          setAnswerDetail([]);
        }}
        destroyOnClose
      >
        {currentRecord && (
          <Answers
            record={currentRecord}
            answers={answerDetail}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default QuestionResult;
