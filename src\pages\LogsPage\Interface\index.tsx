import { queryReqLogList } from '@/services/api/logs';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  ProDescriptions,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import '@umijs/max';
import { Button, Drawer, Input, message } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { useModel } from '@umijs/max';

const TableList: React.FC = () => {
  const { fetchDicList, dicList } = useModel('dictionary');
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<LOGS.LogListItem>();
  const [selectedRowsState, setSelectedRows] = useState<LOGS.LogListItem[]>([]);

  useEffect(() => {
    fetchDicList('logRequestType');
  }, []);

  const typeObject = {};
  for (let i = 0; i < dicList.length; i++) {
    typeObject[dicList[i].value] = { text: dicList[i].label };
  }
  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */
  const columns: ProColumns<LOGS.LogListItem>[] = [
    {
      title: '请求类型',
      dataIndex: 'requestType',
      width: 100,
      valueEnum: typeObject,
    },
    {
      title: '请求方法',
      dataIndex: 'requestAction',
      width: 300,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '请求参数',
      dataIndex: 'requestPar',
      width: 200,
      key: 'requestPar',
      ellipsis: true,
      hideInSearch: true,
      renderText: (requestPar) => {
        const t = typeof (requestPar);
        if (t === 'string' || t === 'number' || t === 'boolean') {  
          return requestPar
        }
        return JSON.stringify(requestPar)
      },
    },
    {
      title: '患者ID',
      dataIndex: 'patientID',
      width: 100,
      key: 'patientID',
      // hideInTable: true
    },
    {
      title: '终端编号',
      dataIndex: 'deviceCode',
      width: 100,
      key: 'deviceCode',
    },
    // {
    //   title: '返回值',
    //   dataIndex: 'returnCode',
    //   hideInSearch: true,
    //   width: 100,
    //   key: 'returnCode'
    // },
    {
      title: '返回内容',
      dataIndex: 'returnPar',
      width: 200,
      ellipsis: true,
      hideInSearch: true,
      key: 'returnPar',
      renderText: (returnPar) => {
        const t = typeof (returnPar);
        if (t === 'string' || t === 'number' || t === 'boolean') {  
          return returnPar
        }
        return JSON.stringify(returnPar)
      },
    },
    {
      title: '耗时（毫秒）',
      dataIndex: 'requestDuration',
      hideInSearch: true,
      width: 80,
      key: 'requestDuration'
    },
    {
      title: '请求时间',
      dataIndex: 'requestTime',
      valueType: 'dateTimeRange',
      width: 150,
      key: 'requestTime',
      hideInTable: true,
      hideInDescriptions: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      key: 'createTime',
      hideInSearch: true,
      renderText: (_ , record)=>{
        return record.requestTime
      }
    },
  ];
  return (
    <PageContainer>
      <ProTable<LOGS.LogListItem, LOGS.PageParams>
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => []}
        request={queryReqLogList}
        columns={columns}
      />
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.requestAction && (
          <ProDescriptions
            column={1}
            title={currentRow?.requestAction}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={[
              {
                title: '请求类型',
                dataIndex: 'requestType',
                valueEnum: typeObject,
              },
              {
                title: '请求方法',
                dataIndex: 'requestAction',
              },
              {
                title: '请求参数',
                dataIndex: 'requestPar',
                key: 'requestPar',
                hideInSearch: true,
                valueType: "code",
                renderText: (requestPar) => {
                  const t = typeof (requestPar);
                  if (t === 'string' || t === 'number' || t === 'boolean') {  
                    return requestPar
                  }
                  return JSON.stringify(requestPar)
                },
              },
              {
                title: '患者ID',
                dataIndex: 'patientID',
                width: 100,
                key: 'patientID',
              },
              {
                title: '终端编号',
                dataIndex: 'deviceCode',
                width: 100,
                key: 'deviceCode',
              },
              {
                title: '返回内容',
                dataIndex: 'returnPar',
                hideInSearch: true,
                key: 'returnPar',
                valueType: "code",
                renderText: (returnPar) => {
                  const t = typeof (returnPar);
                  if (t === 'string' || t === 'number' || t === 'boolean') {  
                    return returnPar
                  }
                  return JSON.stringify(returnPar)
                },
              },
              {
                title: '耗时（毫秒）',
                dataIndex: 'requestDuration',
                key: 'requestDuration'
              },
              {
                title: '请求时间',
                dataIndex: 'requestTime',
                key: 'requestTime',
              }] as ProDescriptionsItemProps<LOGS.LogListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};
export default TableList;
