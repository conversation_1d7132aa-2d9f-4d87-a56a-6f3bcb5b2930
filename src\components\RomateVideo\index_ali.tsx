import React, {useRef, useState, useImperative<PERSON><PERSON>le, forwardRef, useEffect} from 'react'
import { But<PERSON>, Modal, Drawer, Select, Image, message} from 'antd';
import Draggable from 'react-draggable'
import { PhoneFilled } from '@ant-design/icons'
import DingRTC from 'dingrtc'
import { queryGenerateToken, getChannelStatusByChannel, editChannel} from '@/services/api/audio';
import {SrsRtcSignalingAsync, SrsRtcPublisherAsync, SrsRtcPlayerAsync, SrsRtcSignalingParse} from '@/utils/srs.sdk.js'
import { encrypt } from '@/utils'
import '@umijs/max';
import './index.less'

export type tokenType = {
    userID?: string;
    channelID?: string;
    token?: string
    userName?: string;
    status?: number;
};

// 阿里音视频appID
const appID = 'u2rum00l';
const mcuNotSubscribed = true;

/**
 * @zh-CN 请求视频通话token
 * @param fields
 */
const handleQueryGenerateToken = async (fields: tokenType) => {
  try {
    const response = await queryGenerateToken({ ...fields });
    if (response.code === '0') {
      return response.data;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    message.error('获取音视频通话token失败');
    return false;
  }
};

/**
 * @zh-CN 请求频道状态
 * @param fields
 */
const handleGetChannelStatusByChannel = async (fields: tokenType) => {
    try {
      const response = await getChannelStatusByChannel({ ...fields });
      if (response.code === '0') {
        return response.data;
      }
      message.error(response.msg);
      return false;
    } catch (error) {
      message.error('网络异常');
      return false;
    }
};

/**
 * @zh-CN 修改频道状态
 * @param fields
 */
const handleEditChannel = async (fields: tokenType) => {
    try {
      const response = await editChannel({ ...fields });
      if (response.code === '0') {
        return response.data;
      }
      message.error(response.msg);
      return false;
    } catch (error) {
      message.error('网络异常');
      return false;
    }
};



const RomateVideo = forwardRef((props, ref) => {
    const [myUserID, setMyUserID] = useState<string>('')
    const [userID, setUserID] = useState<string>('');
    const [state, setStateCode] = useState<number>(0)  // 0 初始化 1 正在呼叫 2 对话中 3 结束通话
    // 创建客户端实例
    const client = DingRTC.createClient();
    const cameraTrack = useRef(null)
    const micTrack = useRef(null)
    const audioConnectRef = useRef(null)
    const audioNoResRef = useRef(null)
    const audioRejectRef = useRef(null)
    const timeOutRef = useRef(null)
    const timeInterval = useRef(null)
    
    const handleQueryPeerStatus = async () => {
        const res = await handleGetChannelStatusByChannel({
            userID: encrypt(userID),
            channelID: encrypt(userID)
        })
        if(res && res.status === 4) {
            // 判断对方已离开房间 关闭
            close('reject')
        }
    }

    const handleEditStatus = async () => {
        const res = await handleEditChannel({
            status: 3,
            channelID: encrypt(userID)
        })
    }

    // 呼叫方法
    const call = async (props: tokenType) => {
        const supported = DingRTC.checkSystemRequirements();
        if (!supported) return message.error('当前浏览器不支持音视频通话！')
        // 1 先获取token
        const token = await handleQueryGenerateToken({
            userID: encrypt(props.userID),
            channelID: encrypt(props.channelID)
        })
        if(!token) return;
        setMyUserID(props.userID);
        setUserID(props.channelID);  // 呼叫的对象即为当前的房间号
        // 2.加入房间
        const result = await client.join({
            appId: appID,
            token: token,
            uid: props.userID,
            channel: props.channelID,
            userName: props.userName,
        });
        console.log(result)
        // 摄像头轨道 
        cameraTrack.current = await DingRTC.createCameraVideoTrack({
            frameRate: 15,
            dimension: 'VD_1280x720',
        });
        // 麦克风轨道
        micTrack.current = await DingRTC.createMicrophoneAudioTrack();
        audioConnectRef.current.play();
        setStateCode(1)
        // cameraTrack.current.play('#player');
        // micTrack.current.play();
        // client.publish([cameraTrack.current, micTrack.current])
    }

    useEffect(()=>{
        if(state === 1){
            timeOutRef.current = setTimeout(()=>{
                close('overtime')
            }, 60000)
            timeInterval.current = setInterval(()=>{
                handleQueryPeerStatus()
            }, 1000)
        }
        return ()=> {
            clearTimeout(timeOutRef.current)
            clearInterval(timeInterval.current)
        }
    }, [state])
    

    client.on('user-joined', (user) => {
        // 当进来的用户为呼叫的用户时
        if(user.userId === userID){
            timeOutRef.current && clearTimeout(timeOutRef.current)
            timeInterval.current && clearInterval(timeInterval.current)
            audioConnectRef.current.pause();
            setStateCode(2)
            // 显示画面推流
            cameraTrack.current.play('#player');
            micTrack.current.play();
            client.publish([cameraTrack.current, micTrack.current])
        }
    })

    client.on('user-published', (user, mediaType, auxiliary)=> {
        if(mediaType === 'video'){
            user.userId === userID && client.subscribe(user.userId, mediaType, auxiliary).then((track) =>{
                setStateCode(2)
                track.play(`#uid${user.userId}`)
            })
        } else if (mcuNotSubscribed){
            user.userId === userID && client.subscribe('mcu', 'audio').then((track) => {
                setStateCode(2)
                track.play(`#uid${user.userId}`)
            })
        }
    })

    client.on('user-left', (user) => {
        if(user.userId === userID){
            console.log('监听离开事件', user.userId)
            message.info('对方已挂断！')
            client.unpublish([cameraTrack.current, micTrack.current])
            cameraTrack.current.close()
            micTrack.current.close()
            client.leave()
            setStateCode(3)
        }
    })

    const close = (type?: 'active' | 'overtime' | 'reject' ) => {
        client.unpublish([cameraTrack.current, micTrack.current])
        cameraTrack.current.close()
        micTrack.current.close()
        // 修改频道状态
        handleEditStatus();
        if(state === 2) {
            // client.unsubscribe(userID, 'video')
            client.unsubscribe('mcu', 'audio')
            client.leave()
        }else if(state === 1){
            timeOutRef.current && clearTimeout(timeOutRef.current)
            timeInterval.current && clearInterval(timeInterval.current)
            client.leave()
            audioConnectRef.current.pause();
            type === 'overtime' && audioNoResRef.current.play();
            type === 'reject' && audioRejectRef.current.play();
        }
        setStateCode(3)
    }

    useImperativeHandle(ref, () => ({ call, close }));
    
    return (
        <>
            <div className='video-content'>
                <div className='wrapper'></div>
            </div>
            { (state === 1 || state === 2) && <Draggable handle=".handle" bounds=".wrapper">
                <div className="media-wrapper">
                    <div className="handle">视频通话</div>
                    <div className='content'>
                        <div className='my' id="player"></div>
                        <div className="romate-user" id={`uid${userID}`}>
                            { state === 1 && <div className="loading-text">正在呼叫{userID}中，请等待...</div> }
                        </div>
                        <div className='close-btn' onClick={() => close('active')}>
                            <PhoneFilled />
                        </div>
                    </div>
                </div>
            </Draggable>}
            <audio ref={audioConnectRef} src='audio/avchat_connecting.mp3' loop hidden>
                Your browser does not support the audio element.
            </audio>
            <audio ref={audioNoResRef} src="audio/avchat_no_response.mp3" hidden>
                Your browser does not support the audio element.
            </audio>
            <audio ref={audioRejectRef} src="audio/avchat_peer_reject.mp3" hidden>
                Your browser does not support the audio element.
            </audio>
        </>
    );
});

export default RomateVideo;