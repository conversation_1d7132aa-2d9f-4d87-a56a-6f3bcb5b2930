declare namespace USER {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 用户详情
    type UserListItem = {
        hospitalName?: string,  // 医院名称
        realName?: string,   // 姓名
        phone?: string,    // 手机号
        roleID?: number,   // 角色ID
        roleName?: string,  // 角色姓名
        state?: number,    // 状态
        userName?: string,  // 用户名
        saID?: string,  // saID
        userID?: number, // 用户ID
        key?: number     // key
        lastLoginTime?: string,   // 上次登录时间
        digID?: string | any[],
        pwd?: string,
        newPwd?: string,
        codeReg?: string // 注册码
    };
    // 用户列表
    type UserList = {
        /** 列表的内容 **/
        data?: UserListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}