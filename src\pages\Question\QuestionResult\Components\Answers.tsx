import React from 'react';
import { Descriptions, Divider, List, Card, Tag, Space, Typography } from 'antd';
import { 
  UserOutlined, 
  PhoneOutlined, 
  ClockCircleOutlined, 
  StarOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import moment from 'moment';

const { Text, Title } = Typography;

export interface AnswersProps {
  record: QUESTION.AnswerRecordItem;
  answers: QUESTION.AnswerResultItem[];
}

const Answers: React.FC<AnswersProps> = ({ record, answers = [] }) => {
  // 获取问题类型图标
  const getQuestionTypeIcon = (type: number) => {
    switch (type) {
      case 1:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 2:
        return <CheckCircleOutlined style={{ color: '#1890ff' }} />;
      case 3:
        return <FileTextOutlined style={{ color: '#722ed1' }} />;
      default:
        return <FileTextOutlined />;
    }
  };

  // 获取问题类型名称
  const getQuestionTypeName = (type: number) => {
    switch (type) {
      case 1:
        return '单选题';
      case 2:
        return '多选题';
      case 3:
        return '文本题';
      default:
        return '未知类型';
    }
  };

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 90) return '#52c41a';
    if (score >= 70) return '#faad14';
    return '#ff4d4f';
  };

  // 格式化答案显示
  const formatAnswer = (answer: string, questionType: number, options?: string[]) => {
    if (!answer) return '未回答';
    
    if (questionType === 1 || questionType === 2) {
      // 单选或多选题，显示选项内容
      try {
        const selectedIndexes = answer.split(',').map(index => parseInt(index.trim()));
        const selectedOptions = selectedIndexes
          .map(index => options?.[index])
          .filter(Boolean);
        
        return selectedOptions.length > 0 ? selectedOptions.join(', ') : answer;
      } catch {
        return answer;
      }
    }
    
    // 文本题直接显示答案
    return answer;
  };

  return (
    <div className="answers-container">
      {/* 答卷基本信息 */}
      <Card title="答卷信息" style={{ marginBottom: 16 }}>
        <Descriptions column={2} size="small">
          <Descriptions.Item 
            label={
              <Space>
                <UserOutlined />
                答题人
              </Space>
            }
          >
            {record.answererName || '未知'}
          </Descriptions.Item>
          <Descriptions.Item 
            label={
              <Space>
                <PhoneOutlined />
                联系电话
              </Space>
            }
          >
            {record.answererPhone || '未提供'}
          </Descriptions.Item>
          <Descriptions.Item 
            label={
              <Space>
                <ClockCircleOutlined />
                答题时间
              </Space>
            }
          >
            {record.answerTime ? moment(record.answerTime).format('YYYY-MM-DD HH:mm:ss') : '未知'}
          </Descriptions.Item>
          <Descriptions.Item 
            label={
              <Space>
                <StarOutlined />
                评分
              </Space>
            }
          >
            <Tag color={getScoreColor(record.score || 0)}>
              {record.score || 0}分
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="问卷名称" span={2}>
            {record.questionnaireName || '未知问卷'}
          </Descriptions.Item>
          <Descriptions.Item label="设备编号" span={2}>
            {record.deviceCode || '未知设备'}
          </Descriptions.Item>
          <Descriptions.Item label="医院名称" span={2}>
            {record.hospitalName || '未知医院'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Divider />

      {/* 答题详情 */}
      <Card title={`答题详情 (共${answers.length}题)`}>
        {answers.length > 0 ? (
          <List
            dataSource={answers}
            renderItem={(item, index) => (
              <List.Item
                style={{
                  padding: '16px 0',
                  borderBottom: index === answers.length - 1 ? 'none' : '1px solid #f0f0f0',
                }}
              >
                <div style={{ width: '100%' }}>
                  <div style={{ marginBottom: 12 }}>
                    <Space>
                      <Tag color="blue">Q{index + 1}</Tag>
                      {getQuestionTypeIcon(item.questionType || 3)}
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {getQuestionTypeName(item.questionType || 3)}
                      </Text>
                    </Space>
                  </div>
                  
                  <div style={{ marginBottom: 8 }}>
                    <Text strong style={{ fontSize: 14 }}>
                      {item.questionTitle || '未知问题'}
                    </Text>
                  </div>
                  
                  <div style={{ 
                    padding: '12px 16px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: 6,
                    borderLeft: '3px solid #1890ff',
                  }}>
                    <Text style={{ fontSize: 14 }}>
                      {formatAnswer(
                        item.answer || '', 
                        item.questionType || 3, 
                        item.options
                      )}
                    </Text>
                  </div>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px',
            color: '#999',
          }}>
            <FileTextOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>暂无答题记录</div>
          </div>
        )}
      </Card>

      {/* 统计信息 */}
      {answers.length > 0 && (
        <Card title="答题统计" style={{ marginTop: 16 }}>
          <Descriptions column={3} size="small">
            <Descriptions.Item label="总题数">
              {answers.length}
            </Descriptions.Item>
            <Descriptions.Item label="已答题数">
              {answers.filter(item => item.answer && item.answer.trim()).length}
            </Descriptions.Item>
            <Descriptions.Item label="未答题数">
              {answers.filter(item => !item.answer || !item.answer.trim()).length}
            </Descriptions.Item>
            <Descriptions.Item label="单选题">
              {answers.filter(item => item.questionType === 1).length}
            </Descriptions.Item>
            <Descriptions.Item label="多选题">
              {answers.filter(item => item.questionType === 2).length}
            </Descriptions.Item>
            <Descriptions.Item label="文本题">
              {answers.filter(item => item.questionType === 3).length}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      )}
    </div>
  );
};

export default Answers;
