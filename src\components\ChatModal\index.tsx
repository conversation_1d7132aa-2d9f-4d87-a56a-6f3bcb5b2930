import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Modal, message } from 'antd';
import { useModel } from '@umijs/max';
import ChatWindow, { DisplayChatMessage } from '@/components/ChatWindow';
import type { 
  ChatConversation, 
  ChatMessage,
  ApiResponse
} from '@/services/api/chat';
import { 
  getChatHistory, 
  createOrGetConversation,
  sendTextMessage,
  sendImageMessage,
  markMessagesAsRead
} from '@/services/api/chat';
import './index.less';

export interface ChatModalProps {
  visible: boolean;
  onCancel: () => void;
  receiverId: string;
  receiverName: string;
  receiverAvatar?: string;
  patientInfo?: {
    patientID?: string;
    patientName?: string;
    invoice?: string;
    hospitalName?: string;
  };
}

const ChatModal: React.FC<ChatModalProps> = ({
  visible,
  onCancel,
  receiverId,
  receiverName,
  receiverAvatar,
  patientInfo,
}) => {
  const { initialState } = useModel('@@initialState');
  const [conversation, setConversation] = useState<ChatConversation | null>(null);
  const [messages, setMessages] = useState<DisplayChatMessage[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取当前用户ID（设备号）
  const currentUserId = initialState?.currentUser?.userName || 'device001';

  // 轮询配置
  const [pollingInterval] = useState(1000); // 1秒轮询
  const [isPollingEnabled, setIsPollingEnabled] = useState(true);
  const messagePollingRef = useRef<NodeJS.Timeout | null>(null);

  // 加载聊天记录
  const loadChatHistory = useCallback(async (conversationId: string, showLoading = true) => {
    if (showLoading) {
      setLoading(true);
    }
    try {
      const response = await getChatHistory({ conversationId });
      
      if (response.code === '0') {
        // 转换消息格式以适配UI显示
        const displayMessages: DisplayChatMessage[] = (response.data || []).map(msg => ({
          ...msg,
          sender: msg.senderId === currentUserId ? 'self' : 'other'
        }));
        setMessages(displayMessages);
      } else {
        // 轮询时不显示错误提示
        if (showLoading) {
          message.error(response.msg || '获取聊天记录失败');
        }
      }
    } catch (error) {
      // 轮询时不显示错误提示
      if (showLoading) {
        message.error('获取聊天记录失败');
      }
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  }, [currentUserId]);

  // 开始轮询消息
  const startMessagePolling = useCallback(() => {
    if (!isPollingEnabled || !conversation) return;
    
    // 清除现有轮询
    if (messagePollingRef.current) {
      clearInterval(messagePollingRef.current);
    }
    
    // 设置新的轮询
    messagePollingRef.current = setInterval(() => {
      loadChatHistory(conversation.conversationId, false); // 轮询时不显示loading
    }, pollingInterval);
  }, [isPollingEnabled, conversation, pollingInterval, loadChatHistory]);

  // 停止轮询消息
  const stopMessagePolling = useCallback(() => {
    if (messagePollingRef.current) {
      clearInterval(messagePollingRef.current);
      messagePollingRef.current = null;
    }
  }, []);

  // 创建或获取会话
  const initializeConversation = useCallback(async () => {
    if (!receiverId) return;

    try {
      setLoading(true);
      
      // 创建或获取会话
      const response = await createOrGetConversation({
        user1Id: currentUserId,
        user2Id: receiverId,
      });

      if (response.code === '0') {
        setConversation(response.data);
        
        // 加载聊天记录
        await loadChatHistory(response.data.conversationId, false);
        
        // 标记消息为已读
        try {
          await markMessagesAsRead({
            conversationId: response.data.conversationId,
            userId: currentUserId
          });
        } catch (error) {
          console.error('标记已读失败:', error);
        }
      } else {
        message.error(response.msg || '创建聊天会话失败');
      }
    } catch (error) {
      message.error('创建聊天会话失败');
    } finally {
      setLoading(false);
    }
  }, [receiverId, currentUserId, loadChatHistory]);

  // 发送消息
  const handleSendMessage = async (messageData: {
    content: string;
    messageType: number;
    imageFile?: File;
  }) => {
    if (!conversation) return false;
    
    try {
      let response: ApiResponse<ChatMessage> | undefined;
      
      // 根据消息类型调用不同的API
      if (messageData.messageType === 1) {
        // 发送文字消息
        response = await sendTextMessage({
          senderId: currentUserId,
          receiverId,
          content: messageData.content,
        });
      } else if (messageData.messageType === 2 && messageData.imageFile) {
        // 先添加一个临时的预览消息到界面
        const tempMessage: DisplayChatMessage = {
          messageId: `temp_${Date.now()}`,
          conversationId: conversation.conversationId,
          senderId: currentUserId,
          receiverId,
          messageType: 2,
          content: '[图片上传中...]',
          imageUrl: messageData.content, // 使用预览URL
          sendStatus: 1, // 发送中
          readStatus: 0,
          createTime: new Date().toISOString(),
          sender: 'self'
        };
        
        // 立即显示预览消息
        setMessages(prev => [...prev, tempMessage]);
        
        // 发送图片消息
        response = await sendImageMessage({
          senderId: currentUserId,
          receiverId,
          imageFile: messageData.imageFile,
        });
        
        // 移除临时消息
        setMessages(prev => prev.filter(msg => msg.messageId !== tempMessage.messageId));
      }
      
      if (response && response.code === '0') {
        // 将新消息添加到消息列表
        const newDisplayMessage: DisplayChatMessage = {
          ...response.data,
          sender: 'self'
        };
        setMessages(prev => [...prev, newDisplayMessage]);
        
        // 清理预览URL（如果是图片消息）
        if (messageData.messageType === 2 && messageData.content.startsWith('blob:')) {
          URL.revokeObjectURL(messageData.content);
        }
        
        return true;
      } else {
        message.error(response?.msg || '发送消息失败');
        return false;
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败');
      return false;
    }
  };

  // Modal打开时初始化会话
  useEffect(() => {
    if (visible && receiverId) {
      initializeConversation();
      setIsPollingEnabled(true);
    } else {
      // Modal关闭时清理状态
      setConversation(null);
      setMessages([]);
      setIsPollingEnabled(false);
      stopMessagePolling();
    }
  }, [visible, receiverId, initializeConversation, stopMessagePolling]);

  // 监听会话变化，开始轮询
  useEffect(() => {
    if (conversation && isPollingEnabled && visible) {
      startMessagePolling();
    } else {
      stopMessagePolling();
    }
    
    return () => {
      stopMessagePolling();
    };
  }, [conversation, isPollingEnabled, visible, startMessagePolling, stopMessagePolling]);

  // 页面可见性变化时控制轮询
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopMessagePolling();
      } else if (visible && conversation && isPollingEnabled) {
        startMessagePolling();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [visible, conversation, isPollingEnabled, startMessagePolling, stopMessagePolling]);

  const modalTitle = (
    <div className="chat-modal-title">
      <span>与 {receiverName} 的聊天</span>
      {patientInfo && (
        <span className="patient-info">
          {patientInfo.hospitalName && `${patientInfo.hospitalName} - `}
          {patientInfo.patientID && `病人ID: ${patientInfo.patientID}`}
        </span>
      )}
    </div>
  );

  return (
    <Modal
      title={modalTitle}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      height={600}
      className="chat-modal"
      destroyOnClose
    >
      <div className="chat-modal-content">
        <ChatWindow
          receiverId={receiverId}
          receiverName={receiverName}
          receiverAvatar={receiverAvatar}
          onSendMessage={handleSendMessage}
          messages={messages}
          loading={loading}
        />
      </div>
    </Modal>
  );
};

export default ChatModal;
