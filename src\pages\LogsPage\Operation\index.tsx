import { queryOptList } from '@/services/api/logs';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  ProDescriptions,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import '@umijs/max';
import { Button, Drawer, Input, message } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { useModel } from '@umijs/max';

const TableList: React.FC = () => {
  const { fetchDicList, dicList } = useModel('dictionary');
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<LOGS.LogListItem>();
  const [selectedRowsState, setSelectedRows] = useState<LOGS.LogListItem[]>([]);

  useEffect(() => {
    fetchDicList('operatingType');
  }, []);

  const typeObject = {};
  for (let i = 0; i < dicList.length; i++) {
    typeObject[dicList[i].value] = { text: dicList[i].label };
  }
  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */
  const columns: ProColumns<LOGS.LogListItem>[] = [
    {
      title: '操作名称',
      dataIndex: 'operatingName',
      key: 'operatingName'
    },
    {
      title: '操作类型',
      dataIndex: 'operatingType',
      key: 'operatingType',
      valueEnum: typeObject,
      hideInTable: true,
    },    
    {
      title: '操作类型',
      dataIndex: 'operatingTypeName',
      key: 'operatingTypeName',
      hideInSearch: true,
    },  
    {
      title: '操作人',
      dataIndex: 'createUserName',
      key: 'createUserName',
      hideInSearch: true
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true
    }
  ];
  return (
    <PageContainer>
      <ProTable<LOGS.LogListItem, LOGS.PageParams>
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => []}
        request={queryOptList}
        columns={columns}
      />
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.requestAction && (
          <ProDescriptions
            column={1}
            title={currentRow?.requestAction}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<LOGS.LogListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};
export default TableList;
