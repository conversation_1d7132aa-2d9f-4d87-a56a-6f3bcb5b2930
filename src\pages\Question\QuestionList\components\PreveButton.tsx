import React from 'react';
import { Space, Popconfirm } from 'antd';

export interface PreveButtonProps {
  record: QUESTION.QuestionnaireListItem;
  index: number;
  handleEdit?: (record: QUESTION.QuestionnaireListItem) => void;
  handlePublish?: (record: QUESTION.QuestionnaireListItem) => void;
  handleRemove?: (record: QUESTION.QuestionnaireListItem) => void;
  handlePreview?: (record: QUESTION.QuestionnaireListItem) => void;
  handleFill?: (index: number, record: QUESTION.QuestionnaireListItem) => void;
  handleUnPublish?: (record: QUESTION.QuestionnaireListItem) => void;
}

// 表格列表操作按钮
const PreveButton: React.FC<PreveButtonProps> = ({
  record,
  index,
  handleEdit,
  handlePublish,
  handleRemove,
  handlePreview,
  handleFill,
  handleUnPublish,
}) => {
  return (
    <Space size="middle">
      {record.status === 0 && (
        <>
          <a onClick={() => handleEdit?.(record)}>编辑</a>
          <a onClick={() => handlePublish?.(record)}>发布</a>
          <a onClick={() => handlePreview?.(record)}>预览</a>
          <Popconfirm
            title="确定要删除这个问卷吗？"
            onConfirm={() => handleRemove?.(record)}
            okText="确定"
            cancelText="取消"
          >
            <a style={{ color: 'red' }}>删除</a>
          </Popconfirm>
        </>
      )}
      
      {record.status === 1 && (
        <>
          <a onClick={() => handlePreview?.(record)}>预览</a>
          <a onClick={() => handleUnPublish?.(record)}>暂停</a>
          <a onClick={() => handleFill?.(index, record)}>填写问卷</a>
        </>
      )}
      
      {record.status === 2 && (
        <>
          <a onClick={() => handleEdit?.(record)}>编辑</a>
          <a onClick={() => handlePublish?.(record)}>重新发布</a>
          <a onClick={() => handlePreview?.(record)}>预览</a>
          <Popconfirm
            title="确定要删除这个问卷吗？"
            onConfirm={() => handleRemove?.(record)}
            okText="确定"
            cancelText="取消"
          >
            <a style={{ color: 'red' }}>删除</a>
          </Popconfirm>
        </>
      )}
    </Space>
  );
};

export default PreveButton;
