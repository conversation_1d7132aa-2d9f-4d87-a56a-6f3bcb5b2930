import { Modal, Table } from 'antd';

const PatientForm = props => {
  const { 
    modalVisible,
    columns,
    data,
    onCancel
  } = props;

  const hiddenList = ['registerTime', 'checkTime', 'callTime']

  const newColumns = columns.map((item) => ({
    ...item,
    hidden: hiddenList.includes(item.key as string), 
  }));

  console.log(newColumns)

  return (
    <Modal
      width={1000}
      destroyOnClose
      title="搜索结果"
      open={modalVisible}
      footer={null}
      onCancel={onCancel}
    >
      <Table columns={newColumns} dataSource={data} scroll={{y: 300 }} pagination={false} />
    </Modal>
  );
};

export default PatientForm;