declare namespace CLINIC {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 诊室详情
    type ClinicListItem = {        
        roomID?: number,   // 诊室ID
        roomCode?: string, // 诊室代码 
        roomName?: string, // 诊室名称
        roomIP?: string,   // 诊室IP
        sort?: number,      // 排序
        remark?: string,   // 备注
        lastModifyUser?: number, // 上次修改用户
        createTime?: string,  // 创建时间
        lastModifyTime?: string, // 上次修改时间
        createUser?: string,  // 创建用户
        state?: number,  // 状态
        saID?: string, // 医院ID
        hospitalName?: string, // 机构名称
        key?: number,  // key 同roomID
        deptCode?: string  // 默认关联科室 
        digID?: string // 关联诊区ID,
        digAreaName?: string // 关联诊区名称
    };
    // 诊室列表
    type ClinicList = {
        /** 列表的内容 **/
        data?: ClinicListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}