# 满意度调查模块重构说明

## 重构概述

将满意度调查模块从JSX重构为TSX，删除dva组件的使用，改用useModel，并统一使用services/api/question.js中的服务接口。

## 重构内容

### 1. 文件格式转换
- ✅ 所有`.jsx`文件转换为`.tsx`
- ✅ 添加完整的TypeScript类型定义
- ✅ 使用严格的类型检查

### 2. 状态管理重构
- ❌ 删除dva组件的使用 (`connect`)
- ✅ 改用`useModel`进行状态管理
- ✅ 使用React Hooks进行组件状态管理

### 3. 服务接口统一
- ✅ 统一使用`@/services/api/question`中的接口
- ✅ 规范化API调用方式
- ✅ 统一错误处理机制

## 目录结构

```
src/pages/Question/
├── QuestionList/                # 问卷配置
│   ├── index.tsx               # 主页面 (JSX → TSX)
│   └── components/
│       └── PreveButton.tsx     # 操作按钮组件 (JSX → TSX)
├── QuestionDetail/             # 问卷详情
│   ├── index.tsx               # 主页面 (JSX → TSX)
│   ├── components/             # 子组件 (保持原有)
│   └── style.less              # 样式文件
├── QuestionResult/             # 查看答卷
│   ├── index.tsx               # 主页面 (JSX → TSX)
│   ├── Components/             # 子组件 (保持原有)
│   └── index.less              # 样式文件
├── FavorableRate/              # 好评率统计
│   └── index.tsx               # 主页面 (JSX → TSX)
├── data.d.ts                   # TypeScript类型定义 (新增)
└── README.md                   # 说明文档 (新增)
```

## 主要功能模块

### 1. 问卷配置 (`QuestionList`)

#### 功能特性
- **问卷列表管理**：查询、新增、编辑、删除问卷
- **状态管理**：草稿、已发布、已暂停状态切换
- **预览功能**：问卷预览和填写
- **机构筛选**：按机构过滤问卷数据

#### 技术实现
```typescript
// 使用useModel替代dva
const { initialState } = useModel('@@initialState');
const { hosList, fetchHosList } = useModel('hospital');

// 统一的API调用
import { 
  queryQuestionnaireList, 
  publishQuestionnaire, 
  deleteQuestionnaire 
} from '@/services/api/question';

// 类型安全的状态管理
const [selectedRows, setSelectedRows] = useState<QUESTION.QuestionnaireListItem[]>([]);
```

### 2. 问卷详情 (`QuestionDetail`)

#### 功能特性
- **多模式支持**：新增、编辑、查看、预览模式
- **问题管理**：添加、编辑、删除问题
- **表单验证**：完整的表单验证机制
- **实时预览**：问卷预览功能

#### 技术实现
```typescript
// URL参数处理
const [searchParams] = useSearchParams();
const id = searchParams.get('id');
const mode = searchParams.get('mode') || 'view';

// 表单管理
const [form] = Form.useForm();

// 问题列表状态
const [questions, setQuestions] = useState<QUESTION.QuestionListItem[]>([]);
```

### 3. 查看答卷 (`QuestionResult`)

#### 功能特性
- **答卷记录查询**：支持多条件筛选
- **答卷详情查看**：Drawer形式展示详细答案
- **数据导出**：Excel格式导出功能
- **评分统计**：答卷评分可视化

#### 技术实现
```typescript
// Drawer状态管理
const [showDetail, setShowDetail] = useState(false);
const [currentRecord, setCurrentRecord] = useState<QUESTION.AnswerRecordItem>();

// 答案详情
const [answerDetail, setAnswerDetail] = useState<QUESTION.AnswerResultItem[]>([]);

// 导出功能
const handleExportToExcel = async (fields: any) => {
  const paramsStr = parseParam(fields);
  // Excel导出逻辑
};
```

### 4. 好评率统计 (`FavorableRate`)

#### 功能特性
- **统计数据展示**：总评数、好评数、好评率
- **可视化图表**：进度条、统计卡片
- **时间范围筛选**：一周、三周、一月、一季度
- **机构对比**：不同机构好评率对比

#### 技术实现
```typescript
// 汇总数据状态
const [summaryData, setSummaryData] = useState<{
  totalCount: number;
  goodCount: number;
  goodRate: number;
}>({
  totalCount: 0,
  goodCount: 0,
  goodRate: 0,
});

// 可视化组件
<Progress
  percent={rate}
  size="small"
  status={status}
  format={(percent) => `${percent}%`}
/>
```

## TypeScript类型定义

### 核心数据类型
```typescript
declare namespace QUESTION {
  // 问卷列表项
  type QuestionnaireListItem = {
    id?: number;
    title?: string;
    description?: string;
    status?: number; // 0-草稿 1-发布 2-暂停
    createTime?: string;
    questionCount?: number;
    answerCount?: number;
    saID?: string;
    hospitalName?: string;
  };

  // 问题列表项
  type QuestionListItem = {
    id?: number;
    questionnaireId?: number;
    title?: string;
    type?: number; // 1-单选 2-多选 3-文本
    options?: string[];
    required?: boolean;
    sort?: number;
  };

  // 答卷记录项
  type AnswerRecordItem = {
    id?: number;
    questionnaireId?: number;
    questionnaireName?: string;
    answererName?: string;
    answererPhone?: string;
    deviceCode?: string;
    hospitalName?: string;
    answerTime?: string;
    score?: number;
    status?: number;
  };
}
```

## API接口使用

### 统一的服务接口
```typescript
// 问卷管理
import { 
  queryQuestionnaireList,    // 获取问卷列表
  addQuestionnaire,          // 新增问卷
  editQuestionnaire,         // 修改问卷
  publishQuestionnaire,      // 发布/暂停问卷
  deleteQuestionnaire,       // 删除问卷
  queryListById,             // 获取问卷详情
} from '@/services/api/question';

// 答卷管理
import {
  queryAnswerRecord,         // 查询答卷记录
  queryAnswerRecordPage,     // 分页查询答卷记录
  getAnswerDisplayByRecord,  // 获取答卷详情
} from '@/services/api/question';

// 统计分析
import {
  queryGoodValue,            // 获取好评率统计
} from '@/services/api/question';
```

### 错误处理机制
```typescript
const handleOperation = async (fields: any) => {
  const hide = message.loading('正在处理...');
  try {
    const response = await apiFunction({ ...fields });
    if (response.code === '0') {
      hide();
      message.success('操作成功');
      return true;
    }
    message.error(response.msg || '操作失败');
    return false;
  } catch (error) {
    hide();
    message.error('操作失败请重试！');
    return false;
  }
};
```

## 重构对比

### 状态管理对比
| 重构前 | 重构后 |
|--------|--------|
| `connect(mapStateToProps)` | `useModel('@@initialState')` |
| `dispatch({ type: 'xxx' })` | `直接调用API函数` |
| `props.xxx` | `const { xxx } = useModel('xxx')` |

### 类型安全对比
| 重构前 | 重构后 |
|--------|--------|
| `const [data, setData] = useState()` | `const [data, setData] = useState<Type[]>([])` |
| `function handleClick(record)` | `function handleClick(record: Type)` |
| `props` | `interface Props { ... }` |

### 组件导入对比
| 重构前 | 重构后 |
|--------|--------|
| `import { connect } from 'umi'` | `import { useModel } from '@umijs/max'` |
| `import { PageHeaderWrapper }` | `import { PageContainer }` |
| `import { xxx } from '@/services/question'` | `import { xxx } from '@/services/api/question'` |

## 性能优化

### 1. 组件优化
- 使用React.memo优化组件渲染
- 合理使用useCallback和useMemo
- 避免不必要的重新渲染

### 2. 数据加载优化
- 分页加载大量数据
- 搜索防抖处理
- 缓存常用数据

### 3. 用户体验优化
- Loading状态提示
- 错误边界处理
- 友好的错误提示

## 注意事项

### 1. 兼容性
- 保持与现有API接口的兼容性
- 确保数据格式的一致性
- 维护现有的业务逻辑

### 2. 类型安全
- 严格的TypeScript类型检查
- 完整的接口类型定义
- 运行时类型验证

### 3. 代码质量
- 统一的代码风格
- 完善的错误处理
- 清晰的注释说明

## 后续优化建议

1. **单元测试**：添加完整的单元测试覆盖
2. **国际化**：支持多语言切换
3. **主题定制**：支持自定义主题样式
4. **权限控制**：细粒度的权限管理
5. **数据可视化**：更丰富的图表展示
6. **移动端适配**：响应式设计优化

## 相关文件

- `src/pages/Question/` - 满意度调查模块
- `src/services/api/question.js` - API接口定义
- `config/routes.ts` - 路由配置
- `src/pages/Question/README.md` - 本说明文档
