declare namespace HOSPITAL {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 医院详情
    type HosListItem = {
        saID?: string, // 机构ID
        hospitalName?:string,  // 机构名称
        systemKey?: string,   // 机构代码
        systemName?: string,  // 系统名称
        contactName?: string,  // 医院联系人
        contactTel ?: string,  // 医院联系方式
        hisUrl?:string,     // HIS访问地址
        payUrl?: string,   // 支付地址
        createUser?:number,     // 创建人
        createTime?:string,   // 创建时间
        lastModifyUser?: number,   // 上次修改人
        lastModifyTime?: string, // 上次修改时间
        iconUrl?: string,  // logoUrl
        key?: string       // key 同 机构ID
    };
    // 医院列表
    type HosList = {
        /** 列表的内容 **/
        data?: HosListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}