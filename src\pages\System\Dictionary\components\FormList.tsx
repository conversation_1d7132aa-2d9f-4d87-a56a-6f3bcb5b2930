import { ProFormText } from '@ant-design/pro-form';

const FormList = () => {
  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '类型为必填项',
          },
        ]}
        name="type"
        label="类型"
        placeholder="请输入类型"
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: '类型名称为必填项',
          },
        ]}
        name="typeName"
        label="类型名称"
        placeholder="请输入类型名称"
      />
      <ProFormText 
        rules={[
          {
            required: true,
            message: '内容为必填项',
          },
        ]} 
        name="content" 
        label="内容"
        placeholder="请输入内容"
      />
      <ProFormText 
        rules={[
          {
            required: true,
            message: '参数值为必填项',
          },
        ]} 
        name="code" 
        label="参数值"
        placeholder="请输入参数值"
      />
      <ProFormText name="sort" label="排序" placeholder="请输入排序" />
      <ProFormText name="remark" label="备注" placeholder="请输入备注" />
    </>
  );
};

export default FormList;
