import React from "react";
import { Space } from "antd";

// 表格列表操作按钮
const PreveButton = ({
    record,
    index,
    handleEdit,
    handlePublish,
    handleRemove,
    handlePreview,
    handleFill,
    handleUnPublish,
}) => {
    return (
        <Space size="middle">
            {
                record.state === 1 ? <>
                    <a onClick={() => handleEdit(record)}>编辑问卷</a>
                    <a onClick={() => handlePublish(record)}>发布</a>
                    {/* <a onClick={() => handleUpdate(record)}>修改类型</a> */}
                    {/* <a onClick={() => handlePreview()}>查看数据</a> */}
                </> :
                    record.state === 2 ? 
                    <>
                        {/* <a onClick={() => handleFill(index,record)}>填写问卷</a> */}
                        <a onClick={() => handleUnPublish(record)}>取消发布</a>
                        {/* <a onClick={() => handlePreview()}>查看数据</a> */}
                    </> :
                    // record.state === 3 ?
                    //     <>
                    //         <a onClick={() => handlePreview()}>查看数据</a>
                    //     </> : 
                    <></>
            }
        </Space>
    )
}

export default PreveButton;