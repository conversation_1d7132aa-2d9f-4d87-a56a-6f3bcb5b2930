import {
  PageContainer,
} from '@ant-design/pro-components';
import { Row, Col, Timeline} from 'antd';
import React, { useEffect, useState, useRef} from 'react';
import DeptNumChart from './components/DeptNumChart';
import DeptTimeChart from './components/DeptTimeChart';
import DeptNumTable from './components/DeptNumTable';
import PatientTypePie from './components/PatientTypePie';
import RadarCharts from './components/RadarCharts';
import FlipNum from './components/FlipNum';
import { usePrevious, useFullscreen, useRequest} from 'ahooks';
import { getHospitalVisitInfo } from '@/services/api/analysis';
import style from './index.less';

// 请求首页数据
const queryHospitalVisitInfo = async () => {
    try {
      const response = await getHospitalVisitInfo({});

      if (response.code === '0') {
        return response.data;
      }
      return false;
    } catch (error) {
      return false;
    }
};

const Analysis: React.FC = () => {
    // const prevNumber = usePrevious(number);
    const ref = useRef(null);
    const [isFullscreen, { enterFullscreen, exitFullscreen, toggleFullscreen }] = useFullscreen(ref);
    const intervalRef = useRef(null);

    const { data } = useRequest(queryHospitalVisitInfo, {
        refreshDeps: [],
        pollingInterval: 30000,
        pollingWhenHidden: true,
    });
    
    useEffect(()=>{
        // intervalRef.current = setInterval(()=>{
        //     const str = generateRandomString(6)
        //     console.log(str)
        //     setNumber(str)
        // }, 5000)

        // return () => {
        //     clearInterval(intervalRef.current)
        // }
    }, [])
    
  return (
    <PageContainer header={{breadcrumb:{}, title: false}} >
        <div className={style.wrapper} ref={ref}>
            <div className={style.title} onClick={enterFullscreen}><div>京威盛智慧医院</div><div>排队叫号系统数据大屏</div></div>
            <div className={style.content}>
                <Row gutter={[35, 60]}>
                    <Col span={8}>
                        <div className={style.rightContent}>
                            <div className={style.subTitle}>总门诊人次</div>
                            <FlipNum/>
                            <div className={style.dataTitle}>今日科室就诊量TOP10</div>
                            <DeptNumChart/>
                        </div>
                    </Col>
                    <Col span={8}>
                        <div className={style.midTopContent}>
                            <div className={style.subTitle}>重点科室平均就诊时长</div>
                            <DeptTimeChart />
                        </div>
                        <div className={style.midBottomContent}>
                            <div className={style.subTitle}>各科室就诊人次top6</div>
                            <DeptNumTable />
                        </div>
                    </Col>
                    <Col span={8}>
                        <div className={style.leftTopContent}>
                            <div className={style.dataContent}>
                                <div className={style.item}>
                                    <div className={style.text}>全院平均就诊时长：{data?.avgVisitsTime}分钟</div>
                                    <div className={`${style.bar} ${style.blue}`} />
                                </div>
                            </div>
                            <div className={style.dataContent}>
                                <div className={style.item}>
                                    <div className={style.text}>今日开诊科室：{data?.deptNum}</div>
                                    <div className={`${style.bar} ${style.red}`} />
                                </div>
                                <div className={style.item}>
                                    <div className={style.text}>今日开诊医生：{data?.doctorNum}</div>
                                    <div className={`${style.bar} ${style.yellow}`} />
                                </div>
                            </div>
                        </div>
                        <div className={style.leftBottomContent}>
                            <div className={style.subTitle}>就诊人群分析</div>
                            <div className={style.chartContent}>
                                <PatientTypePie />
                                <RadarCharts />
                            </div>
                        </div>
                    </Col>
                </Row>
            </div>
        </div>
    </PageContainer>
  );
};
export default Analysis;
