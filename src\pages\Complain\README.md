# 投诉管理功能实现

## 功能概述

投诉管理模块包含两个主要功能：
1. **患者投诉** - 管理和处理患者投诉信息
2. **绑定推送** - 管理投诉信息的推送接收人配置

## 目录结构

```
src/pages/Complain/
├── Content/                    # 患者投诉管理
│   ├── index.tsx              # 主页面
│   └── components/
│       └── UpdateForm.tsx     # 处理投诉表单
├── Bind/                      # 绑定推送管理
│   ├── index.tsx              # 主页面
│   └── components/
│       ├── CreateForm.tsx     # 新增推送绑定表单
│       ├── UpdateForm.tsx     # 修改推送绑定表单
│       └── FormList.tsx       # 表单字段组件
├── data.d.ts                  # TypeScript 类型定义
└── README.md                  # 说明文档
```

## 功能详情

### 1. 患者投诉管理 (`/complain/content`)

#### 主要功能
- **投诉列表查询**：支持按患者姓名、联系电话、机构、处理状态、时间范围等条件筛选
- **投诉详情查看**：查看投诉的详细信息
- **投诉处理**：对未处理的投诉进行处理，填写处理备注
- **投诉忽略**：对不需要处理的投诉进行忽略操作

#### 数据字段
- 投诉ID、患者姓名、联系电话
- 投诉内容、投诉时间、机构名称
- 处理状态（未处理/已处理/已忽略）
- 处理时间、处理人、处理备注

#### 操作流程
1. 查看投诉列表
2. 点击"详情"查看投诉详细信息
3. 对于未处理的投诉：
   - 点击"处理"填写处理备注并标记为已处理
   - 点击"忽略"直接标记为已忽略

### 2. 绑定推送管理 (`/complain/bind`)

#### 主要功能
- **推送绑定列表**：管理投诉信息的推送接收人配置
- **新增推送绑定**：添加新的推送接收人
- **编辑推送绑定**：修改现有的推送配置
- **删除推送绑定**：移除不需要的推送配置

#### 数据字段
- 接收人姓名、邮箱、电话
- 机构名称、推送类型（邮件/短信/微信）
- 状态（启用/禁用）、创建时间、备注

#### 推送类型
- **邮件推送**：通过邮件发送投诉通知
- **短信推送**：通过短信发送投诉通知
- **微信推送**：通过微信发送投诉通知

## API 接口

### 投诉管理相关
```javascript
// 获取患者投诉列表
queryComplaintList(params)

// 处理投诉
updateComplaint(params)
```

### 推送绑定相关
```javascript
// 获取投诉接收人列表
queryComplaintPushList(params)

// 添加投诉接收人
addComplaintPush(params)

// 更新投诉接收人
updateComplaintPush(params)

// 移除投诉接收人
removeComplaintPush(params)
```

## 技术实现

### 1. 基础架构
- 基于 **Ant Design Pro** 框架
- 使用 **ProTable** 组件实现表格功能
- 使用 **ModalForm** 实现弹窗表单
- 使用 **ProDescriptions** 实现详情展示

### 2. 状态管理
```typescript
// 模态框状态
const [createModalVisible, handleModalVisible] = useState(false);
const [updateModalVisible, handleUpdateModalVisible] = useState(false);
const [showDetail, setShowDetail] = useState(false);

// 当前操作数据
const [currentRow, setCurrentRow] = useState<COMPLAIN.ComplaintListItem>();
```

### 3. 数据处理
```typescript
// 处理投诉
const handleUpdate = async (fields: COMPLAIN.ComplaintListItem) => {
  const hide = message.loading('正在处理');
  try {
    const response = await updateComplaint({ ...fields });
    if (response.code === '0') {
      message.success('处理成功');
      return true;
    } else {
      message.error(response.msg || '处理失败');
      return false;
    }
  } catch (error) {
    message.error('处理失败请重试！');
    return false;
  } finally {
    hide();
  }
};
```

### 4. 表单验证
```typescript
// 邮箱验证
{
  type: 'email',
  message: '请输入正确的邮箱格式',
}

// 手机号验证
{
  pattern: /^1[3-9]\d{9}$/,
  message: '请输入正确的手机号码',
}
```

## 路由配置

在 `config/routes.ts` 中已配置：

```typescript
{
  name: '投诉管理',
  icon: 'DislikeOutlined',
  path: '/complain',
  routes: [
    {
      name: '患者投诉',
      icon: 'ProfileOutlined',
      path: '/complain/content',
      component: './Complain/Content',
    },
    {
      name: '绑定推送',
      icon: 'LinkOutlined',
      path: '/complain/bind',
      component: './Complain/Bind',
    }
  ]
}
```

## 权限控制

- 基于用户角色的菜单显示控制
- 基于机构的数据过滤（通过 `saID` 字段）
- 操作权限控制（查看、处理、删除等）

## 样式和交互

### 1. 状态标识
- **未处理**：默认标签样式
- **已处理**：绿色成功标签
- **已忽略**：红色错误标签

### 2. 操作按钮
- **详情**：查看详细信息
- **处理**：仅对未处理投诉显示
- **忽略**：仅对未处理投诉显示，带确认提示
- **编辑**：修改推送绑定信息
- **删除**：删除推送绑定，带确认提示

### 3. 响应式设计
- 表格支持横向滚动
- 弹窗表单适配不同屏幕尺寸
- 移动端友好的操作体验

## 数据流程

### 投诉处理流程
1. 患者提交投诉 → 系统记录投诉信息
2. 管理员查看投诉列表 → 筛选未处理投诉
3. 处理投诉 → 填写处理备注 → 更新状态为已处理
4. 或选择忽略 → 直接更新状态为已忽略

### 推送配置流程
1. 添加推送接收人 → 配置接收人信息和推送方式
2. 启用推送 → 当有新投诉时自动推送通知
3. 管理推送配置 → 修改或删除不需要的配置

## 扩展功能建议

1. **投诉统计**：添加投诉数据统计和图表展示
2. **自动推送**：集成邮件、短信、微信推送服务
3. **投诉分类**：添加投诉类型分类管理
4. **处理时限**：设置投诉处理时限和提醒
5. **满意度调查**：投诉处理后的满意度反馈
6. **导出功能**：支持投诉数据导出为Excel
7. **批量操作**：支持批量处理多个投诉

## 注意事项

1. **数据安全**：投诉信息涉及患者隐私，需要严格的权限控制
2. **及时处理**：建议设置投诉处理时限和提醒机制
3. **日志记录**：记录所有投诉处理操作的日志
4. **备份机制**：重要投诉数据需要定期备份
5. **性能优化**：大量投诉数据时需要考虑分页和索引优化
