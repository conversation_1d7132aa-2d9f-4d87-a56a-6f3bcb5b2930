import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

type FormValueType = Partial<DEVICES.DeviceListItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
  values: FormValueType;
};

const DigForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, onSubmit, values }) => {
  const { fetchDigAreaList } = useModel('hospital');

  return (
    <ModalForm
      title="设备关联诊区"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={(field) => onSubmit(field)}
      {...formItemLayout}
      initialValues={{ ...values }}
      className="_modal-wrapper"
    >
      <ProFormSelect
        name="digID"
        label="关联诊区"
        request={() => fetchDigAreaList()}
        mode="single"
      />
    </ModalForm>
  );
};

export default DigForm;
