const Contants = {
    // 介质类型
    CARD_TYPE: [
        {
            id: 1,
            content: '身份证'
        },
        {
            id: 2,
            content: '医保卡'
        },
        {
            id: 3,
            content: '医保电子凭证'
        },
        {
            id: 4,
            content: '电子健康卡'
        },
        {
            id: 5,
            content: '健康码'
        }
    ],
    // 支付类型
    PAY_BUSINESS_TYPE: [
        {
            id: 1,
            content: '门诊支付'
        },
        {
            id: 2,
            content: '挂号支付'
        },
        {
            id: 3,
            content: '市民卡充值'
        },
        {
            id: 4,
            content: '住院预缴'
        },
        {
            id: 5,
            content: '医保本购买'
        },
        {
            id: 6,
            content: '出院结算'
        },
        {
            id: 7,
            content: '门诊充值'
        },
        {
            id: 8,
            content: '预约挂号支付'
        }
    ],
    // 支付方式
    PAY_TYPE: [
        {
            id: 0,
            content: '未知'
        },
        {
            id: 1,
            content: '微信'
        },
        {
            id: 2,
            content: '支付宝'
        },
        {
            id: 3,
            content: '现金'
        },
        {
            id: 4,
            content: '银联'
        },
        {
            id: 5,
            content: '医保'
        },
        {
            id: 6,
            content: '支付宝刷脸'
        },
        {
            id: 7,
            content: '支付宝小程序'
        },
        {
            id: 8,
            content: '农行聚合支付'
        },
        {
            id: 9,
            content: '银联聚合支付（云闪付）'
        },
        {
            id: 10,
            content: '微信刷脸'
        },
        {
            id: 11,
            content: '黔农云'
        },
        {
            id: 12,
            content: '市民卡充值'
        },
        {
            id: 17,
            content: '康邻聚合支付'
        },
        {
            id: 20,
            content: '市民卡扣款'
        },
        {
            id: 21,
            content: '支付宝代扣'
        },
        {
            id: 36,
            content: '数字人民币'
        },
        {
            id: 37,
            content: '建行聚合码'
        }
    ],
    // 支付状态
    PAY_STATE :[
        {
            id: 0,
            content: '支付中',
            state: 'Processing'
        },
        {
            id: 1,
            content: '支付成功',
            state: 'Success'
        },
        {
            id: 2,
            content: '退款',
            state: 'Warning'
        },
        {
            id: 3,
            content: '关闭订单',
            state: 'Default'
        },
        {
            id: 4,
            content: '支付失败',
            state: 'Error'
        },
        {
            id: 5,
            content: '退费异常',
            state: 'Error'
        },
        {
            id: 6,
            content: '关闭失败',
            state: 'Error'
        },
        {
            id: 7,
            content: '交易结束不可退款',
            state: 'Default'
        },
        {
            id: 8,
            content: '退款中',
            state: 'Processing'
        },
        {
            id: 9,
            content: '部分退款',
            state: 'Warning'
        }
    ]
}

export const typeObjectFormat = (type: string) =>{
    const typeObject = {};
    const dicList = Contants[type];
    for (let i = 0; i < dicList.length; i++) {
        typeObject[dicList[i].id] = { text: dicList[i].content, status: dicList[i].state || undefined};
    }
    console.log(type, typeObject)
    return typeObject
}

export const typeArrayFormat = (type: string) =>{
    const dicList = Contants[type];
    return dicList.map( (item: any) => item.content)
}


export const findObjectString = (type: string, id: number) =>{
    const dicList = Contants[type];
    return dicList.find(item => item.id === id)?.content
}



