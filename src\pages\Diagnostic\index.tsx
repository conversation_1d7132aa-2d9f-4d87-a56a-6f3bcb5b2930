import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON>, Modal, Drawer, Select} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryDiagnosisList, updateDiagnosis, addDiagnosis } from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { handleOperateMethod } from '@/utils/index';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加诊断信息
 * @param fields
 */
const handleAdd = (fields: DIAGNOSIS.DiagnosisListItem) => handleOperateMethod(addDiagnosis, fields, 'add');
/**
 * @zh-CN 修改诊断信息
 * @param fields
 */
const handleUpdate = (fields: DIAGNOSIS.DiagnosisListItem) => handleOperateMethod(updateDiagnosis, fields, 'update');
/**
 * @zh-CN 删除诊断信息
 * @param deviceID
 */
const handleRemove = (id?: string) =>
  handleOperateMethod(updateDiagnosis, { id, state: 9 }, 'delete');

const TableList = () => {
  const { initialState } = useModel('@@initialState');
  // 字典接口调用
  const { fetchDicList, dicList } = useModel('dictionary');
  /** 新增诊断信息的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改诊断信息的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示诊断详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<DIAGNOSIS.DiagnosisListItem>();
  /** 诊断设置的弹窗 */
  const { hosList, fetchHosList} = useModel('hospital');

  useEffect(() => {
    fetchDicList('DiagnosisType');
  }, []);
  
  const typeObject = {};
  for (let i = 0; i < dicList.length; i++) {
    typeObject[dicList[i].value] = { text: dicList[i].label };
  }

  const columns: ProColumns<DIAGNOSIS.DiagnosisListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      hideInTable: true,
      hideInSearch: true,
      // renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
      //   return (
      //     <Select {...rest} placeholder="请选择">
      //       {hosList &&
      //         hosList.map((c: ListType) => {
      //           return (
      //             <Option value={c.value} key={`${c.value}`}>
      //               {c.label}
      //             </Option>
      //           );
      //         })}
      //     </Select>
      //   );
      // },
    },
    {
      title: '诊断名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '拼音搜索',
      dataIndex: 'pyCode',
      key: 'pyCode',
      hideInTable: true,
    },
    {
      title: '诊断代码',
      dataIndex: 'code',
      key: 'code',
      hideInSearch: true,
    },
    {
      title: '诊断序号',
      dataIndex: 'sort',
      key: 'sort',
      hideInSearch: true,
    },
    {
      title: '诊断类型',
      dataIndex: 'type',
      key: 'type',
      valueEnum: typeObject,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 250,
      render: (_, record) => [
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.name}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.id);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        params={{
          saID: initialState.currentUser.saID
        }}
        request={queryDiagnosisList}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value)
            const success = await handleAdd({...value});
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, id: currentRow?.id});

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions
            column={1}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<DIAGNOSIS.DiagnosisListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
