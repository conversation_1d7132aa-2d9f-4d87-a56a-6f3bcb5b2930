// 字典相关SELECT查询
import {
  useState,
  useCallback
} from 'react';
import {
  queryDictionary
} from '@/services/api/system';
import {
  message
} from 'antd'

export type ListType = {
  value?: string;
  label?: string;
};

export default () => {
  const [dicList, setDicList] = useState<ListType[]>([]);
  const [dicList2, setDicList2] = useState<ListType[]>([]);
  const [dicList3, setDicList3] = useState<ListType[]>([]);
  // 查询字典方法
  const fetchDicList = useCallback(async (type?: string, codeType?: string) => {
    try {
      const res = await queryDictionary({
        type
      });
      if (res.code === '0' && res.data) {
        const data = res.data.map((el: { [key: string]: any }) => {
          if(codeType === 'number'){
            return {
              label: el.content,
              value: parseInt(el.code),
            };
          }
          return {
            label: el.content,
            value: el.code,
          };
        });
        setDicList(data)
        return data
      }else{
        message.error('请求字典信息失败,请刷新重试');
        return false
      }
    } catch (error) {
      message.error('请求数据失败,请刷新重试');
      return false
    }
  }, []);
  // 查询字典方法2
  const fetchDicList2 = useCallback(async (type?: string, codeType?: string) => {
    try {
      const res = await queryDictionary({
        type
      });
      if (res.code === '0' && res.data) {
        const data = res.data.map((el: { [key: string]: any }) => {
          if(codeType === 'number'){
            return {
              label: el.content,
              value: parseInt(el.code),
            };
          }
          return {
            label: el.content,
            value: el.code,
          };
        });
        setDicList2(data)
        return data
      }else{
        message.error('请求字典信息失败,请刷新重试');
        return false
      }
    } catch (error) {
      message.error('请求数据失败,请刷新重试');
      return false
    }
  }, []);
  // 查询字典中文匹配
  const fetchDicList3 = useCallback(async (type?: string) => {
    try {
      const res = await queryDictionary({
        type
      });
      if (res.code === '0' && res.data) {
        const data = res.data.map((el: { [key: string]: any }) => {
          return {
            label: el.content,
            value: el.content,
          };
        });
        setDicList3(data)
        return data
      }else{
        message.error('请求字典信息失败,请刷新重试');
        return false
      }
    } catch (error) {
      message.error('请求数据失败,请刷新重试');
      return false
    }
  }, []);
  return {
    dicList,
    fetchDicList,
    dicList2,
    fetchDicList2,
    dicList3,
    fetchDicList3
  };
};
