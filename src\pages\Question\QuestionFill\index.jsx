import React, { useState } from 'react';
import { Input, Button, Radio, Checkbox, Modal, Card, Row, Col, message, Form } from 'antd';
import { connect, history } from 'umi';

const { TextArea } = Input;

const QuestionListForm = ({
    questions,
    form,
    handleRadioChange,
    handleCheckboxChange,
    handleTextChange,
}) => {
   
    const optionStyle = {
        display: 'block',
        height: '32px',
        lineHeight: '32px'
    };

    const onFinish=(values)=>{
        console.log(values);
    }

    return (
        <Form form={form}  labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} >
            <Row>
                {
                    questions.map((question, questionIndex, array) => {
                        return (<Col span={24}>
                            <Form.Item
                                name={"question"+question.id}
                                label={questionIndex+1+"."+question.title}
                                rules={[{
                                    required: question.required, message:"必填项"
                                }]}
                            >
                            {
                                question.type === 3 ? 
                                <Radio.Group 
                                // onChange={e => handleRadioChange(e, questionIndex)}
                                    value={question.answer}
                                >
                                    {question.options && question.options.map((option, optionIndex) => {
                                        return (
                                            <Radio style={optionStyle} value={option.id} key={optionIndex}>{option.text}</Radio>
                                        );
                                    })}
                                </Radio.Group>:
                                question.type === 2 ?
                                <Checkbox.Group 
                                // onChange={(checkedValues) => handleCheckboxChange(checkedValues, questionIndex)} 
                                
                                >
                                    <Row>
                                        {question.options.map((option, optionIndex) => {
                                            return (
                                                <Col span={24}>
                                                    <Checkbox style={optionStyle} value={option.id} key={optionIndex}>{option.text}</Checkbox>
                                                </Col>
                                            );
                                        })}
                                    </Row>
                                </Checkbox.Group>:
                                <TextArea rows={5} value={question.answer || ""} 
                                // onChange={(e) => handleTextChange(e, questionIndex)} 
                                />
                            }
                            </Form.Item>
                            </Col> ) 
                        })
                }    
            </Row>
        </Form>
    )

}



const Fill = ({
    question: {
        detailType,
        editing,
    }
}) => {
    const [form] = Form.useForm();

    const { title, questions } = editing || {};
    const [questionFields, setQuestionFields] = useState(questions || [])

    const handleRadioChange = (e, questionIndex) => {
        console.log(e);
        let temparr = [...questionFields];
        console.log(temparr)
        temparr[questionIndex].answer = e.target.value;
        setQuestionFields(temparr);

    }

    const handleCheckboxChange = (checkedValues, questionIndex) => {
        console.log(checkedValues)
        let temparr = [...questionFields];
        temparr[questionIndex].answer = checkedValues;
        setQuestionFields(temparr);
    }

    const handleTextChange = (e, questionIndex) => {
        let temparr = [...questionFields];
        temparr[questionIndex].answer = e.target.value;
        setQuestionFields(temparr);

    }

    const handleSubmitQuestionnaire = () => {
        console.log(questionFields)
        let questions = [...questionFields];
        let values = form.getFieldsValue();
        // console.log(values)
        let arr = [];
        for(let key in values){
            arr.push({
                id: key.substring(8),
                answer: values[key]
            })
        }

        console.log(arr)
        questions.forEach((el)=>{
            arr.forEach((el2)=>{
                if(el.id.toString() === el2.id){
                    console.log("======相等")
                    el.answer = el2.answer
                }
            })
        })

        console.log(questions)

    }



    return (
        <Card>
            <div className="editTitle" style={{ margin: '0 20px 20px 20px', padding: 20, textAlign: 'center' }}>
                <h2><strong>{title}</strong></h2>
            </div>
            <div style={{ marginBottom: 20, padding: 20, borderTop: '2px solid #ccc', borderBottom: '2px solid #ccc' }}>
                <QuestionListForm
                    form={form}
                    questions={questionFields}
                    // handleRadioChange={handleRadioChange}
                    // handleCheckboxChange={handleCheckboxChange}
                    // handleTextChange={handleTextChange}
                />
            </div>
            {/* <div style={{ padding: 20, textAlign: 'center' }}>
                <Button type="primary" size="large" onClick={handleSubmitQuestionnaire}>提交问卷</Button>
            </div> */}
        </Card>
    );
}

export default connect(({ question }) => ({
    question
}))(Fill);