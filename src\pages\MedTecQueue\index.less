@import (reference) '~antd/es/style/themes/index';
.header{
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    border-bottom: 1px solid #eee;
    padding: 0 20px 10px;
    margin-bottom: 10px;
    .digName{
        color: #5197FE;
        font-size: 26px;
        max-width: 300px;
        overflow: hidden;        
        text-overflow:ellipsis; 
        white-space: nowrap;
    }
    .title{
        font-size: 30px;
        align-items: flex-end;
        font-weight: 400;
        letter-spacing: 2px;
    }
    .time{
        font-size: 15px 
    }

    .right{
        display: flex;
        align-items: center;
        .roomName{
            color: #5197FE;
            font-size: 26px;
            // width: 200px;
            margin-right: 20px;
            text-align: right;
        }
    }

}

.wrapperDiv{
    max-height: calc(100vh - 100px);
    overflow-y: scroll;  
}

.digDataName{
    font-size: 18px;
    color: #5197FE;
    font-weight: bold;
    width: 200px;
    overflow: hidden;        
    text-overflow:ellipsis; 
    white-space: nowrap;
}

.num{
    font-size: 20px;
    font-weight: bold;
}

.callingInfo{
    text-align: center;
    font-size: 18px;
}

.callingPatient{
    text-align: center;
    margin-top: 5px;
}

.callingDoctor{
    text-align: center;
    margin-top: 20px;
    span{
        font-size: 15px;
        font-weight: bold;
    }
}

.icon{
    width: 50px;
    height: 50px;
    border-radius: 50%;
    text-align: center;
    line-height: 50px;
    color: #eee;
    font-size: 20px;
}

.deptBlockTitle{
    font-size: 18px;
    font-weight: bold;
}

.table{
    margin-top: 20px;
}

.selectedRowTip{
    margin-bottom: 10px; 
    padding: 10px 10px; 
    background-color: #e6f7ff;
    display: flex;
    justify-content: space-between;
}

.searchbox{
    // background-color: antiquewhite;
    :global{
        .ant-form-item{
            margin-bottom: 0;
        }
    }

}



