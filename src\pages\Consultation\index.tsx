import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON>, Modal, Drawer, Select, Image, message} from 'antd';
import { useState, useRef, useEffect, useMemo, createRef, Fragment} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { getConsultationList } from '@/services/api/logs';
import { queryGenerateToken } from '@/services/api/audio';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import DingRTC from 'dingrtc'
import { encrypt } from '@/utils'
import RomateVideo from '@/components/RomateVideo/index_srs';
// import { CallViewProvider} from '@xkit-yx/call-kit-react-ui'
// import type { CallViewProviderRef } from '@xkit-yx/call-kit-react-ui';
// import V2NIM from 'nim-web-sdk-ng'
// import '@xkit-yx/call-kit-react-ui/es/style'
import './index.less'

const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

export type tokenType = {
  userID?: string;
  channelID?: string;
  token?: string
};

const appID = 'u2rum00l';

/**
 * @zh-CN 请求硬件异常信息
 * @param fields
 */
const handleQueryGenerateToken = async (fields: tokenType) => {
  try {
    const response = await queryGenerateToken({ ...fields });
    if (response.code === '0') {
      return response.data;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    message.error('获取音视频通话token失败');
    return false;
  }
};

// const appkey = '594e0b4ae30fecc68c59821240373291' // 请填写你的appkey
// const account = '68558' // 请填写你的account
// const token = 'e077bb0f-9269-442a-887c-d13e9d2403d7' // 请填写你的token

const TableList = () => {
  const [isLogin, setIsLogin] = useState<boolean>(false)
  const [deviceCode, setDeviceCode] = useState<string>('')
  const { initialState } = useModel('@@initialState');  
  /** 新增开单信息的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改开单信息的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示开单详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  const romateVideoRef = createRef<any>()
  // const callViewProviderRef = createRef<CallViewProviderRef>()
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<CONSULTATION.ItemListItem>();

  const { hosList, fetchHosList } = useModel('hospital');
  

  useEffect(() => {
    fetchHosList();

    return ()=>{
      setIsLogin(false)
      setDeviceCode('')
    }
  }, []);

  // /**
  //  * 创建客户端实例
  //  */
  // const client = DingRTC.createClient();

  // const supported = DingRTC.checkSystemRequirements();
  // if (supported) {
  //   //支持webrtc
  //   console.log('支持webrtc')
  // } else {
  //   //不支持webrtc
  //   console.log('支持webrtc')
  // }

  const callAudio = async (value) => {
    if(!value) return message.error('该记录未绑定设备号，无法呼叫')
    if(isLogin) return message.error('正在通话其他设备中')
    setIsLogin(true)
    setDeviceCode(value)
    // romateVideoRef.current?.call({
    //   // userID: '1234',
    //   channelID: 'PH90118231',
    //   userName: initialState?.currentUser.realName,
    //   // userName: 'admin'
    // })
  }


    useEffect(() => {
    if (romateVideoRef.current?.call && deviceCode) {
      romateVideoRef.current?.call({
        // userID: '1234',
        channelID: deviceCode,
        userName: initialState?.currentUser.realName,
        // userName: 'admin'
      })
    }
  }, [romateVideoRef.current?.call, deviceCode])
  

  // const callAudio = async (deviceCode) => {
  //   const uid = '123';
  //   const channelID = 'asd123';
  //   const userName = '胡'
  //   const token = await handleQueryGenerateToken({
  //     userID: encrypt('123'),
  //     channelID: encrypt('asd123')
  //   })
  //   console.log(token)
  //   await client.join({
  //     appId: appID,
  //     token: token,
  //     uid: uid,
  //     channel: channelID,
  //     userName: userName,
  //  });

  //   // 摄像头轨道
  //   const cameraTrack = await DingRTC.createCameraVideoTrack({
  //     frameRate: 15,
  //     dimension: 'VD_1280x720',
  //   });
  //   // 麦克风轨道
  //   const micTrack = await DingRTC.createMicrophoneAudioTrack();

  //   cameraTrack.play('#player');
  //   micTrack.play();
  // }

  // const nim = useMemo(() => {
  //   return V2NIM.getInstance({
  //     appkey,
  //     account,
  //     token,
  //     apiVersion: 'v2',
  //     debugLevel: 'debug',
  //   })
  // }, [])


  // useEffect(() => {
  //   if (nim) {
  //     // 当 App 完成渲染后，登录 IM
  //     nim.V2NIMLoginService.login(account, token, {
  //       retryCount: 5,
  //     }).then(() => {
  //       setIsLogin(true)
  //     })
  //   }

  //   // 当 App 卸载时，登出 IM
  //   return () => {
  //     if (nim) {
  //       nim.V2NIMLoginService.logout().then(() => {
  //         setIsLogin(false)
  //       })
  //     }
  //   }
  // }, [nim])

  // useEffect(() => {
  //   if (callViewProviderRef.current?.neCall) {
  //     //注册呼叫结束事件监听
  //     callViewProviderRef.current?.neCall?.on('onRecordSend', (options) => {
  //       //@ts-ignore
  //       // store.msgStore.addMsg(options.conversationId, [options])
  //       document.getElementById(options.messageClientId)?.scrollIntoView({
  //         block: 'nearest', // 滚动到目标元素的最近可见位置
  //         inline: 'nearest', // 避免水平方向的滚动
  //       })
  //     })
  //     // 设置呼叫超时时间
  //     callViewProviderRef.current?.neCall?.setTimeout(60)
  //     // 接通成功事件
  //     callViewProviderRef.current?.neCall?.on('onCallConnected', () => {
  //       // 暂停音视频消息的播放
  //       // pauseAllAudio()
  //       // pauseAllVideo()
  //     })
  //   }
  // }, [callViewProviderRef.current?.neCall])

  const columns: ProColumns<CONSULTATION.ItemListItem>[]  = [
    {
      title: '患者ID',
      dataIndex: 'patientID',
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
    },
    {
      title: '发票号',
      dataIndex: 'invoice',
    },
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.map((c: ListType) => {
          return {
            value: c.value,
            label: c.label,
          };
        })
        return (
          <Select placeholder="请选择" showSearch optionFilterProp="label" options={options} allowClear/>
        );
      },
      colSize: 2,
    },
    {
      title: '病情描述',
      dataIndex: 'describes',
      hideInSearch: true,
      ellipsis: true,
      hideInDescriptions: true,
    },
    {
      title: '病情描述',
      dataIndex: 'describes',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '诊断信息',
      dataIndex: 'diagnosis',
      hideInSearch: true,
    },
    {
      title: '处方信息',
      dataIndex: 'prescriptionStr',
      render: (prescription: string) => 
        {
          return prescription.split('|').map(item=>{
            return <>
              <div key={item}>{ item.split(',')[0] }&nbsp;&nbsp;{item.split(',')[1]}</div>
            </>
          })
        },
      hideInSearch: true,
    },
    {
      title: '签名数据',
      dataIndex: 'signData',
      hideInSearch: true,
      width: 100,
      render: (signData: string) => 
        {
          return <Image src={signData} width={100}/>
        },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInTable: true,
      hideInDescriptions: true,
      valueType: 'dateRange', 
      search: {
        transform: (value) => {
          return {
            beginTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '首次用药',
      dataIndex: 'nonFirstuse',
      hideInSearch: true,
      hideInTable: true,
      valueEnum: {
        "1": {
          text: '是'
        },
        "0": {
          text: '否'
        }
      }
    },
    {
      title: '是否过敏',
      dataIndex: 'allergyCheck',
      hideInSearch: true,
      hideInTable: true,
      valueEnum: {
        "1": {
          text: '是'
        },
        "0": {
          text: '否'
        }
      }
    },
    {
      title: '是否孕期',
      dataIndex: 'breastfeedingCheck',
      hideInSearch: true,
      hideInTable: true,
      valueEnum: {
        "1": {
          text: '是'
        },
        "0": {
          text: '否'
        }
      }
    },
    {
      title: '是否肝肾异常',
      dataIndex: 'allergyCheck',
      hideInSearch: true,
      hideInTable: true,
      valueEnum: {
        "1": {
          text: '是'
        },
        "0": {
          text: '否'
        }
      }
    },
    {
      title: '是否有既往病史',
      dataIndex: 'allergyCheck',
      hideInSearch: true,
      hideInTable: true,
      width: 120,
      valueEnum: {
        "1": {
          text: '是'
        },
        "0": {
          text: '否'
        }
      }
    },
    {
      title: '既往病史信息',
      dataIndex: 'medicalHistory',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 130,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a 
          key="media"
          onClick={async () => {
            // callViewProviderRef.current?.call({
            //   accId: 'wangbotest', // 被叫im账号
            //   callType: '2', // 1: 音频通话 2: 视频通话
            //   extraInfo: JSON.stringify({"key":"call","value":"testValue","userName":"**********"})
            // })
            await callAudio(record.deviceCode)
          }}
        >
          视频通话
        </a>
    ]}
  ];
  return (
    <>
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="id"
        scroll={{ x: 1500 }}
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
        ]}
        // params={{
        //   saID: initialState.currentUser.saID
        // }}
        request={getConsultationList}
        columns={columns}
      />
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions
            column={1}
            title={currentRow?.patientName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<CONSULTATION.ItemListItem>[]}
          />
        )}
      </Drawer>
      
      {/* <div className="media-wrapper" id="player"></div> */}
      {/* { nim && isLogin ? (
        <div className="media-wrapper">
          <CallViewProvider
            ref={callViewProviderRef}
            neCallConfig={{
              nim,
              appkey, // 应用 
              debug: true,
            }}
            position={{
              x: 10,
              y: 10,
            }}
          >
            <Fragment />
          </CallViewProvider>
        </div>
      ) : null
      } */}
    </PageContainer>
    { isLogin && <RomateVideo ref={romateVideoRef} setIsLogin={setIsLogin} setDeviceCode={setDeviceCode} /> } 
    </>
  );
};

export default TableList;
