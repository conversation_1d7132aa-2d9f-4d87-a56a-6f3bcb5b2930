import {
    PlusOutlined,
    InfoCircleOutlined,
    AppstoreOutlined,
    UnorderedListOutlined,
    ArrowDownOutlined,
    CloudUploadOutlined,
    RedoOutlined,
    StopOutlined,
    SoundOutlined
  } from '@ant-design/icons';
  import { Button, message, Modal, Radio, Popover, Tag, Drawer, Select, Space, Switch, Dropdown, Menu} from 'antd';
  import { useState, useRef, useEffect } from 'react';
  import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
  import { ProFormRadio, ProFormSwitch, ProList } from '@ant-design/pro-components'
  import type { ActionType, ProColumns, ProDescriptionsItemProps} from '@ant-design/pro-components';
  import type { MenuProps } from 'antd';
  import { queryDevicesList, updateDevice, addDevice, cmdDevice, getDeviceFaultInfo} from '@/services/api/devices';
  import { useModel } from '@umijs/max';
  import ProTable from '@ant-design/pro-table';
  import ProDescriptions from '@ant-design/pro-descriptions';
  import BoardcastForm  from '@/pages/Devices/Terminal/components/BoardcastForm';
  import { handleOperateMethod } from '@/utils/index';
  
  const { Option } = Select;
  export type ListType = {
    value?: any;
    label?: string;
  };
  
  /**
   * @zh-CN 添加设备
   * @param fields
   */
  const handleAdd = (fields: DEVICES.DeviceListItem) => handleOperateMethod(addDevice, fields, 'add');
  /**
   * @zh-CN 更新设备
   * @param fields
   */
  const handleUpdate = (fields: DEVICES.DeviceListItem) => handleOperateMethod(updateDevice, fields, 'update');
  /**
   * @zh-CN 删除设备
   * @param deviceID
   */
  const handleRemove = (deviceID?: number) =>
    handleOperateMethod(updateDevice, { deviceID, state: 9 }, 'delete');
  /**
   * @zh-CN 启用设备
   * @param deviceID
   */
  const handleStart = (deviceID?: number) =>
    handleOperateMethod(updateDevice, { deviceID, state: 1 }, 'start');
  /**
   * @zh-CN 停用设备
   * @param deviceID
   */
  const handleStop = (deviceID?: number) => handleOperateMethod(updateDevice, { deviceID, state: 0 }, 'stop');
  /**
   * @zh-CN 设备指令
   * @param fields
   */
  const handleSendCmd = (fields: DEVICES.CmdTypeItem) => handleOperateMethod(cmdDevice, fields, 'send');

  
  const TableList = (props) => {
    /** 新增设备的弹窗 */
    const [createModalVisible, handleModalVisible] = useState(false);
    /** 修改设备的弹窗 */
    const [updateModalVisible, handleUpdateModalVisible] = useState(false);
    /** 设备下发广告的弹窗 */
    const [adModalVisible, handleAdModalVisible] = useState(false);  // 广告弹出框
    /** 版本更新下发的弹窗 */
    const [versionModalVisible, handleVersionModalVisible] = useState(false); // 版本更新弹出框
    /** 广播下发的弹窗 */
    const [boardcastModalVisible, handleBoardcastModalVisible] = useState(false);  // 广播下发弹出框
    /** 设备绑定诊室的弹窗 */
    const [clinicModalVisible, handleClinicModalVisible] = useState(false); // 设备绑定诊室
    /** 设备硬件状态的弹窗 */
    const [hardwareModalVisible, handleHardwareModalVisible] = useState(false); // 设备硬件状态
    /** 展示设备详情的弹窗 */
    const [showDetail, setShowDetail] = useState(false);
    const actionRef = useRef<ActionType>();
    /** 取当前行数据 */ 
    const [currentRow, setCurrentRow] = useState<DEVICES.DeviceListItem>();
    const [selectedRowsState, setSelectedRows] = useState<DEVICES.DeviceListItem[]>([]);
    const [formStyle, handleFormStyleChange] = useState('table'); // 列表或图片模式展示切换
    const { dicList, fetchDicList } = useModel('dictionary');
    const { roleList, fetchRoleList } = useModel('system');
    const { hosList, fetchHosList } = useModel('hospital');
  
    useEffect(() => {
      fetchHosList();
      fetchDicList('deviceType', 'number');
      fetchRoleList();
    }, []);
  
    const extraContent = (
      <Radio.Group
        defaultValue={formStyle}
        value={formStyle}
        buttonStyle="solid"
        onChange={(e) => {
          handleFormStyleChange(e.target.value);
        }}
      >
        <Radio.Button value="list">
          <Space>
            <AppstoreOutlined />
            <span>大图模式</span>
          </Space>
        </Radio.Button>
        <Radio.Button value="table">
          <Space>
            <UnorderedListOutlined />
            <span>列表模式</span>
          </Space>
        </Radio.Button>
      </Radio.Group>
    );

    const columns: ProColumns<DEVICES.DeviceListItem>[]  = [
      {
        title: '终端编号',
        dataIndex: 'deviceCode',
        key: 'deviceCode',
        width: 100,
        fixed: 'left',
        hideInSearch: true,
        hideInTable: true,
      },
      {
        title: 'HIS终端编号',
        dataIndex: 'hisDeviceCode',
        width: 120,
        hideInSearch: true,
        hideInTable: true,
      },
      {
        title: '终端名称',
        dataIndex: 'deviceName',
        key: 'deviceName',
        width: 120,
        fixed: 'left',
      },
      {
        title: '设备位置',
        dataIndex: 'deviceAddress',
        width: 120,
        hideInSearch: true,
      },
      {
        title: '关联诊室',
        dataIndex: 'roomID',
        key: 'roomID',
        valueType: 'select',
        render: (tags, record) => {
          if (!record) return '';
          const roomName = record.roomName ? record.roomName.split(',') : [];
          return (
            <span>
              {roomName.map((tag) => {
                return (
                  <Tag color="geekblue" key={tag}>
                    {tag}
                  </Tag>
                );
              })}
            </span>
          );
        },
        hideInSearch: true,
        hideInTable: true,
      },
      {
        title: '关联科室',
        dataIndex: 'deptCode',
        render: (tags, record) => {
          if (!record) return '';
          const deptName = record.deptName ? record.deptName.split(',') : [];
          return (
            <span>
              {deptName.map((tag) => {
                return (
                  <Tag color="green" key={tag}>
                    {tag}
                  </Tag>
                );
              })}
            </span>
          );
        },
        width: 180,
        hideInSearch: true,
        hideInTable: true,
      },
      {
        title: 'IP地址',
        dataIndex: 'deviceIP',
        width: 150,
        hideInSearch: true,
      },
      {
        title: '设备类型',
        dataIndex: 'deviceType',
        width: 150,
        renderText: (_, record) => {
          return record.deviceTypeName;
        },
        renderFormItem: (item, { type, defaultRender, ...rest }) => {
          return (
            <Select {...rest} placeholder="请选择">
              {dicList &&
                dicList.map((c: ListType) => {
                  return (
                    <Option value={c.value} key={`deviceType${c.value}`}>
                      {c.label}
                    </Option>
                  );
                })}
            </Select>
          );
        },
      },
      {
        title: '状态',
        dataIndex: 'state',
        valueEnum: {
          0: {
            text: '未启用',
            status: 'Default',
          },
          1: {
            text: '正常',
            status: 'Processing',
          },
          2: {
            text: '异常',
            status: 'Error',
          },
          3: {
            text: '离线',
            status: 'Warning',
          },
        },
        width: 100,
      },
    ];
    
    return (
    <Modal
        title="设备列表"
        width={1080}
        bodyStyle={{ padding: 0}}
        open={props.modalVisible}
        onCancel={props.onCancel}
        footer={false}
        className="_modal-wrapper"
    >
        <ProTable
          actionRef={actionRef}
          rowKey="deviceID"
          search={false}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
          toolBarRender={() => [
            selectedRowsState?.length > 0 && (
              <Button
                type="primary"
                key="primary"
                onClick={() => {
                  Modal.confirm({
                    title: '确认重启',
                    icon: <InfoCircleOutlined />,
                    content: `是否确认重启这${selectedRowsState.length}台设备?`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: async () => {
                      const success = await handleSendCmd({
                        cmdContent: '',
                        cmdSrc: '',
                        deviceIDs: selectedRowsState.map((item)=> item.deviceID).join(','),
                        optType: 1
                      });
                      if (success) {
                        if (actionRef.current) {
                          actionRef.current.reload();
                        }
                      }
                    }
                  });
                }}
              >
                <RedoOutlined /> 批量重启
              </Button>
            ),
            selectedRowsState?.length > 0 && (
              <Button
                type="primary"
                key="primary"
                onClick={() => {
                  Modal.confirm({
                    title: '确认关机',
                    icon: <InfoCircleOutlined />,
                    content: `是否确认关机这${selectedRowsState.length}台设备?`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: async () => {
                      const success = await handleSendCmd({
                        cmdContent: '',
                        cmdSrc: '',
                        deviceIDs: selectedRowsState.map((item)=> item.deviceID).join(','),
                        optType: 2
                      });
                      if (success) {
                        if (actionRef.current) {
                          actionRef.current.reload();
                        }
                      }
                    }
                  });
                }}
              >
                <StopOutlined /> 批量关机
              </Button>
            ),
            selectedRowsState?.length > 0 && (
              <Button
                type="primary"
                key="primary"
                onClick={() => {
                  handleBoardcastModalVisible(true);
                }}
              >
                <SoundOutlined /> 设备广播
              </Button>
            ),
          ]}
          request={queryDevicesList}
          params={{deviceType: '9'}}
          columns={columns}
          rowSelection={{
            onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }}
        />
        {
          boardcastModalVisible && <BoardcastForm
            onSubmit={async values => {
              const success = await handleSendCmd({...values, deviceIDs: selectedRowsState.map((item)=> item.deviceID).join(',')});
  
              if (success) {
                handleBoardcastModalVisible(false);
                if (actionRef.current) {
                  actionRef.current?.reload?.();
                }
              }
            }}
            onCancel={() => { handleBoardcastModalVisible(false);}}
            modalVisible={ boardcastModalVisible }
          />
        }
      </Modal>
    );
  };
  
  export default TableList;
  