import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON>, Mo<PERSON>, Drawer } from 'antd';
import { useState, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryHosList, updateHos, addHos } from '@/services/api/hospital';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { handleOperateMethod } from '@/utils/index';

/**
 * @zh-CN 添加机构
 * @param fields
 */
const handleAdd = (fields: HOSPITAL.HosListItem) => handleOperateMethod(addHos, fields, 'add');
/**
 * @zh-CN 修改机构
 * @param fields
 */
const handleUpdate = (fields: HOSPITAL.HosListItem) => handleOperateMethod(updateHos, fields, 'update');
/**
 * @zh-CN 删除机构
 * @param deviceID
 */
const handleRemove = (saID?: string) =>
  handleOperateMethod(updateHos, { saID, state: 9 }, 'delete');

const TableList = () => {
  /** 新增机构的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改机构的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示机构详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<HOSPITAL.HosListItem>();

  const columns: ProColumns<HOSPITAL.HosListItem>[]  = [
    {
      title: '机构ID',
      dataIndex: 'saID',
      key: 'saID',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '机构名称',
      dataIndex: 'hospitalName',
      key: 'hospitalName',
    },
    {
      title: '机构代码',
      dataIndex: 'systemKey',
      key: 'systemKey',
    },
    {
      title: '联系人名称',
      dataIndex: 'contactName',
      key: 'contactName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      key: 'createTime',
      hideInSearch: true,
      hideInForm: true,
    },
    // {
    //   title: '医院logo',
    //   dataIndex: 'iconUrl',
    //   key: 'iconUrl',
    //   hideInSearch: true,
    // },
    // {
    //   title: 'HIS接口地址',
    //   dataIndex: 'hisUrl',
    //   key: 'hisUrl',
    //   hideInSearch: true,
    // },
    // {
    //   title: '支付接口地址',
    //   dataIndex: 'payUrl',
    //   key: 'payUrl',
    //   hideInSearch: true,
    // },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 300,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.hospitalName}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.saID);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        request={queryHosList}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value);
            const success = await handleAdd(value);
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reloadAndRest?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, saID: currentRow?.saID });

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reloadAndRest?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.hospitalName && (
          <ProDescriptions
            column={1}
            title={currentRow?.hospitalName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.saID,
            }}
            columns={columns as ProDescriptionsItemProps<HOSPITAL.HosListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
