import { useState } from 'react';
import { ProFormText, ProFormSelect, ProFormTextArea, ProFormUploadButton} from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-components';
import {message, Upload} from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import { useModel } from '@umijs/max';
import defaultSettings from '../../../../../config/defaultSettings';


const UPLOAD_URL = `/${defaultSettings.apiName}/system/uploadDoctorFiles.np`


const FormList = () => {
  const { fetchDeptList } = useModel('hospital');
  const [ fileList, setFileList ] = useState<UploadFile[]>([])
  const [ filePath, setFilePath ] = useState<string>('')
  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '医生姓名为必填项',
          },
        ]}
        name="doctorName"
        label="医生姓名"
        placeholder="请输入医生姓名"
      />
      <ProFormText 
        rules={[
          {
            required: true,
            message: '医生代码为必填项',
          },
        ]} 
        name="doctorCode" 
        label="医生代码"
        placeholder="请输入医生代码"
      />
      <ProFormText
        name="doctorNameSpelling"
        label="姓名拼音"
        placeholder="请输入医生姓名拼音"
      />
      <ProFormSelect
        name="deptCode"
        label="医生科室"
        rules={[
          // {
          //   required: true,
          //   message: '医生科室为必填项',
          // },
        ]}
        colProps={{ span: 12 }}
        request={() => fetchDeptList()}
        showSearch
      />
      <ProFormText name="title" label="医生职称" placeholder="请输入医生职称" />
      <ProFormTextArea name="professional" label="医生擅长" placeholder="请输入医生擅长" />
      <ProFormTextArea name="profile" label="医生简介" placeholder="请输入医生简介" />
      <ProFormDependency name={['doctorCode']}>
        {({ doctorCode }) => {
          return (
            <ProFormUploadButton 
              name="icon" 
              label="医生照片" 
              fieldProps={{
                name: 'multipartFile',
                listType: 'picture-card',
                data: {
                  moduleSrc: "doctorIcon",
                  uploadType: 1,
                  doctorCode: doctorCode
                },
                action: UPLOAD_URL,
                headers: {
                  authorization: 'authorization-text',
                },
                beforeUpload: (file) => {
                  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif';
                  const isSize = file.size / 1024;
                  if (!doctorCode) {
                    message.error('上传医生图片请先输入医生代码');
                    return Upload.LIST_IGNORE
                  }
                  if (!isJpgOrPng) {
                    message.error('只能提交JPG/PNG/GIF格式文件');
                    return Upload.LIST_IGNORE
                  }
                  // if (isSize > 100) {
                  //   message.error('图片大小不可超过100KB')
                  //   return Upload.LIST_IGNORE
                  // }
                  return true;
                },
                onChange(info: any) {
                  console.log(info)
                  let proUploadList = [...info.fileList];
                  proUploadList = proUploadList.slice(-1);
                  // setFileList(proUploadList)
                  if (info.file.status !== 'uploading') {
                    console.log(info.file, info.fileList);
                  }
                  if (info.file.status === 'done') {
                    message.success(`${info.file.name} 文件上传成功。`);
                    setFilePath(info.file.response.data)
                  } else if (info.file.status === 'error') {
                    message.error(`${info.file.name} 文件上传失败。`);
                  }
                },
              }} 
              // fileList={fileList} 
              max={1}
            />
          );
        }}
      </ProFormDependency> 
    </>
  );
};

export default FormList;
