/**
 * 将文本中的换行符 \n 转换为 React 元素中的 <br /> 标签
 * @param text 包含换行符的文本
 * @param keyPrefix 用于生成唯一key的前缀
 * @returns React 元素数组
 */
export const renderTextWithLineBreaks = (text: string, keyPrefix: string = 'line') => {
  if (!text) return null;
  
  const lines = text.split('\n');
  return lines.map((line, index) => (
    <span key={`${keyPrefix}-${index}`}>
      {line}
      {index < lines.length - 1 && <br />}
    </span>
  ));
};

/**
 * 将文本中的换行符 \n 转换为多个 div 元素（用于表格显示）
 * @param text 包含换行符的文本
 * @param keyPrefix 用于生成唯一key的前缀
 * @returns React 元素
 */
export const renderTextWithLineDivs = (text: string, keyPrefix: string = 'line') => {
  if (!text) return '-';
  
  const lines = text.split('\n');
  return (
    <div>
      {lines.map((line, index) => (
        <div key={`${keyPrefix}-${index}`}>{line || '\u00A0'}</div>
      ))}
    </div>
  );
};

/**
 * 将文本中的换行符转换为HTML的<br>标签（用于innerHTML）
 * @param text 包含换行符的文本
 * @returns 转换后的HTML字符串
 */
export const convertLineBreaksToHtml = (text: string): string => {
  if (!text) return '';
  return text.replace(/\n/g, '<br />');
};

/**
 * 移除文本中的换行符，用空格替代
 * @param text 包含换行符的文本
 * @returns 移除换行符后的文本
 */
export const removeLineBreaks = (text: string): string => {
  if (!text) return '';
  return text.replace(/\n/g, ' ');
};

/**
 * 限制文本显示长度，超出部分用省略号表示，保留换行符
 * @param text 原始文本
 * @param maxLength 最大长度
 * @returns 处理后的文本
 */
export const truncateTextWithLineBreaks = (text: string, maxLength: number = 100): string => {
  if (!text) return '';
  
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength) + '...';
};

/**
 * 计算包含换行符的文本的行数
 * @param text 包含换行符的文本
 * @returns 行数
 */
export const countTextLines = (text: string): number => {
  if (!text) return 0;
  return text.split('\n').length;
};

/**
 * 为表格单元格渲染带换行的文本（带省略号支持）
 * @param text 文本内容
 * @param maxLength 最大显示长度
 * @param keyPrefix key前缀
 * @returns React元素
 */
export const renderTableCellWithLineBreaks = (
  text: string, 
  maxLength: number = 100, 
  keyPrefix: string = 'cell'
) => {
  if (!text) return '-';
  
  const truncatedText = truncateTextWithLineBreaks(text, maxLength);
  const lines = truncatedText.split('\n');
  
  return (
    <div title={text}> {/* 完整文本作为tooltip */}
      {lines.map((line, index) => (
        <div key={`${keyPrefix}-${index}`}>{line || '\u00A0'}</div>
      ))}
    </div>
  );
};
