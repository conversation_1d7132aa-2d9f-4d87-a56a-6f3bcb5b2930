declare namespace LOGS {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 日志详情
    type LogListItem = {
        id: number,   // id 
        state: number,  // 状态
        requestTime: string,  // 请求时间
        returnCode: string,   // 请求状态码
        requestAction: string,  // 请求方法名
        requestPar?: any,  // 请求入参
        returnPar?: any,   // 请求返参
        requestType?: string,  // 请求类型
        requestDuration?: number, // 请求时长
        patientID?: string // 患者ID
        deviceCode?: string // 终端编号
    };
    // 设备列表
    type LogList = {
        /** 列表的内容 **/
        data?: LogListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}