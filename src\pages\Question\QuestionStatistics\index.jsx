import React, { Component, Suspense } from 'react';
import { GridContent } from '@ant-design/pro-layout';
import { Card, Button, Row, Col, Divider } from 'antd';
import { connect, history } from 'umi';
import PageLoading from '@/components/PageLoading';
import { RollbackOutlined } from '@ant-design/icons';
import './index.less';

const ProportionPie = React.lazy(() => import('./Components/ProportionPie.jsx'));

class Statistics extends Component {
    state = {
        
    }

    render() {
        // const {} = this.props;

        return (
            <GridContent>
                <Card
                    className="question-card"
                    title={
                        <div className="editTitle" style={{ padding: 20, textAlign: 'center' }}>
                            <h2><strong>问卷调查-数据分析</strong></h2>
                        </div>
                    }
                    extra={<Button type="primary" onClick={() => history.replace('/question/list')}><RollbackOutlined />返回</Button>}
                >
                    <Row gutter={20}>
                        <Col span={12}>
                            <Suspense fallback={<PageLoading />}>
                                <ProportionPie />
                            </Suspense>
                        </Col>
                        <Col span={12}>
                            <Suspense fallback={<PageLoading />}>
                                <ProportionPie />
                            </Suspense>
                        </Col>
                    </Row>
                </Card>
            </GridContent>
        )
    }


}

export default connect(({ question }) => ({
    question
}))(Statistics);




