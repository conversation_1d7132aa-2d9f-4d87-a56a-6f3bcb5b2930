// 目录
import React, {useRef,useState,useEffect} from 'react';
import { List, Typography, Anchor } from 'antd';
import { connect, history } from 'umi';

const { Link } = Anchor;

const Catalogue = ({
    question: {
        editing
    },
    scrollToAnchor
}) => {
    const [data, setData] = useState([])
    const [selectItem, setSelectItem] = useState("")

    useEffect(()=>{
        let arr = editing.questions ?  editing.questions : [];
        const catalogueData = [];
        arr.forEach((item, index) => {
            if(item.state !== 9 ){
                catalogueData.push({
                    name: item.title,
                    anchorID: "anchorID" + index,
                })
            }
        })
        setData(catalogueData)
    },[editing.questions])

    const handleClick=(anchorName, ind)=>{
        scrollToAnchor(anchorName, ind)
        setSelectItem(ind)
    }
    
    return (
        <div className="scorll-box">
            <List
                header={null}
                footer={null}
                size="small"
                dataSource={data}
                // locale={false}
                locale={{emptyText: "  "}}
                renderItem={(item, index) => (
                    <List.Item>
                        {/* <Anchor>
                            <Link href={item.anchorID} title={item.name} />
                        </Anchor> */}
                        <span className={selectItem===index?"item-title actived":"item-title"} style={{cursor:"pointer"}} onClick={e => handleClick(item.anchorID, index)} >
                            {/* <Typography.Text mark>{`Q${index + 1}.`}</Typography.Text>  */}
                             {`Q${index + 1}.${item.name}`}
                        </span>
                    </List.Item>
                )}
            />
        </div>
    )

}


export default connect(({ question }) => ({
	question
}))(Catalogue); 