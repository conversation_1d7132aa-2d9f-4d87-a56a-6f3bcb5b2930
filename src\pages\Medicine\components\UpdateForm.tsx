import { ModalForm } from '@ant-design/pro-form';
import FormList from './FormList';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

type FormValueType = Partial<MEDICINE.MedicineListItem>;

export type UpdateFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  updateModalVisible: boolean;
  values: FormValueType;
};

const UpdateForm: React.FC<UpdateFormProps> = ({ updateModalVisible, values, onCancel, onSubmit }) => {
  return (
    <ModalForm
      title="修改药品信息"
      layout="horizontal"
      width={640}
      open={updateModalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={onSubmit}
      {...formItemLayout}
      initialValues={{ ...values}}
      className="_modal-wrapper"
    >
      <FormList />
    </ModalForm>
  );
};

export default UpdateForm;
