import { ProFormText, ProFormSelect} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {

  const { fetchDeptListByID } = useModel('hospital');
  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '诊区名称为必填项',
          },
        ]}
        name="digAreaName"
        label="诊区名称"
        placeholder="请输入诊区名称"
      />
      <ProFormText 
        rules={[
          {
            required: true,
            message: '诊区代码为必填项',
          },
        ]} 
        name="digAreaCode" 
        label="诊区代码"
        placeholder="请输入诊区代码"
      />
      <ProFormText name="remark" label="备注" placeholder="请输入备注" />
      <ProFormSelect name="deptIDs" showSearch label="关联科室" placeholder="请输入关联科室" request={()=>fetchDeptListByID()} fieldProps={{
          mode: "multiple"
        }}/>
    </>
  );
};

export default FormList;
