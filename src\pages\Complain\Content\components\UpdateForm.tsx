import { ModalForm, ProFormTextArea } from '@ant-design/pro-form';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

type FormValueType = Partial<COMPLAIN.ComplaintListItem>;

export type UpdateFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  updateModalVisible: boolean;
  values: FormValueType;
};

const UpdateForm: React.FC<UpdateFormProps> = ({ 
  updateModalVisible, 
  values, 
  onCancel, 
  onSubmit 
}) => {
  return (
    <ModalForm
      title="处理投诉"
      layout="horizontal"
      width={640}
      open={updateModalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={onSubmit}
      {...formItemLayout}
      initialValues={{ ...values }}
      className="_modal-wrapper"
    >
      <ProFormTextArea
        name="handleRemark"
        label="处理备注"
        placeholder="请输入处理备注"
        fieldProps={{
          rows: 4,
        }}
        rules={[
          {
            required: true,
            message: '请输入处理备注',
          },
        ]}
      />
    </ModalForm>
  );
};

export default UpdateForm;
