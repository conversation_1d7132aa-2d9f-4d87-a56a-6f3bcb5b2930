
import { useEffect, useRef , useState, forwardRef, useImperativeHandle} from 'react';
import './FlipNum.less';
import { usePrevious, useRequest} from 'ahooks';
import { getVisitsNum } from '@/services/api/analysis';

// 请求总人次数据
const queryVisitsNum = async () => {
  try {
    const response = await getVisitsNum({});
    if (response.code === '0') {
      return response.data;
    }
    return false;
  } catch (error) {
    return false;
  }
};

const FlipNumber = forwardRef((_, ref) => {
  const flipRef = useRef(null);
  const [frontTextData, setFrontTextData] = useState('0');
  const [backTextData, setBackTextData] = useState('1');
  const [flipType, setFlipType] = useState('down');
  const [isGo, setIsGo] = useState(false)
 const _flip = (type: string, front: string, back: string) => {
    // 如果处于翻转中，则不执行
    if (isGo) {
      return false
    }
    setFrontTextData(front)
    setBackTextData(back)
    // 根据传递过来的type设置翻转方向
    setFlipType(type)
    // 设置翻转状态为true
    setIsGo(true)
    setTimeout(() => {
      // 设置翻转状态为false
      setIsGo(false)
      setFrontTextData(back)
    }, 1000)
  }
  // 下翻牌
  const flipDown = (front, back) => {
    _flip('down', front, back)
  }

  // 上翻牌
  const flipUp = (front, back) => {
    _flip('up', front, back)
  }

  useImperativeHandle(ref, ()=> ({
    flipDown,
    flipUp,
    setFront: (front) => { setFrontTextData(front) }
  }))

  return <div className={`flip ${flipType} ${isGo?'go': ''}`} ref={flipRef}>
    <div className={`digital front number${frontTextData}`} />
    <div className={`digital back number${backTextData}`}/>
  </div>
});

export default () => {
  const { data } = useRequest(queryVisitsNum, {
    refreshDeps: [],
    pollingInterval: 10000,
    pollingWhenHidden: true
  });

  const prevNumber = usePrevious(data);
  const flipperNum = useRef({})

  const init = () => {
    const nowTimeStr = prevNumber?.zmzrc.toString().padStart(6, '0');
    
    for (let i = 0; i < 6; i++) {
      flipperNum.current[i].setFront(nowTimeStr[i]);
    }
  }

  const run = () => {
    const nowTimeStr = prevNumber?.zmzrc.toString().padStart(6, '0') || '000000';
    const nextTimeStr = data?.zmzrc.toString().padStart(6, '0') || '000000';
    for (let i = 0; i < 6; i++) {
      if (nowTimeStr[i] === nextTimeStr[i]) {
        continue;
      }
      flipperNum.current[i].flipDown(nowTimeStr[i], nextTimeStr[i]);
    }
  }

  useEffect(()=>{
    if(data?.zmzrc){
      // init()
      run()
    }
  }, [data])

  return <div>
    <div className="numBox">
      <div className="FlipNum">
        <FlipNumber ref={r => { flipperNum.current[0] = r; }}/>
        <FlipNumber ref={r => { flipperNum.current[1] = r; }}/>
        <FlipNumber ref={r => { flipperNum.current[2] = r; }}/>
        <FlipNumber ref={r => { flipperNum.current[3] = r; }}/>
        <FlipNumber ref={r => { flipperNum.current[4] = r; }}/>
        <FlipNumber ref={r => { flipperNum.current[5] = r; }}/>
        <div className='numBoxText'>人次</div>
      </div>                 
    </div>
    <div className="stateNum">
        <div className="stateItem">
            <div className="stateTitle">已就诊人次</div>
            <div className="stateContent green">{data?.yjzrc || 0}</div>
        </div>
        <div className="stateItem">
            <div className="stateTitle">候诊人次</div>
            <div className="stateContent orange">{data?.hzrc || 0}</div>
        </div>
        <div className="stateItem">
            <div className="stateTitle">过号人次</div>
            <div className="stateContent blue">{data?.ghrc || 0}</div>
        </div>
    </div>
  </div>
}