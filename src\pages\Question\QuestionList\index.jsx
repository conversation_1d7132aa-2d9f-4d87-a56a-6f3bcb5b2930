/*
	问卷列表
*/
import React, { useState, useRef, useEffect } from 'react';
import mockData from './mock';
import ProTable from '@ant-design/pro-table';
import PreveButton from "./components/PreveButton";
import { ExclamationCircleOutlined, PlusOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { message, Dropdown, Menu, Button, Select, Modal } from 'antd';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { connect, history } from 'umi';
import { queryQuestionnaireList, publishQuestionnaire, deleteQuestionnaire, queryListById } from '@/services/question';

const { Option } = Select;
const { confirm } = Modal;

/**
 * 查询问卷
 * @param fields
 */
const selectById = async fields => {
	const hide = message.loading('正在查询');
	try {
		const response = await queryListById({ ...fields });
		if (response.code === '0') {
			hide();
			message.success('查询成功');
			return response;
		}
		message.error(response.msg);
		return false;
	} catch (error) {
		hide();
		message.error('查询失败请重试！');
		return false;
	}
};

/**
 * 发布问卷
 * @param fields
 */
const publish = async fields => {
	const hide = message.loading('正在发布');

	try {
		const response = await publishQuestionnaire({ ...fields });
		if (response.code === '0') {
			hide();
			message.success('发布成功');
			return true;
		}
		message.error(response.msg);
		return false;
	} catch (error) {
		hide();
		message.error('发布失败请重试！');
		return false;
	}
};

/**
 * 取消发布
 * @param fields
 */
const unPublish = async fields => {
	const hide = message.loading('正在取消发布');

	try {
		const response = await publishQuestionnaire({ ...fields });
		if (response.code === '0') {
			hide();
			message.success('取消发布成功');
			return true;
		}
		message.error(response.msg);
		return false;
	} catch (error) {
		hide();
		message.error('取消发布失败请重试！');
		return false;
	}
};

/**
 * 删除
 * @param fields
 */
const remove = async fields => {
	const hide = message.loading('正在删除');
	try {
		const response = await deleteQuestionnaire({ ...fields });
		if (response.code === '0') {
			hide();
			message.success('删除成功');
			return true;
		}
		message.error(response.msg);
		return false;
	} catch (error) {
		hide();
		message.error('删除失败请重试！');
		return false;
	}
};

const requiredType = {0: false, 1: true, 2: false};   //required 转换 checked

const defaultQuestionnaire = {
	title: '此处添加问卷标题',
	date: '',
	stage: "未发布",
	questions: [],
	remark: '此处添加问卷描述',
	titleEditable: false,
	remarkEditable: false,
	addAreaVisible: false
};

const Question = (props) => {

	const {
		dispatch,
		question: { list },
		userManager: { hosList }
	} = props;

	// const [dataSource, setDataSoutce] = useState(list)
	const actionRef = useRef();
	// localStorage.removeItem("editing")

	// useEffect(() => {
	// 	dispatch({
	// 		type: 'userManager/fetchHos',
	// 		payload: {
	// 			current: 1,
	// 			pageSize: 999,
	// 		},
	// 	});
	// }, []);

	const refushList = () => {
		if (actionRef.current) {
			actionRef.current.reload();
		}
	}

	const setEditingAction = (setEditing, type, record, arr) => {
		dispatch({
			type: 'question/setEditingDetails',
			payload: { ...setEditing }
		})
		dispatch({
			type: 'question/setDetailTypeType',
			payload: type
		})
		dispatch({
			type: 'question/setQuestionnaireValues',
			payload: record || null
		})
		dispatch({
			type: 'question/setQuestionLists',
			payload: arr || null
		})
	}

	const setAddEditing = (data) => {
		const setEditing = Object.assign({}, { ...data, detailType: "add" })
		setEditingAction(setEditing, "add");
		history.replace('/question/list/detail');
	}

	// 新建问卷
	const handleAdd = () => {
		const setEditing = Object.assign({}, { ...defaultQuestionnaire, detailType: "add" })
		setEditingAction(setEditing, "add");
		history.replace('/question/list/detail');
	}

	// 新建问卷
	const handleAdd2 = () => {
		const editing = sessionStorage.getItem("editing");
		if(!editing || JSON.stringify(editing)==="{}"){
			setAddEditing(defaultQuestionnaire);
			return;
		}
		confirm({
			title: '提示',
			icon: <ExclamationCircleOutlined />,
			content: '之前有未提交的内容，需要填充吗?',
			onOk() {	
				setAddEditing(JSON.parse(editing));
			},
			onCancel() {
				setAddEditing(defaultQuestionnaire);
			},
		});
	}

	// 查询问卷问题列表，处理编辑表单和填写问卷表单数据
	const querySelectById = async (record, detailType)=>{
		if (!record.id) {
			return ;
		}
		const res = await selectById({ questionnaireId: record.id })
		if(!res) return;
		const setEditing = {
			title: record.title,
			date: '',
			id: record.id,
			stage: record.state,
			questions: [],
			questionType: record.questionType,
			state: record.state,
			remark: record.describe,
			titleEditable: false,
			remarkEditable: false,
			addAreaVisible: false,
		}
		if(res.data){
			// 问卷编辑界面数据转换
			res.data.length > 0 && res.data.forEach((item, index) => {
				let questionoptionsList = [];
				if(item.type===2 || item.type===3){ //单选多选
					item.questionoptionsList.forEach((el)=>{
						questionoptionsList.push({
							...el,
							text: el.title,
						})
					})
				}
				setEditing.questions.push({
					...item,
					type: item.type,
					title: item.title,
					options: questionoptionsList,
					value: '',
					// id: item.id,
					required: requiredType[item.required]
				})
			})
		}
		setEditingAction(setEditing, detailType, record, res.data)
	}
	
	// 编辑问卷
	const handleEdit = async (record) => {
		await querySelectById(record, "update");
		history.replace(`/question/list/detail?id=${record.id}`)
	}

	// 填写问卷
	const handleFill = async (index, record) => {
		await querySelectById(record, "fill");
		history.replace(`/question/fill?id=${record.id}`)
	}

	const handlePreview = (index) => {
		console.log('预览数据')

		history.replace('/question/result')
	}

	const saveQuestionLists = (list) => {
		dispatch({
			type: 'question/saveQustionLists',
			payload: [...list]
		})
	}

	// 发布
	const handlePublish = (record) => {
		Modal.confirm({
			title: `确认发布`,
			icon: <InfoCircleOutlined />,
			content: `是否确认发布问卷“${record.title}”?`,
			okText: '确认',
			cancelText: '取消',
			onOk: async () => {
				const success = await publish({ id: record.id, state: 2 });
				if (success) {
					refushList();
				}
			}
		})

	}

	// 取消发布
	const handleUnPublish = (record) => {
		Modal.confirm({
			title: `确认取消发布`,
			icon: <InfoCircleOutlined />,
			content: `是否确认取消发布问卷“${record.title}”?`,
			okText: '确认',
			cancelText: '取消',
			onOk: async () => {
				const success = await unPublish({ id: record.id, state: 1 });
				if (success) {
					refushList();
				}
			}
		})
	}


	// 删除问卷
	const handleRemove = async (record) => {
		Modal.confirm({
			title: '确认删除',
			icon: <InfoCircleOutlined />,
			content: `是否确认删除问卷“${record.title}”?`,
			okText: '确认',
			cancelText: '取消',
			onOk: async () => {
				const success = remove({ id: record.id, state: 9 });
				if (success) {
					refushList();
				}
			}
		})
	}

	const columns = [
		{
			title: '院区名称',
			dataIndex: 'said',
			hideInSearch: true,
			hideInTable: true,
			width: '20%',
			renderText: (said, record) => {
				return record.hospitalname
			},
			renderFormItem: (item, { defaultRender, ...rest }) => {
				return (
					<Select {...rest} placeholder="请选择">
						{
							hosList && hosList.map((c) => {
								return (<Option value={c.key} key={`dept${c.key}`}>{c.hospitalName}</Option>)
							})
						}
					</Select>
				)
			}
		},
		{
			title: '问卷标题',
			dataIndex: 'title',
			key: 'title',
			width: '20%',
		},
		{
			title: '问卷类型',
			dataIndex: 'questionType',
			key: 'questionType',
			valueEnum: {
				'1': {
				  	text: '门诊问卷',
				},
				'2': {
					text: '住院问卷',
				},
			},
		},
		{
			title: '问卷描述',
			dataIndex: 'describe',
			key: 'describe',
			hideInSearch: true,
			width: '20%',
		},
		{
			title: '创建时间',
			dataIndex: 'createtime',
			key: 'createtime',
			valueType: 'dateTime',
			hideInSearch: true,
		},
		{
			title: '发布时间',
			dataIndex: 'publishtime',
			key: 'publishtime',
			valueType: 'dateTime',
			hideInSearch: true,
		},
		{
			title: '发布状态',
			dataIndex: 'state',
			width: '10%',
			key: 'state',
			hideInSearch: true,
			valueEnum: {
				0: {
					text: '失效',
				},
				1: {
					text: '未发布',
				},
				2: {
					text: '已发布',
				},
			},
		},
		{
			title: '操作',
			dataIndex: 'option',
			hideInForm: true,
			hideInSearch: true,
			render: (_, record, index) => {
				return (
					<span>
						<PreveButton 
							record={record} 
							index={index} 
							handleEdit={handleEdit}
							handlePublish={handlePublish}
							handleUnPublish={handleUnPublish}
							handleRemove={handleRemove}
							handlePreview={handlePreview}
							handleFill={handleFill}
						/>
					</span>
				)
			}
		}
	];

	return (
		<PageHeaderWrapper>
			<ProTable
				headerTitle="问卷列表"
				actionRef={actionRef}
				rowKey="id"
				pagination={{ pageSize: 10 }}
				// toolBarRender={(action, { selectedRows }) => [
				// 	<Button type="primary" onClick={() => handleAdd()}>
				// 		<PlusOutlined /> 创建问卷
				// 	</Button>
				// ]}
				tableAlertRender={false}
				// dataSource={mockData}
				columns={columns}
				request={async (params) => {
					const msg = await queryQuestionnaireList(params);
					saveQuestionLists(msg.data ? msg.data : []); //存储list到question model
					return {
						data: msg.data,
						msg: msg.msg,
						total: msg.total,
						// // success 请返回 true，
						// // 不然 table 会停止解析数据，即使有数据
						// success: boolean,
						// // 不传会使用 data 的长度，如果是分页一定要传
					}
				}}
			/>
		</PageHeaderWrapper>
	);
};

export default connect(({ userManager, question }) => ({
	userManager,
	question
}))(Question);
