declare namespace PAYS {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 支付日志详情
    type PayListItem = {
        payID?: number; // ID
        saID?: string;  // 机构ID
        hospitalName?: string; // 医院名称
        patientID?: string;    // 患者ID
        patientName?: string;  // 患者姓名
        businessID?: string;   // 业务单据号
        productID?: string;   // 平台订单号
        outTradeNo?: string;  // 商户订单号
        subject?: string;  // 订单标题
        invoiceNO?: string; // 发票号码
        totalAmount?: number; // 总金额
        privateAmount?: number; // 自费金额
        medicalAmount?: number; // 医保金额
        medicalPrivateAmount?: number; // 医保个账金额
        payBusinessType?: number; // 支付类型 1门诊支付2挂号支付3市民卡充值4住院预缴5医保本购买6出院结算7就诊卡充值
        payType?: number; //支付方式 1微信2支付宝3现金4银联5医保6支付宝刷脸7支付宝小程序8农行聚合支付9银联聚合支付（云闪付）10微信刷脸11黔农云支付 12市民卡充值（台州）  17康邻聚合支付19建行龙支付20 市民卡扣款（台州肿瘤）21支付宝代扣（台州肿瘤）36 数字人民币  37建行聚合码
        sysType?: number; // 系统类型
        sysTypeName?: string;  // 系统类型名称
        state?: number | string;    // 状态
        payTime?: string;  // 支付时间
        createTime?: string; // 创建时间
        notifyTime?: string; // 通知时间
        remark?: string; // 备注
        refundTime?: string;  // 退款时间
        refundRemark?: string; // 退款备注
        deviceCode?: string;  // 设备号
        cardType?: string;   // 登录介质
        hisState?: string;   // HIS状态
        hisRemark?: string;  // HIS结算备注
        hisDeviceCode?: string; // HIS设备编号
        spbillCreateIp?: string; // 支付终端IP
        creditsStr?: string; // 患者签名字段
    };
    // 设备列表
    type PayList = {
        /** 列表的内容 **/
        data?: PayListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}