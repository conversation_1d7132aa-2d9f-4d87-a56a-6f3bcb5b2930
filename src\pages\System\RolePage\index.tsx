import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON>, Modal, Drawer, Select} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryRoleList, updateRole, addRole, updateRolePer} from '@/services/api/system';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import MenuForm from './components/MenuForm';
import { handleOperateMethod } from '@/utils/index';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加角色
 * @param fields
 */
const handleAdd = (fields: ROLE.RoleListItem) => handleOperateMethod(addRole, fields, 'add');
/**
 * @zh-CN 修改角色
 * @param fields
 */
const handleUpdate = (fields: ROLE.RoleListItem) => handleOperateMethod(updateRole, fields, 'update');

/**
 * @zh-CN 修改菜单
 * @param fields
 */
const handleUpdatePer = (fields: ROLE.RoleListItem) => handleOperateMethod(updateRolePer, fields, 'update');

/**
 * @zh-CN 删除角色
 * @param deviceID
 */
const handleRemove = (roleID?: number) =>
  handleOperateMethod(updateRole, { roleID, state: 9 }, 'delete');

const TableList = () => {
  /** 新增角色的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改角色的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示角色详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  /** 修改角色菜单的弹窗 */
  const [menuModalVisible, handleMenuModalVisible] = useState(false);

  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<ROLE.RoleListItem>();
  const { dicList, fetchDicList } = useModel('dictionary');
  const { menuList, fetchMenuList, roleMenuList, fetchRoleMenuList} = useModel('system');

  useEffect(() => {
    fetchDicList('SysType');
  }, []);

  const columns: ProColumns<ROLE.RoleListItem>[]  = [
    {
      title: '角色名称',
      dataIndex: 'roleName',
      key: 'roleName',
    },
    {
      title: '设备类型',
      dataIndex: 'sysType',
      width: 150,
      renderText: (_, record) => {
        return record.sysTypeName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select {...rest} placeholder="请选择">
            {dicList &&
              dicList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`sysType${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 300,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.roleName}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.roleID);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
        <a key="menu" onClick={() => {
          fetchMenuList(record.sysType)
          fetchRoleMenuList(record.roleID)
          handleMenuModalVisible(true)
          setCurrentRow(record);
        }}>
          菜单配置
        </a>
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        request={queryRoleList}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value);
            const success = await handleAdd(value);
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, roleID: currentRow?.roleID });

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      {menuModalVisible && menuList && roleMenuList &&
        <MenuForm
          onSubmit={async values => {
            const success = await handleUpdatePer(values);
            if(success) {
              handleMenuModalVisible(false);
              setCurrentRow(undefined);

              if(actionRef.current) {
                actionRef.current?.reload();
              }
            }
          }}
          onCancel={() => {
            handleMenuModalVisible(false);
          }}
          menuModalVisible = {menuModalVisible}
          values= {currentRow || {}}
          selectOption={{menuList, roleMenuList}}
        />
      }
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.roleName && (
          <ProDescriptions
            column={1}
            title={currentRow?.roleName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.roleID,
            }}
            columns={columns as ProDescriptionsItemProps<ROLE.RoleListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
