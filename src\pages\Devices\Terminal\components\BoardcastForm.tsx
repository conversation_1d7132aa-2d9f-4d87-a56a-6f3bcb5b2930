import { ModalForm, ProFormTextArea, ProFormSelect} from '@ant-design/pro-form';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
}

type FormValueType = Partial<DEVICES.CmdTypeItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
};

const BoardcastForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, onSubmit }) => {

  return (
    <ModalForm
      title="设备广播"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={(values) => {
        const data = {
          cmdContent: values.cmdContent,  // 命令类型
          cmdSrc: "",
          optType: values.optType  // 操作类型 5 下发文字广播 8 语音广播 
        }
        return onSubmit(data)
      }}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      <ProFormSelect
        name="optType"
        label="广播类型"
        initialValue={'5'}
        rules={[{ required: true, message: '广播类型为必填项' }]}
        valueEnum={{
          5: '文字广播',
          8: '语音广播',
        }}
      />
      <ProFormTextArea name="cmdContent" rules={[{ required: true, message: '广播内容为必填项' }]} placeholder="请输入下发广播内容" label="广播内容" /> 
    </ModalForm>
  );
};

export default BoardcastForm;