declare namespace MONITOR {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 数据详情
    type DataListItem = {
        key?: string, // 
        name?: string, // 
        idCard?: string,  // 
        deviceCode?: string, // 
        verificationTime?: string, // 
        businessType?: string, // 
        businessTypeName?: string, // 
        scenario?: string,  // 患者年龄
        scenarioName?: string, // 医保机构位置
        readFaceData?: string,  // 核验时间
        brushFaceIDCard?: number, // 总金额
        brushFaceName?: number,  // 自费金额
        state?: string,  // 状态
    };
    // 数据列表
    type DataList = {
        /** 列表的内容 **/
        data?: DataListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}