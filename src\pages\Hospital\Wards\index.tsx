import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON>, Modal, Drawer, Select} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryDeptList, updateDeptInfo, addDeptInfo } from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { handleOperateMethod } from '@/utils/index';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加科室
 * @param fields
 */
const handleAdd = (fields: DEPT.DeptListItem) => handleOperateMethod(addDeptInfo, fields, 'add');
/**
 * @zh-CN 修改科室
 * @param fields
 */
const handleUpdate = (fields: DEPT.DeptListItem) => handleOperateMethod(updateDeptInfo, fields, 'update');
/**
 * @zh-CN 删除科室
 * @param deviceID
 */
const handleRemove = (deptID?: number) =>
  handleOperateMethod(updateDeptInfo, { deptID, state: 9 }, 'delete');

const TableList = () => {
  /** 新增科室的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改科室的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示科室详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<DEPT.DeptListItem>();
  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);

  const columns: ProColumns<DEPT.DeptListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select {...rest} placeholder="请选择">
            {hosList &&
              hosList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '科室名称',
      dataIndex: 'deptName',
      key: 'deptName',
    },
    {
      title: '科室代码',
      dataIndex: 'deptCode',
      key: 'deptCode',
    },
    {
      title: '科室排队预估时间',
      dataIndex: 'estimatedTime',
      key: 'estimatedTime',
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 300,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.deptName}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.deptID);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        request={queryDeptList}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value);
            const success = await handleAdd(value);
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, deptID: currentRow?.deptID });

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.deptName && (
          <ProDescriptions
            column={1}
            title={currentRow?.deptName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.deptID,
            }}
            columns={columns as ProDescriptionsItemProps<DEPT.DeptListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
