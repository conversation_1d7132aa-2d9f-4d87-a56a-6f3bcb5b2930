import { useEffect, useRef, memo } from 'react';
import * as echarts from 'echarts'
// 引入柱状图
import 'echarts/lib/chart/bar';
// 引入提示框和标题组件
import 'echarts/lib/component/tooltip';
import styles from '../index.less';
import { getDeptVisitNum } from '@/services/api/analysis';
import { useRequest } from 'ahooks';

// 请求科室数据
const queryDeptVisitNum = async () => {
    try {
      const response = await getDeptVisitNum({});

      if (response.code === '0') {
        return response.data;
      }
      return false;
    } catch (error) {
      return false;
    }
};

const EchartsTest = memo(() => {
    const ref = useRef<any>();
    const chartRef = useRef<any>();
    const clearTime = useRef<any>();

    const { data } = useRequest(queryDeptVisitNum, {
        refreshDeps: [],
        pollingInterval: 50000,
        pollingWhenHidden: true
    });

    const handleResize = () => {
        chartRef.current.resize();
    }
    
    useEffect(() => {
        if(data?.length > 0){
            chartRef.current = echarts.init(ref.current);
            const series: number[] = [];
            const citys = data?.map((v: any) => v.deptName);
            const gzNumArr = data?.map((v: any) => v.jrjzrc);
            const zcNumArr = data?.map((v: any) => v.zrjzrc);
            const dataGroup = [gzNumArr, zcNumArr];
            const colorList = [ '#4083E2', '#68BBC4']
            const parttValueNames = ['今日', '昨日']
            parttValueNames.forEach((item, index) => {
                const obj: any = {
                    name: item,
                    type: 'bar',
                    barGap: 0,
                    barWidth: '20%',
                    data: dataGroup[index],
                    color: colorList[index],
                    label: { //柱状图显示数字
                        show: true,
                        position: 'right',
                        color: '#fff',
                        fontSize: 12,
                        formatter: (v: any) => { // 设置圆饼图中间文字排版
                            if (v.value == 0) {
                                return ''
                            } else {
                                return `${v.value}`
                            }
                        }
                    },
                }
                series.push(obj)
            });
            // 绘制图表
            const option = {
                itemStyle: {
                    normal: {
                        label: {
                            formatter: "{c}" + "人",
                            show: true,
                            position: "top",
                            textStyle: {
                                fontWeight: "bolder",
                                fontSize: "12",
                                color: "#fff"
                            }
                        },
                        opacity: 1
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    orient: 'horizontal',
                    icon: 'rect',//图例样式
                    top: '4%',
                    itemHeight: 14, //图例图标的高度
                    itemWidth: 14, //图例图标的宽度
                    itemGap: 20, //图例图标间的间距
                    textStyle: {
                        color: '#B1B2BE', //图例文字颜色
                        fontSize: 12, //图例文字字体大小
                        // fontWeight: 600
                    },
                },
                grid: {
                    top: '10%',
                    left: '3%',
                    right: '3%',
                    bottom: '6%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.1],
                    // boundaryGap: false,
                    axisLine: { //x轴线的颜色以及宽度
                        show: false,
                        lineStyle: {
                            color: "#A4A0DE",
                            width: 1,
                            type: "solid"
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: { //y轴文字的配置
                        show: true,
                        // rotate: 50, //设置倾斜角度，数值 可设置 正负 两种，
                        textStyle: {
                            color: "#A4A0DE",
                        }
                    },
                    splitLine: { //分割线配置
                        show: true,
                        lineStyle: {
                            color: "#232252",
                        }
                    },
                },
                yAxis: {
                    type: 'category',
                    inverse: true, //顺序
                    axisLine: { //y轴线的颜色以及宽度
                        show: false,
                        lineStyle: {
                            color: "#A4A0DE",
                            width: 1,
                            type: "solid"
                        },
                    },
                    splitLine: { //分割线配置
                        show: false,
                        lineStyle: {
                            color: "#A4A0DE",
                        }
                    },
                    axisTick: {
                        show: false,
                    },
                    // boundaryGap: false, //留白
                    axisLabel: { //y轴文字的配置
                        show: true,
                        textStyle: {
                            color: "#A4A0DE",
                        }
                    },
                    data: citys
                },
                // 自动滚动
                series
            };
            chartRef.current.setOption(option);
            chartRef.current.dispatchAction({
                type: 'showTip',
                seriesIndex: 0,
                dataIndex: 0
            });
            let index = 0;
            clearTime.current = setInterval(() => {
                ++index;
                if (index == citys.length) {
                    index = 0;
                }
                chartRef.current.dispatchAction({
                    type: 'showTip',
                    seriesIndex: 0,
                    dataIndex: index
                });
            }, 5000);
            window.addEventListener('resize', handleResize);
        }
        return () => {
            clearInterval(clearTime.current);
            window.removeEventListener('resize', handleResize)
        }
    }, [data])

    return (
        <div className={styles.main1} id="main1" ref={ref}></div>
    );
});

export default EchartsTest;

