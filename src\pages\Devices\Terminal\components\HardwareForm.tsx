import { ModalForm } from '@ant-design/pro-form';
import { Table, Tag} from 'antd';
import type { ColumnsType } from 'antd/es/table';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

type HardwareFormProps = {
  modalVisible: boolean;
  onCancel: (flag?: boolean) => void;
  data: DEVICES.HardwareItem[];
};

const statusColor = ['error', 'success']
const statusName = ['异常', '正常']

const HardwareForm: React.FC<HardwareFormProps> = ({ modalVisible, onCancel, data }) => {

  const columns: ColumnsType<DEVICES.HardwareItem> =  [
    {
      title: '#',
      dataIndex: 'index',
      width: 80,    
      align: 'center',
      render: (text, record, index) => {
        return <span>{index + 1}</span>;
      },
    },
    {
      title: '硬件名称',
      dataIndex: 'content',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'state',
      // render: (state: any, record) => (
      //   <span>
      //     <Tag color={statusColor[state]} key={state}>
      //       {statusName[state]}
      //     </Tag>
      //     {state === 0 ? <span style={{ color: '#ff0000' }}>{record.errorRemark}</span> : ''}
      //   </span>
      // ),
    },
  ];

  return (
    <ModalForm
      title="硬件状态"
      layout="horizontal"
      width={980}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
        footer: null
      }}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      <Table dataSource={data} columns={columns} pagination={false} />
    </ModalForm>
  );
};

export default HardwareForm;
