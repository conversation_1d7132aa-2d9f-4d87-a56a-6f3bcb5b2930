import React,  { useState, useEffect} from 'react';
import { Modal, Transfer, Tree } from 'antd';
import type { TransferDirection, TransferItem } from 'antd/es/transfer';
import type { DataNode } from 'antd/es/tree';

interface TreeTransferProps {
    dataSource: DataNode[];
    targetKeys: string[];
    onChange: (targetKeys: string[], direction: TransferDirection, moveKeys: string[]) => void;
}


// Customize Table Transfer
const isChecked = (selectedKeys: (string | number)[], eventKey: string | number) => selectedKeys.includes(eventKey);
  
const generateTree = (treeNodes: DataNode[] = [], checkedKeys: string[] = []): DataNode[] =>
    treeNodes.map(({ children, ...props }) => ({
    ...props,
    disabled: checkedKeys.includes(props.key as string),
    children: generateTree(children, checkedKeys),
}));
  
const TreeTransfer = ({ dataSource, targetKeys, ...restProps }: TreeTransferProps) => {
    const transferDataSource: TransferItem[] = [];
    function flatten(list: DataNode[] = []) {
        list.forEach((item) => {
            transferDataSource.push(item as TransferItem);
            flatten(item.children);
        });
    }
    flatten(dataSource);

    console.log(dataSource)

    return (
        <Transfer
            {...restProps}
            titles={['系统功能列表', '角色配置功能']}
            targetKeys={targetKeys}
            dataSource={transferDataSource}
            className="tree-transfer"
            render={item => item.title}
            showSelectAll={true}
        >
            {({ direction, onItemSelect, selectedKeys }) => {
                if (direction === 'left') {
                    console.log(targetKeys)
                    const checkedKeys = [ ...selectedKeys, ...targetKeys];
                    // console.log(checkedKeys)
                    return (
                        <Tree
                            blockNode
                            checkable
                            checkStrictly
                            defaultExpandAll
                            checkedKeys={checkedKeys}
                            treeData={generateTree(dataSource, targetKeys)}
                            onCheck={(_, { node: { key } }) => {
                                onItemSelect(key as string, !isChecked(checkedKeys, key));
                            }}
                            onSelect={(_, { node: { key } }) => {
                                onItemSelect(key as string, !isChecked(checkedKeys, key));
                            }}
                        />
                    );
                }
            }}
        </Transfer>
    );
};

type FormValueType = Partial<ROLE.RoleListItem>;

export type MenuFormProps = {
    onCancel: (flag?: boolean, formVals?: FormValueType) => void;
    onSubmit: (values: FormValueType) => Promise<void>;
    menuModalVisible: boolean;
    values: FormValueType;
    selectOption: {
        menuList: Array<ROLE.FunctionListItem>,
        roleMenuList: Array<ROLE.FunctionListItem>
    }
};

const MenuForm: React.FC<MenuFormProps> = props => {

    const { menuModalVisible, onCancel, selectOption: {
        menuList,
        roleMenuList
    }, onSubmit: handleUpdate, values} = props;


    const format_roleMenuList = roleMenuList && roleMenuList.map((item) => item.perCode );
    const [ keys, setKeys ] = useState<string[]>(format_roleMenuList);

    useEffect(()=>{
        setKeys(format_roleMenuList);
     }, [menuList]);

    // 去除新增的key 
    const diff = (a: string[], b: string[]) => { 
        for(var i=0; i< b.length; i++) { 
            for(var j=0; j<a.length; j++) { 
                if(a[j]==b[i]){ 
                    a.splice(j,1); 
                    j=j-1; 
                } 
            } 
        } 
        return a;
    }

    const onChange = (  nextTargetKeys: string[], direction: string, moveKeys: string[] ) => {
        // 这里有特殊处理 Transfer的 TargetKeys中新增的key在最前面
        // 处理后新增的往后排
        console.log(nextTargetKeys)
        if(direction === 'right'){
            const array = diff(nextTargetKeys, moveKeys)
            setKeys([...array, ...moveKeys])  // 后选的在后面
        }else {
            setKeys(nextTargetKeys)
        }
    }

    return (
      <Modal
        destroyOnClose
        title="菜单配置"
        open={menuModalVisible}
        onOk={()=> {handleUpdate({perCode: keys.join(','), roleID: values.roleID})}}
        onCancel={() => onCancel()}
        width={750}
      >
        {/* 1:windows 2:android */}
        {/* <TreeTransfer dataSource={values.sysType === '1' || values.sysType === '2'  ? format_menuList : systemMenu} targetKeys={keys} onChange={setKeys} /> */}
        <TreeTransfer dataSource={menuList} targetKeys={keys} onChange={onChange} />
      </Modal>
    );
};

export default MenuForm;