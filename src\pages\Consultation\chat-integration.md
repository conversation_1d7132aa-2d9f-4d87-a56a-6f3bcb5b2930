# 问诊记录与聊天功能集成

## 功能概述

在问诊记录界面的"图文聊天"按钮上增加了创建聊天会话并跳转聊天页面的功能，实现了问诊记录与聊天系统的无缝集成。

## 主要功能

### 1. 创建聊天会话
- 点击"图文聊天"按钮时自动创建或获取聊天会话
- 使用当前用户ID和患者设备号作为会话参数
- 支持会话的创建和复用

### 2. 自动跳转
- 创建会话成功后自动跳转到聊天页面
- 传递会话信息和患者信息到聊天页面
- 在聊天页面自动选择对应的会话

### 3. 患者信息传递
- 传递患者基本信息（ID、姓名、发票号、医院名称）
- 在聊天页面显示相关的患者信息
- 提供更好的上下文信息

## 技术实现

### 1. 问诊记录页面修改

#### 导入依赖
```typescript
import { useModel, history } from '@umijs/max';
import { createOrGetConversation } from '@/services/api/chat';
```

#### 创建聊天会话函数
```typescript
const handleCreateChatAndNavigate = async (record: CONSULTATION.ItemListItem) => {
  if (!record.deviceCode) {
    message.error('该记录未绑定设备号，无法创建聊天会话');
    return;
  }

  try {
    // 获取当前用户ID
    const currentUserId = initialState?.currentUser?.userName || initialState?.currentUser?.deviceSn;
    if (!currentUserId) {
      message.error('用户信息获取失败，请重新登录');
      return;
    }

    // 创建或获取会话
    const response = await createOrGetConversation({
      user1Id: currentUserId,
      user2Id: record.deviceCode,
    });

    if (response.code === '0') {
      message.success('聊天会话创建成功');
      
      // 跳转到聊天页面，并传递会话信息
      history.push('/chat', {
        conversationId: response.data.conversationId,
        receiverId: record.deviceCode,
        receiverName: record.patientName || '患者',
        patientInfo: {
          patientID: record.patientID,
          patientName: record.patientName,
          invoice: record.invoice,
          hospitalName: record.hospitalName,
        }
      });
    } else {
      message.error(response.msg || '创建聊天会话失败');
    }
  } catch (error) {
    console.error('创建聊天会话失败:', error);
    message.error('创建聊天会话失败，请稍后重试');
  }
};
```

#### 修改按钮点击事件
```typescript
<a 
  key="content"
  onClick={async () => {
    await handleCreateChatAndNavigate(record);
  }}
>
  图文聊天
</a>
```

### 2. 聊天页面修改

#### 导入依赖
```typescript
import { useModel, useLocation } from '@umijs/max';
```

#### 获取传递的参数
```typescript
const location = useLocation();

// 获取从问诊记录页面传递过来的参数
const locationState = location.state as {
  conversationId?: string;
  receiverId?: string;
  receiverName?: string;
  patientInfo?: {
    patientID?: string;
    patientName?: string;
    invoice?: string;
    hospitalName?: string;
  };
} | undefined;
```

#### 自动选择会话
```typescript
// 处理从问诊记录页面传递过来的参数，自动选择对应的会话
useEffect(() => {
  if (locationState?.conversationId && conversations.length > 0) {
    // 查找对应的会话
    const targetConversation = conversations.find(
      conv => conv.conversationId === locationState.conversationId
    );
    
    if (targetConversation) {
      // 自动选择该会话
      handleSelectConversation(targetConversation);
      
      // 显示成功消息
      if (locationState.patientInfo?.patientName) {
        message.success(`已为患者 ${locationState.patientInfo.patientName} 创建聊天会话`);
      }
    } else {
      // 如果没有找到会话，可能是新创建的，等待会话列表更新
      console.log('等待会话列表更新...');
    }
  }
}, [conversations, locationState, handleSelectConversation]);
```

## 用户体验流程

### 1. 在问诊记录页面
1. 用户查看问诊记录列表
2. 点击某条记录的"图文聊天"按钮
3. 系统验证设备号是否存在
4. 调用API创建或获取聊天会话
5. 显示成功提示信息

### 2. 跳转到聊天页面
1. 自动跳转到聊天页面
2. 传递会话ID和患者信息
3. 聊天页面加载会话列表
4. 自动选择对应的会话
5. 显示患者聊天会话创建成功的提示

### 3. 开始聊天
1. 聊天窗口自动打开
2. 显示患者姓名作为聊天对象
3. 可以立即开始发送消息
4. 支持文字和图片消息

## 错误处理

### 1. 设备号验证
```typescript
if (!record.deviceCode) {
  message.error('该记录未绑定设备号，无法创建聊天会话');
  return;
}
```

### 2. 用户信息验证
```typescript
const currentUserId = initialState?.currentUser?.userName || initialState?.currentUser?.deviceSn;
if (!currentUserId) {
  message.error('用户信息获取失败，请重新登录');
  return;
}
```

### 3. API调用错误
```typescript
if (response.code === '0') {
  // 成功处理
} else {
  message.error(response.msg || '创建聊天会话失败');
}
```

### 4. 网络异常处理
```typescript
try {
  // API调用
} catch (error) {
  console.error('创建聊天会话失败:', error);
  message.error('创建聊天会话失败，请稍后重试');
}
```

## 数据传递格式

### 跳转参数
```typescript
history.push('/chat', {
  conversationId: string,      // 会话ID
  receiverId: string,          // 接收者ID（患者设备号）
  receiverName: string,        // 接收者姓名
  patientInfo: {               // 患者信息
    patientID: string,         // 患者ID
    patientName: string,       // 患者姓名
    invoice: string,           // 发票号
    hospitalName: string,      // 医院名称
  }
});
```

## 测试建议

### 1. 基本功能测试
1. 选择有设备号的问诊记录，点击"图文聊天"
2. 验证是否成功创建会话并跳转
3. 检查聊天页面是否自动选择对应会话

### 2. 异常情况测试
1. 选择没有设备号的记录，验证错误提示
2. 网络异常时的错误处理
3. 重复点击同一记录的处理

### 3. 用户体验测试
1. 跳转速度和流畅性
2. 提示信息的准确性
3. 会话选择的正确性

## 相关文件

- `src/pages/Consultation/index.tsx` - 问诊记录页面
- `src/pages/Chat/index.tsx` - 聊天页面
- `src/services/api/chat.ts` - 聊天API接口

## 后续优化建议

1. **缓存优化**：缓存已创建的会话，避免重复创建
2. **加载状态**：添加创建会话时的loading状态
3. **会话信息**：在聊天窗口显示更多患者信息
4. **历史记录**：保存聊天跳转历史，支持返回
5. **权限控制**：添加聊天权限验证机制
