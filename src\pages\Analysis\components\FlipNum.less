.numBox{
    color: #fff;
    text-align: center;
    padding: 5px;
    .FlipNum{
        display: flex;
        justify-content: center;
        gap: 10px;
        justify-items: end;
        .numBoxText{
            font-size: 26px;
            vertical-align: bottom;
        }
    }
    .flip {
        display: inline-block;
        position: relative;
        width: 60px;
        height: 100px;
        line-height: 100px;
        border: 1px solid #63A1F7;
        border-radius: 10px;
        background: #fff;
        font-size: 66px;
        color: #fff;
        box-shadow: 0 0 6px rgba(99, 161, 247, .5);
        text-align: center;
        font-family: "Helvetica Neue";
        &.down{
            &.go{
                .front{
                    &::before{
                        transform-origin: 50% 100%;
                        animation: frontFlipDown 0.6s ease-in-out both;
                        box-shadow: 0 -2px 6px rgba(255, 255, 255, 0.3);
                        backface-visibility: hidden;
                    }
                }
                .back{
                    &::after{
                        animation: backFlipDown 0.6s ease-in-out both;
                    }
                }
            }
            .front{
                &::before{
                    z-index: 3;
                }
                &::after{
                    z-index: 1;
                }
            }
            .back{
                &::before{
                    z-index: 1;
                }
                &::after{
                    z-index: 2;
                    transform-origin: 50% 0%;
                    transform: perspective(160px) rotateX(180deg);
                }
            }
        }
        &.up{
            &.go{
                .front{
                    &::after{
                        transform-origin: 50% 0;
                        animation: frontFlipUp 0.6s ease-in-out both;
                        box-shadow: 02px6px rgba(255, 255, 255, 0.3);
                        backface-visibility: hidden;
                    }
                }
                .back{
                    &::before{
                        animation: backFlipUp 0.6s ease-in-out both;
                    }
                }
            }
            .front{
                &::before{
                    z-index: 1;
                }
                &::after{
                    z-index: 3;
                }
            }
            .back{
                &::before{
                    z-index: 2;
                    transform-origin: 50% 100%;
                    transform: perspective(160px) rotateX(-180deg);
                }
                &::after{
                    z-index: 1;
                }
            }
        }
        .digital{
            &::before, &::after{
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                background: #63A1F7;
                overflow: hidden;
                box-sizing: border-box;
            }
            &::before {
                top: 0;
                bottom: 50%;
                border-radius: 10px 10px 0 0;
                border-bottom: solid 1px rgba(46,98,165, 30%);
            }
            &::after {
                top: 50%;
                bottom: 0;
                border-radius: 0 0 10px 10px;
                line-height: 0;
            }
            &.number0{
                &::before, &::after{
                    content: "0";
                }
            }
            &.number1{
                &::before, &::after{
                    content: "1";
                }
            }
            &.number2{
                &::before, &::after{
                    content: "2";
                }
            }
            &.number3{
                &::before, &::after{
                    content: "3";
                }
            }
            &.number4{
                &::before, &::after{
                    content: "4";
                }
            }
            &.number5{
                &::before, &::after{
                    content: "5";
                }
            }
            &.number6{
                &::before, &::after{
                    content: "6";
                }
            }
            &.number7{
                &::before, &::after{
                    content: "7";
                }
            }
            &.number8{
                &::before, &::after{
                    content: "8";
                }
            }
            &.number9{
                &::before, &::after{
                    content: "9";
                }
            }
        }
    
        @keyframes frontFlipDown {
            0% {
                transform: perspective(160px) rotateX(0deg);
            }
            100% {
                transform: perspective(160px) rotateX(-180deg);
            }
        }
        @keyframes backFlipDown {
            0% {
                transform: perspective(160px) rotateX(180deg);
            }
            100% {
                transform: perspective(160px) rotateX(0deg);
            }
        }
    
        @keyframes frontFlipUp {
            0% {
                transform: perspective(160px) rotateX(0deg);
            }
            100% {
                transform: perspective(160px) rotateX(180deg);
            }
        }
        @keyframes backFlipUp {
            0% {
                transform: perspective(160px) rotateX(-180deg);
            }
            100% {
                transform: perspective(160px) rotateX(0deg);
            }
        }
    }
}
.numBoxText{
    font-size: 30px;
}
.stateNum{
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
    .stateItem{
        font-size: 25px;
        color: #fff;
        text-align: center;
    }
    .stateContent{
        font-size: 28px;
        color: #fff;
        width: 85px;
        height: 85px;
        line-height: 85px;
        margin: 3px auto 0;
        &.blue{
            background-image: url(../../../assets/home/<USER>
            background-size: 100% 100%;
        }
        &.green{
            background-image: url(../../../assets/home/<USER>
            background-size: 100% 100%;
        }
        &.orange{
            background-image: url(../../../assets/home/<USER>
            background-size: 100% 100%;
        }
    }
}

