
import { useEffect, useRef, memo } from 'react';
import * as echarts from 'echarts';
import { getPatientRegisterTypeInfo } from '@/services/api/analysis';
import { useRequest } from 'ahooks';
import styles from '../index.less';

// 请求患者类型
const queryPatientRegisterTypeInfo = async () => {
    try {
      const response = await getPatientRegisterTypeInfo({});

      if (response.code === '0') {
        return response.data;
      }
      return false;
    } catch (error) {
      return false;
    }
};


const EchartsTest = memo(() => {
    const chartRef = useRef<any>();
    const ref = useRef<any>();
    const handleResize = () => {
        chartRef.current.resize();
    }

    const { data } = useRequest(queryPatientRegisterTypeInfo, {
        refreshDeps: [],
        pollingInterval: 30000,
        pollingWhenHidden: true
    });

    useEffect(() => {
        chartRef.current = echarts.init(ref.current);

        const option = {
            legend: {
                icon: 'rect',
                //圆点在后
                orient: "horizontal",
                top: "6%",
                itemHeight: 18, //图例图标的高度
                itemWidth: 35, //图例图标的宽度
                itemGap: 25, //图例图标间的间距
                textStyle: {
                    color: '#fff',
                    fontStyle: 12,
                    // fontWeight: '600',
                }
            },
            grid: { //设置图表撑满整个画布
                top: "5%",
                left: "5%",
                right: "5%",
                bottom: "5%",
                containLabel: true
            },
            tooltip: { //悬浮框
                show: true,
                trigger: 'item',
                // formatter: "{a} <br/>{b}: {c} ({d}%)",
                formatter: "{b}: {c}%",
                // position: ['50%', '50%']
            },
            series: [
                {
                    name: '',
                    type: 'pie',
                    radius: [50, 90],
                    center: ['55%', '50%'],
                    // roseType: 'radius',
                    itemStyle: {
                        borderRadius: 5
                    },
                    tooltip: {
                        formatter: "{b} : {c} ({d}%)"
                    },
                    label: { //隐藏引导线
                        normal: {
                            show: true,
                            textStyle: {
                                fontSize: 12,
                                color: '#fff'
                            },
                            formatter: '{b}：{c}({d}%)',
                        },
                        emphasis: {
                            label: {
                                show: false, //隐藏中间文字
                            },
                            textStyle: { //设置文字样式
                                fontSize: '12',
                                color: "#fff"
                            }
                        },
                        labelLine: {
                            normal: {
                                // 统一设置指示线长度
                                length: 0,
                                length2: 0,
                            }
                        },
                    },
                    data: [
                        {
                            value: data?.fzrc,
                            name: '复诊患者',
                            itemStyle: {
                                normal: {
                                    color: "#4083E2",
                                },
                            },
                        },
                        {
                            value: data?.czrc,
                            name: '初诊患者',
                            itemStyle: {
                                normal: {
                                    color: "#86E3CC",
                                },
                            },
                        },
                    ]
                }
            ]
        };
        window.addEventListener('resize', handleResize);

        //  动效
        // function makeRandomData() {
        //     return [
        //         {
        //             value: Math.random(),
        //             name: 'A'
        //         },
        //         {
        //             value: Math.random(),
        //             name: 'B'
        //         }
        //     ];
        // }
        // setInterval(() => {
        //     myChart.setOption({
        //         series: {
        //             data: makeRandomData()
        //         }
        //     });
        // }, 2000);
        chartRef.current.setOption(option);
        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [data]);
    return (
        <div className={styles.main4} id="main4" ref={ref}></div>
    );
});

export default EchartsTest;