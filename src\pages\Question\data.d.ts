declare namespace QUESTION {
  // 问卷列表项
  type QuestionnaireListItem = {
    id?: number;
    title?: string;
    description?: string;
    status?: number; // 0-草稿 1-发布 2-暂停
    createTime?: string;
    updateTime?: string;
    publishTime?: string;
    questionCount?: number;
    answerCount?: number;
    saID?: string;
    hospitalName?: string;
    creator?: string;
  };

  // 问题列表项
  type QuestionListItem = {
    id?: number;
    questionnaireId?: number;
    title?: string;
    type?: number; // 1-单选 2-多选 3-文本
    options?: string[];
    required?: boolean;
    sort?: number;
    createTime?: string;
  };

  // 答卷记录项
  type AnswerRecordItem = {
    id?: number;
    questionnaireId?: number;
    questionnaireName?: string;
    answererName?: string;
    answererPhone?: string;
    deviceCode?: string;
    hospitalName?: string;
    answerTime?: string;
    score?: number;
    status?: number;
  };

  // 答题结果项
  type AnswerResultItem = {
    questionId?: number;
    questionTitle?: string;
    questionType?: number;
    answer?: string;
    options?: string[];
  };

  // API响应类型
  type ApiResponse<T = any> = {
    code: string;
    msg?: string;
    data?: T;
  };

  // 分页参数
  type PageParams = {
    current?: number;
    pageSize?: number;
    [key: string]: any;
  };

  // 分页响应
  type PageResponse<T = any> = {
    data?: T[];
    total?: number;
    success?: boolean;
  };

  // 好评统计
  type GoodValueStats = {
    totalCount?: number;
    goodCount?: number;
    goodRate?: number;
    hospitalName?: string;
    statisticsDate?: string;
  };
}
