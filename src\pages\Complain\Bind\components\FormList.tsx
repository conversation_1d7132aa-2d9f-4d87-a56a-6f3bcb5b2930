import {
  ProFormText,
  ProFormSelect,
  ProFormTextArea,
  ProFormRadio,
} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

export type ListType = {
  value?: any;
  label?: string;
};

const FormList: React.FC = () => {
  const { hosList } = useModel('hospital');

  return (
    <>
      <ProFormText
        name="receiverName"
        label="接收人姓名"
        placeholder="请输入接收人姓名"
        rules={[
          {
            required: true,
            message: '请输入接收人姓名',
          },
        ]}
      />
      
      <ProFormText
        name="receiverEmail"
        label="接收人邮箱"
        placeholder="请输入接收人邮箱"
        rules={[
          {
            required: true,
            message: '请输入接收人邮箱',
          },
          {
            type: 'email',
            message: '请输入正确的邮箱格式',
          },
        ]}
      />
      
      <ProFormText
        name="receiverPhone"
        label="接收人电话"
        placeholder="请输入接收人电话"
        rules={[
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
          },
        ]}
      />
      
      <ProFormSelect
        name="saID"
        label="机构名称"
        placeholder="请选择机构"
        options={hosList.map((c: ListType) => ({
          value: c.value,
          label: c.label,
        }))}
        rules={[
          {
            required: true,
            message: '请选择机构',
          },
        ]}
        showSearch
        optionFilterProp="label"
      />
      
      <ProFormSelect
        name="pushType"
        label="推送类型"
        placeholder="请选择推送类型"
        options={[
          { value: 1, label: '邮件推送' },
          { value: 2, label: '短信推送' },
          { value: 3, label: '微信推送' },
        ]}
        rules={[
          {
            required: true,
            message: '请选择推送类型',
          },
        ]}
      />
      
      <ProFormRadio.Group
        name="status"
        label="状态"
        options={[
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ]}
        initialValue={1}
        rules={[
          {
            required: true,
            message: '请选择状态',
          },
        ]}
      />
      
      <ProFormTextArea
        name="remark"
        label="备注"
        placeholder="请输入备注信息"
        fieldProps={{
          rows: 3,
        }}
      />
    </>
  );
};

export default FormList;
