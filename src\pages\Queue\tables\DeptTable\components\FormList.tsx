import { ProFormText, ProFormSelect, ProFormTextArea} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchDeptList } = useModel('hospital');
  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '医生姓名为必填项',
          },
        ]}
        name="doctorName"
        label="医生姓名"
        placeholder="请输入医生姓名"
      />
      <ProFormText 
        rules={[
          {
            required: true,
            message: '医生代码为必填项',
          },
        ]} 
        name="doctorCode" 
        label="医生代码"
        placeholder="请输入医生代码"
      />
      <ProFormText
        name="doctorNameSpelling"
        label="姓名拼音"
        placeholder="请输入医生姓名拼音"
      />
      <ProFormSelect
        name="deptCode"
        label="医生科室"
        rules={[
          {
            required: true,
            message: '医生科室为必填项',
          },
        ]}
        colProps={{ span: 12 }}
        request={() => fetchDeptList()}
      />
      <ProFormText name="title" label="医生职称" placeholder="请输入医生职称" />
      <ProFormTextArea name="professional" label="医生擅长" placeholder="请输入医生擅长" />
      <ProFormTextArea name="profile" label="医生简介" placeholder="请输入医生简介" />
    </>
  );
};

export default FormList;
