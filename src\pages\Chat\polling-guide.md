# 聊天轮询功能说明

## 功能概述

聊天页面现在支持轮询功能，可以实时获取会话列表和聊天记录，无需手动刷新即可看到最新消息。

## 主要特性

### 1. 双重轮询机制
- **会话列表轮询**：定期获取用户的所有会话，更新未读消息数和最后消息
- **聊天记录轮询**：当选择某个会话时，定期获取该会话的最新消息

### 2. 智能轮询控制
- **可配置间隔**：支持500ms到10秒的轮询间隔设置
- **开关控制**：可以随时启用或禁用轮询功能
- **页面可见性检测**：页面隐藏时自动停止轮询，显示时恢复

### 3. 用户体验优化
- **静默轮询**：轮询时不显示loading状态，避免界面闪烁
- **错误处理**：轮询失败时不弹出错误提示，避免频繁打扰
- **资源管理**：组件卸载时自动清理所有轮询定时器

## 轮询控制界面

### 控制组件位置
轮询控制组件位于会话列表顶部，包含：
- **实时更新开关**：带有旋转图标的Switch组件
- **轮询间隔设置**：InputNumber组件，单位为毫秒

### 控制选项
- **开关状态**：
  - 开启：显示旋转的同步图标
  - 关闭：显示暂停图标
- **间隔设置**：
  - 最小值：500ms
  - 最大值：10000ms (10秒)
  - 步长：500ms
  - 默认值：1000ms (1秒)

## 技术实现

### 1. 轮询状态管理
```typescript
const [pollingInterval, setPollingInterval] = useState(1000); // 轮询间隔
const [isPollingEnabled, setIsPollingEnabled] = useState(true); // 轮询开关
const conversationPollingRef = useRef<NodeJS.Timeout | null>(null); // 会话轮询定时器
const messagePollingRef = useRef<NodeJS.Timeout | null>(null); // 消息轮询定时器
```

### 2. 轮询函数
```typescript
// 会话列表轮询
const startConversationPolling = useCallback(() => {
  if (!isPollingEnabled) return;
  
  conversationPollingRef.current = setInterval(() => {
    loadConversations(false); // 静默加载
  }, pollingInterval);
}, [isPollingEnabled, pollingInterval, loadConversations]);

// 消息轮询
const startMessagePolling = useCallback(() => {
  if (!isPollingEnabled || !selectedConversation) return;
  
  messagePollingRef.current = setInterval(() => {
    loadChatHistory(selectedConversation.conversationId, false); // 静默加载
  }, pollingInterval);
}, [isPollingEnabled, selectedConversation, pollingInterval, loadChatHistory]);
```

### 3. 生命周期管理
```typescript
// 组件加载时开始轮询
useEffect(() => {
  loadConversations();
  startConversationPolling();
  
  return () => {
    stopConversationPolling();
    stopMessagePolling();
  };
}, [loadConversations, startConversationPolling, stopConversationPolling, stopMessagePolling]);

// 页面可见性控制
useEffect(() => {
  const handleVisibilityChange = () => {
    if (document.hidden) {
      stopConversationPolling();
      stopMessagePolling();
    } else {
      if (isPollingEnabled) {
        startConversationPolling();
        if (selectedConversation) {
          startMessagePolling();
        }
      }
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);
  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
}, [isPollingEnabled, selectedConversation, startConversationPolling, startMessagePolling, stopConversationPolling, stopMessagePolling]);
```

## 性能考虑

### 1. 网络请求优化
- **静默请求**：轮询时不显示loading状态
- **错误静默**：轮询失败时不显示错误提示
- **请求去重**：避免同时发起多个相同请求

### 2. 内存管理
- **定时器清理**：组件卸载时清理所有定时器
- **事件监听清理**：页面可见性监听器的正确清理
- **引用管理**：使用useRef管理定时器引用

### 3. 用户体验
- **页面隐藏暂停**：页面不可见时停止轮询，节省资源
- **智能恢复**：页面重新可见时自动恢复轮询
- **配置持久化**：轮询设置在会话期间保持

## 使用建议

### 1. 轮询间隔设置
- **高频场景**：客服、紧急沟通建议500-1000ms
- **一般场景**：日常聊天建议1000-3000ms
- **低频场景**：通知类消息建议3000-5000ms

### 2. 网络环境考虑
- **良好网络**：可以使用较短间隔
- **弱网环境**：建议使用较长间隔，避免请求堆积
- **移动网络**：考虑流量消耗，适当延长间隔

### 3. 服务器压力
- **用户量大**：建议设置最小间隔限制
- **服务器负载高**：可以动态调整轮询间隔
- **API限流**：注意接口调用频率限制

## 故障排除

### 1. 轮询不工作
- 检查轮询开关是否开启
- 检查网络连接是否正常
- 查看浏览器控制台是否有错误

### 2. 消息不更新
- 确认选择了正确的会话
- 检查API接口是否返回最新数据
- 验证消息轮询是否正常启动

### 3. 性能问题
- 适当增加轮询间隔
- 检查是否有内存泄漏
- 监控网络请求频率

## 扩展功能

### 1. WebSocket集成
可以考虑将轮询升级为WebSocket实时推送：
- 更低的延迟
- 更少的服务器压力
- 更好的实时性

### 2. 智能轮询
根据用户活跃度动态调整轮询频率：
- 活跃时高频轮询
- 空闲时低频轮询
- 长时间无操作时停止轮询

### 3. 离线检测
集成网络状态检测：
- 离线时停止轮询
- 重新连接时恢复轮询
- 网络状态提示

## 相关文件

- `src/pages/Chat/index.tsx` - 主要轮询逻辑
- `src/pages/Chat/index.less` - 轮询控制样式
- `src/services/api/chat.ts` - API接口调用
